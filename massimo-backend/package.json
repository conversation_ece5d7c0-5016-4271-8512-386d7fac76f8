{"name": "nestjs-starter", "version": "0.1.0", "description": "NestJS starter repository.", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "start": "NODE_OPTIONS=--max-old-space-size=15360 nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "run-s -c lint:*", "lint:eslint": "eslint '{src,apps,libs,test}/**/*.ts'", "lint:prettier": "prettier --check src test", "format": "run-s format:*", "format:eslint": "eslint --fix '{src,apps,libs,test}/**/*.ts'", "format:prettier": "prettier --write src test", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:generate -f src/typeorm/ormconfig.postgres.ts -n", "migration:show": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:show -f src/typeorm/ormconfig.postgres.ts", "migration:run": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:run -f src/typeorm/ormconfig.postgres.ts", "migration:revert": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:revert -f src/typeorm/ormconfig.postgres.ts", "seed:config": "ts-node -r tsconfig-paths/register ./node_modules/typeorm-seeding/dist/cli.js config -n src/typeorm/ormconfig.postgres.ts", "seed:run": "ts-node -r tsconfig-paths/register ./node_modules/typeorm-seeding/dist/cli.js seed -n src/typeorm/ormconfig.postgres.ts", "schema:log": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js schema:log -f src/typeorm/ormconfig.postgres.ts", "doc": "compodoc -p tsconfig.json -s"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,mjs,jsx,ts,tsx}": "eslint"}, "prettier": "@twihike/prettier-config", "dependencies": {"@faker-js/faker": "^7.6.0", "@godaddy/terminus": "4.9.0", "@liaoliaots/nestjs-redis": "^9.0.3", "@nestjs/axios": "0.0.1", "@nestjs/common": "8.0.6", "@nestjs/core": "8.0.6", "@nestjs/graphql": "9.0.4", "@nestjs/jwt": "8.0.0", "@nestjs/microservices": "8.0.6", "@nestjs/passport": "8.0.1", "@nestjs/platform-express": "8.0.6", "@nestjs/platform-socket.io": "^9.1.6", "@nestjs/schedule": "^2.1.0", "@nestjs/swagger": "5.0.9", "@nestjs/terminus": "8.0.0", "@nestjs/throttler": "^3.1.0", "@nestjs/typeorm": "8.0.2", "@nestjs/websockets": "8.0.6", "@socket.io/redis-adapter": "^7.2.0", "@types/deepmerge": "^2.2.0", "@types/sharp": "^0.31.0", "@types/traverse": "^0.6.32", "apollo-server-express": "3.3.0", "aws-sdk": "^2.1248.0", "bcrypt": "5.0.1", "class-transformer": "0.4.0", "class-validator": "0.13.1", "compression": "1.7.4", "csurf": "^1.11.0", "deepmerge": "^4.2.2", "graphql": "15.5.3", "graphql-tools": "8.2.0", "helmet": "4.6.0", "ioredis": "^5.2.4", "lodash": "^4.17.21", "luxon": "^3.1.0", "nestjs-s3": "^1.0.1", "passport": "0.4.1", "passport-jwt": "4.0.0", "passport-mock-strategy": "2.0.0", "pg": "8.7.1", "pluralize": "8.0.0", "redis": "^4.3.1", "reflect-metadata": "0.1.13", "rxjs": "7.3.0", "sanitize-filename": "^1.6.3", "sharp": "^0.31.2", "socket.io": "^4.5.3", "sqlite3": "^5.1.7", "swagger-ui-express": "4.1.6", "traverse": "^0.6.7", "type-graphql": "1.1.1", "typeorm": "0.2.37", "typeorm-seeding": "^1.6.1"}, "devDependencies": {"@compodoc/compodoc": "1.1.15", "@nestjs/cli": "8.1.1", "@nestjs/schematics": "8.0.3", "@nestjs/testing": "8.0.6", "@twihike/eslint-config": "0.1.17", "@twihike/prettier-config": "0.1.17", "@types/bcrypt": "5.0.0", "@types/cron": "^2.0.0", "@types/express": "4.17.13", "@types/jest": "27.0.1", "@types/lodash": "^4.14.186", "@types/luxon": "^3.1.0", "@types/multer": "^1.4.7", "@types/node": "16.9.2", "@types/passport-jwt": "3.0.6", "@types/pluralize": "0.0.29", "@types/supertest": "2.0.11", "@typescript-eslint/eslint-plugin": "4.31.1", "eslint": "7.32.0", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.24.2", "eslint-plugin-jest": "24.4.2", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.1.0", "eslint-plugin-react": "7.25.2", "eslint-plugin-react-hooks": "4.2.0", "eslint-plugin-unicorn": "36.0.0", "husky": "4.3.8", "jest": "27.2.0", "lint-staged": "11.1.2", "npm-run-all": "^4.1.5", "prettier": "2.4.1", "rimraf": "3.0.2", "supertest": "6.1.6", "ts-jest": "27.0.5", "ts-loader": "9.2.5", "ts-node": "10.2.1", "tsconfig-paths": "3.11.0", "typescript": "4.4.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "coverageDirectory": "../coverage", "testEnvironment": "node"}}