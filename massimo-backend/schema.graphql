# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type User {
  id: ID!
  name: String!
  surname: String!
  email: String!
  status: String!
  phone: String!
  isCustomer: Boolean!
  isValid: Boolean!
  isBanned: Boolean!
  version: Int!
}

type SignInResult {
  id: ID!
  name: String!
  surname: String!
  email: String!
  status: String!
  phone: String!
  isCustomer: Boolean!
  isValid: Boolean!
  isBanned: Boolean!
  version: Int!
  token: String!
}

type Query {
  users: [User!]!
  user(name: String!): User!
}

type Mutation {
  signUp(input: SignUpInput!): User!
  signIn(input: SignInInput!): SignInResult!
}

input SignUpInput {
  name: String!
  email: String!
  password: String!
}

input SignInInput {
  email: String!
  password: String!
}
