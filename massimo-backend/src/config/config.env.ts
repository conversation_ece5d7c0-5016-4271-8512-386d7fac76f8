import { IsBoolean, IsIn, IsN<PERSON>ber, IsString } from 'class-validator';

type NODE_ENV = 'development' | 'production' | 'test';
type TYPEORM_TYPE = 'auto' | 'sqlite' | 'postgres';

export class EnvConfig {
  @IsIn(['development', 'production', 'test'])
  NODE_ENV: NODE_ENV;

  @IsNumber()
  PORT: number;

  @IsIn(['auto', 'sqlite', 'postgres'])
  TYPEORM_TYPE: TYPEORM_TYPE;

  @IsString()
  TYPEORM_HOST: string;

  @IsString()
  TYPEORM_USERNAME: string;

  @IsString()
  TYPEORM_PASSWORD: string;

  @IsString()
  TYPEORM_DATABASE: string;

  @IsNumber()
  TYPEORM_PORT: number;

  @IsBoolean()
  TYPEORM_LOGGING: boolean;

  @IsNumber()
  HEALTH_CHECK_DATABASE_TIMEOUT_MS: number;

  @IsString()
  JWT_SECRET: string;

  @IsNumber()
  JWT_EXPIRES_IN: number;

  @IsBoolean()
  SKIP_AUTH: boolean;

  @IsBoolean()
  SWAGGER_UI: boolean;

  @IsString()
  REDIS_HOST: string;

  @IsString()
  REDIS_USERNAME: string;

  @IsString()
  REDIS_PASSWORD: string;

  @IsNumber()
  REDIS_PORT: number;

  @IsString()
  S3_ACCESS_KEY_ID: string;

  @IsString()
  S3_SECRET_ACCESS_KEY: string;

  @IsString()
  S3_ENDPOINT: string;

  @IsBoolean()
  S3_BUCKET_ENDPOINT: boolean;

  @IsBoolean()
  S3_FORCE_PATH_STYLE: boolean;

  static getDefaultObject(): EnvConfig {
    const obj = new EnvConfig();
    obj.NODE_ENV = 'development';
    obj.PORT = 3000;
    obj.TYPEORM_TYPE = 'postgres';
    obj.TYPEORM_HOST = '127.0.0.1';
    obj.TYPEORM_USERNAME = 'admin';
    obj.TYPEORM_PASSWORD = 'QIJRSpTOUksIB1HN';
    obj.TYPEORM_DATABASE = 'defaultdb';
    obj.TYPEORM_PORT = 5432;
    obj.TYPEORM_LOGGING = false;
    obj.HEALTH_CHECK_DATABASE_TIMEOUT_MS = 3000;
    obj.JWT_SECRET =
      'qMjezZCJDRmx6WaGWkU8szi5REnC47EwdLzLmpiVB6qp9hA01CYHg5eNbgzYZQ9UYEYdCKGre54LqRQf5EJZT53gXrVht3P8va1A';
    obj.JWT_EXPIRES_IN = 86_400;
    obj.SKIP_AUTH = false;
    obj.SWAGGER_UI = false;
    obj.REDIS_HOST = '127.0.0.1';
    obj.REDIS_USERNAME = 'default';
    obj.REDIS_PASSWORD = 'eOX6PpA7GY';
    obj.REDIS_PORT = 6379;
    obj.S3_ACCESS_KEY_ID = 'DO00767TPBB8WKLZERPG';
    obj.S3_SECRET_ACCESS_KEY = 'oDr3DyyLs1UOE2w8/veifEWv3HaUbjgjqf47mnUPww8';
    obj.S3_ENDPOINT = 'https://fra1.digitaloceanspaces.com/';
    obj.S3_BUCKET_ENDPOINT = false;
    obj.S3_FORCE_PATH_STYLE = true;

    return obj;
  }
}
