import _ = require('lodash');

const excludeDefaults = ['password', 'generateSearch'];

export const secureResponse = <T>(
  rawData: T,
  exclude: string[] = excludeDefaults,
): T => {
  const loop = (data: unknown) => {
    if (_.isArray(data)) {
      return _.map(data, (item) => {
        if (item.createdAt) {
          // eslint-disable-next-line no-param-reassign
          item.createdAt = +item.createdAt;
        }

        return loop(item);
      });
    }

    if (_.isDate(data)) {
      return data.toUTCString();
    }

    if (_.isObject(data)) {
      return _.omit(
        // eslint-disable-next-line unicorn/prefer-object-from-entries
        _.fromPairs(
          _.map(_.entries(data), ([key, value]) => [key, loop(value)]),
        ),
        exclude,
      );
    }

    return data;
  };

  return loop(rawData);
};
