// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable no-plusplus */
import _ = require('lodash');

export const updateList = {
  defaultRoleId: ['roles', 'defaultRole'],
  departmentId: ['departments', 'department'],
  userIds: ['users', 'users'],
  personIds: ['users', 'persons'],
  customerId: ['customers', 'customer'],
  approverId: ['users', 'approver'],
  projectId: ['projects', 'project'],
  requestId: ['requests', 'request'],
  offerIds: ['offers', 'offers'],
  connectionIds: ['connections', 'connections'],
  targetIds: ["users", "targets"],
  departmentIds: ['departments', 'departments'],
  allowedUserIds: ['users', 'allowedUsers'],
  restrictedUserIds: ['users', 'restrictedUsers'],
  assigneeIds: ['users', 'assignees'],
  contributorIds: ['users', 'contributors'],
  offerId: ['offers', 'offer'],
  organizationId: ['customers', 'organization'],
  personId: ['users', 'person'],
  ownerId: ['users', 'owner'],
  taskId: ['tasks', 'task'],
  chatId: ['chats', 'chat'],
};

export const meanings = {
  users: 'persons',
  persons: 'users',
  customers: 'organizations',
  organizations: 'customers',
};

function cartesian(...args) {
  const r = [];
  const max = args.length - 1;
  function helper(arr, i) {
    for (let j = 0, l = args[i].length; j < l; j++) {
      const a = [...arr]; // clone arr
      a.push(args[i][j]);
      if (i === max) r.push(a);
      else helper(a, i + 1);
    }
  }
  helper([], 0);
  return r;
}

export const alternateMeaing = (values: string[]): string[] => {
  const data: string[] = [];

  values.forEach((value) => {
    const splitted = value.split('.');

    data.push(
      ...cartesian(
        ...splitted.map((item) => {
          if (meanings[item]) {
            return [meanings[item], item];
          }
          return [item];
        }),
      ).map((x) => x.join('.')),
    );
  });

  return data;
};
