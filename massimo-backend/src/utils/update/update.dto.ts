import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsObject, IsOptional, ValidateNested } from 'class-validator';

import { UpdateTemplateOptions } from './options.dto';

export class UpdateTemplate {
  @ApiProperty()
  @Type(() => UpdateTemplateOptions)
  @IsObject()
  @IsOptional()
  @ValidateNested({ each: true })
  readonly changes: UpdateTemplateOptions;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  readonly order: string[];

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  readonly isDelete?: boolean;
}
