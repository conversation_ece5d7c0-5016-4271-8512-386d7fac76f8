import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsOptional, ValidateNested } from 'class-validator';

import { UpdateInputData as ChatData } from '@/base/chats/dto/update-input/data.dto';
import { UpdateInputData as ConnectionData } from '@/base/connections/dto/update-input/data.dto';
import { UpdateInputData as CustomerData } from '@/base/customers/dto/update-input/data.dto';
import { UpdateInputData as DepartmentData } from '@/base/departments/dto/update-input/data.dto';
import { UpdateInputData as MessageData } from '@/base/messages/dto/update-input/data.dto';
import { UpdateInputData as RoleData } from '@/base/roles/dto/update-input/data.dto';
import { UpdateInputData as UserData } from '@/base/users/dto/update-input/data.dto';
import { UpdateInputData as OfferData } from '@/modules/crm/offers/dto/update-input/data.dto';
import { UpdateInputData as ProjectData } from '@/modules/crm/projects/dto/update-input/data.dto';
import { UpdateInputData as RequestData } from '@/modules/crm/requests/dto/update-input/data.dto';

export class UpdateTemplateOptions {
  @ApiProperty()
  @IsOptional()
  @Type(() => UserData)
  @ValidateNested({ each: true })
  users: UserData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => RoleData)
  @ValidateNested({ each: true })
  roles: RoleData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => DepartmentData)
  @ValidateNested({ each: true })
  departments: DepartmentData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => CustomerData)
  @ValidateNested({ each: true })
  customers: CustomerData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => RequestData)
  @ValidateNested({ each: true })
  requests: RequestData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => ProjectData)
  @ValidateNested({ each: true })
  projects: ProjectData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => OfferData)
  @ValidateNested({ each: true })
  offers: OfferData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => ChatData)
  @ValidateNested({ each: true })
  chats: ChatData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => MessageData)
  @ValidateNested({ each: true })
  messages: MessageData[];

  @ApiProperty()
  @IsOptional()
  @Type(() => ConnectionData)
  @ValidateNested({ each: true })
  connections: ConnectionData[];
}
