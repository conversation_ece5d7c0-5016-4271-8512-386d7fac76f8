import _ = require('lodash');

const makeSearch = (fields: string[]) => (data: any) => {
  // eslint-disable-next-line no-param-reassign
  data.search = _.join(
    _.filter(
      _.values(_.pick(data, fields)),
      (d) => _.isString(d) || _.isNumber(d),
    ),
    ' ',
  );

  return data;
};

const targets = {
  users: makeSearch(['name', 'surname', 'email', 'phone']),
  files: makeSearch(['file']),
  departments: makeSearch(['name', 'label', 'description']),
  customers: makeSearch([
    'shartName',
    'fullName',
    'location',
    'phone',
    'email',
    'taxDepartment',
    'taxNumber',
    'address',
  ]),
  tasks: makeSearch(['title', 'description']),
  requests: makeSearch(['name', 'description', 'budget']),
  projects: makeSearch(['id']),
  offers: makeSearch(['name', 'description', 'budget', 'cancellationReason']),
};

export const expandData = (target: string, dataList: any): any => {
  if (target && targets[target as keyof typeof targets]) {
    return _.map(dataList, targets[target as keyof typeof targets]);
  }

  return dataList;
};
