import _ = require('lodash');
import { SelectQueryBuilder } from 'typeorm';

export interface AutoResolveOptions {
  skip?: number;
  take?: number;
  desc?: boolean;
}

export const autoResolve = <T>(
  namespace: string,
  builder: SelectQueryBuilder<T>,
  relations: string[],
  options: AutoResolveOptions = {},
): SelectQueryBuilder<T> => {
  _.each(relations, (relation) => {
    if (relation) {
      const last = _.last(relation.split('.')).split(':');
      const relationData = [...relation.split('.').slice(0, -1), last[0]].join(
        '.',
      );

      builder.leftJoinAndSelect(relationData, last[1] ? last[1] : last[0]);
    }
  });

  if (options.desc) {
    builder.orderBy(`${namespace}.id`, 'DESC');
  }

  if (options.skip) {
    builder.skip(options.skip);
  }

  if (options.take) {
    builder.take(options.take);
  }

  return builder;
};
