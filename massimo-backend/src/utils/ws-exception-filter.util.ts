import { ArgumentsHost, Catch, HttpException } from '@nestjs/common';
import { WsException } from '@nestjs/websockets';
import { Socket } from 'socket.io';

@Catch(WsException, HttpException)
export class WsExceptionFilter {
  public catch(exception: HttpException, host: ArgumentsHost) {
    const client = host.switchToWs().getClient();
    this.handleError(client, exception);
  }

  // eslint-disable-next-line class-methods-use-this
  public handleError(client: Socket, exception: HttpException | WsException) {
    // eslint-disable-next-line unicorn/prefer-ternary
    if (exception instanceof HttpException) {
      throw new WsException(exception.message);
    } else {
      throw exception;
      // handle websocket exception
    }
  }
}
