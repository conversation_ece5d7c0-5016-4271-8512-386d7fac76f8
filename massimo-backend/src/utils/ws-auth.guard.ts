import { ExecutionContext, Injectable } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class SuperAuthGuard extends AuthGuard('jwt') {
  // eslint-disable-next-line class-methods-use-this
  getRequest(context: ExecutionContext) {
    const req = context.switchToHttp().getRequest();

    req.headers = {
      ...req.headers,
      authorization: `Bearer ${req.handshake.auth.token}`,
    };

    return req;
  }
}
