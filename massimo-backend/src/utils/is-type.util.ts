import {
  ValidationArguments,
  ValidationOptions,
  Validator,
  registerDecorator,
} from 'class-validator';
import _ = require('lodash');

const typeValidator = {
  number(value: unknown) {
    return _.isNumber(value);
  },
  numberArray(value: unknown[]) {
    return _.isArray(value) && _.every(value, (x) => _.isNumber(x));
  },
  string(value: unknown) {
    return _.isString(value);
  }
  // Add more here
};

export function IsType(
  types: (keyof typeof typeValidator)[],
  validationOptions?: ValidationOptions,
) {
  // eslint-disable-next-line func-names, @typescript-eslint/explicit-module-boundary-types
  return function (object: unknown, propertyName: string) {
    registerDecorator({
      name: 'wrongType',
      target: object.constructor,
      propertyName,
      options: validationOptions,
      validator: {
        validate(value: unknown[]) {
          return types.some((v) => typeValidator[v](value));
        },
        defaultMessage() {
          const lastType = types.pop();
          if (types.length === 0) return `Has to be ${lastType}`;
          return `Can only be ${types.join(', ')} or ${lastType}.`;
        },
      },
    });
  };
}
