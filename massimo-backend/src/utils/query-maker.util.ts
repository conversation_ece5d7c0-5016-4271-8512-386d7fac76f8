import _ = require('lodash');
import traverse = require('traverse');
import { ILike, In } from 'typeorm';

const searchMaker = (data: string) => {
  if (_.isString(data) && _.startsWith(data, '%') && _.endsWith(data, '%')) {
    return ILike(data);
  }
  return data;
};

export const queryMaker = <T>(rawData: T): T => {
  if (!_.isObjectLike(rawData)) {
    return rawData;
  }

  const data = _.cloneDeep(rawData);

  return _.reduce(
    data as any,
    ((acc, value, key) => {
      if (_.isArray(value) && _.every(value, (x) => _.isString(x))) {
        acc[key] = In(_.map(value, (x) => searchMaker(x)));
      } else if (_.isArray(value)) {
        acc[key] = In(value);
      } else if (_.isObject(value)) {
        if (_.isPlainObject(value)) {
          acc[key] = queryMaker(value);
        } else {
          acc[key] = value;
        }
      } else {
        acc[key] = searchMaker(value);
      }

      return acc;
    }) as any,
    _.isArray(data) ? [] : {},
  ) as any;
};
