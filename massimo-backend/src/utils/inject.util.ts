// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/no-invalid-this */
// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable arrow-body-style */

import _ = require('lodash');
import traverse = require('traverse');

const addressMaker = (data: string) => {
  if(!_.isString(data)) {
    return null;
  }

  return data
    .split('.')
    .map((x) => {
      const parsed = Number.parseInt(x, 10);

      if (!_.isNaN(parsed)) {
        return `${Math.abs(parsed) - 1}`;
      }

      return x;
    })
    .join('.');
};

export const injectData = (target: unknown, data: unknown) => {
  const res = _.cloneDeep(target);

  traverse(res).forEach(function (v) {
    if (_.isArray(v) && _.some(v, (x) => _.startsWith(x, '$'))) {
      this.update(
        _.reduce(
          v,
          (acc, value) => {
            if (_.isString(value) && _.startsWith(value, '$')) {
              const realValue = _.get(
                data,
                addressMaker(
                  _.startsWith(value, '$.') ? value.slice(1) : value,
                ),
              );

              if (realValue) {
                acc.push(realValue);
              } else {
                acc.push(value);
              }
            } else {
              acc.push(value);
            }

            return acc;
          },
          [],
        ),
      );
    } else if (
      _.isObject(v) &&
      _.some(_.keys(v), (x) => _.startsWith(x, '$'))
    ) {
      this.update(
        _.reduce(
          v,
          (acc, value, key) => {
            if (_.isString(key) && _.startsWith(key, '$')) {
              const realKey = _.get(
                data,
                addressMaker(_.startsWith(key, '$.') ? key : key.slice(1)),
              );

              if (realKey) {
                acc[realKey] = value;
              } else {
                acc[key] = value;
              }
            } else {
              acc[key] = value;
            }

            return acc;
          },
          {},
        ),
      );
    } else if (
      _.isObject(v) &&
      _.some(_.values(v), (x) => _.startsWith(x, '$'))
    ) {
      this.update(
        _.reduce(
          v,
          (acc, value: any, key) => {
            if (_.isString(value) && _.startsWith(value, '$')) {
              const realValue = _.get(
                data,
                addressMaker(
                  _.startsWith(value, '$.') ? value : value.slice(1),
                ),
              );

              if (realValue) {
                acc[key] = realValue;
              } else {
                acc[key] = value;
              }
            } else {
              acc[key] = value;
            }

            return acc;
          },
          {},
        ),
      );
    } else if (_.isString(v) && _.startsWith(v, '$')) {
      const address = addressMaker(v);

      if (_.startsWith(v, '$.')) {
        const realData = _.get(data, address);

        if (realData) {
          this.update(realData);
        }
      } else {
        const realData = _.get(data, address.slice(1));

        if (realData) {
          this.update(realData);
        }
      }
    }
  });

  return res;
};
