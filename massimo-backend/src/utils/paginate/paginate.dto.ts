import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsBoolean,
  IsNumber,
  IsOptional,
  Min,
  ValidateNested,
} from 'class-validator';

import { PaginateTemplateOptions } from './options.dto';

export class PaginateTemplate {
  @ApiProperty()
  @Type(() => PaginateTemplateOptions)
  @ValidateNested({ each: true })
  readonly options: PaginateTemplateOptions;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Min(0)
  readonly page: number;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  @Min(0)
  readonly limit: number;

  @ApiProperty()
  @IsBoolean()
  @IsOptional()
  readonly desc: boolean;
}
