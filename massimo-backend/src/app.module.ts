import { RedisModule } from '@liaoliaots/nestjs-redis';
import { Module } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AccessesModule } from './base/access/access.module';
import { AnalyticsModule } from './base/analytics/analytics.module';
import { AuthModule } from './base/auth/auth.module';
import { ChatsModule } from './base/chats/chats.module';
import { ConnectionsModule } from './base/connections/connections.module';
import { CustomersModule } from './base/customers/customers.module';
import { FilesModule } from './base/files/files.module';
import { MessagesModule } from './base/messages/messages.module';
import { NotificationsModule } from './base/notification/notification.module';
import { PostsModule } from './base/posts/posts.module';
import { RolesModule } from './base/roles/roles.module';
import { UsersModule } from './base/users/users.module';
import { ConfigModule } from './config/config.module';
import { EventsModule } from './events/events.module';
import { HealthModule } from './health/health.module';
import { OffersModule } from './modules/crm/offers/offers.module';
import { ProjectsModule } from './modules/crm/projects/projects.module';
import { RequestsModule } from './modules/crm/requests/requests.module';
import { TasksModule } from './modules/crm/tasks/tasks.module';
import options = require('./redis/config.redis');
import { TypeOrmOptionsService } from './typeorm/typeorm-options.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    RedisModule.forRoot({
      readyLog: true,
      config: options,
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useClass: TypeOrmOptionsService,
    }),
    AuthModule,
    ChatsModule,
    UsersModule,
    TasksModule,
    FilesModule,
    RolesModule,
    EventsModule,
    HealthModule,
    OffersModule,
    MessagesModule,
    RequestsModule,
    ProjectsModule,
    AccessesModule,
    AnalyticsModule,
    CustomersModule,
    ConnectionsModule,
    NotificationsModule,
    PostsModule
  ],
})
export class AppModule {}
