import { Module, forwardRef } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CommonsService } from './common.service';

import { Access } from '@/base/access/access.entity';
import { AuthModule } from '@/base/auth/auth.module';
import { Chat } from '@/base/chats/chats.entity';
import { Connection } from '@/base/connections/connections.entity';
import { Customer } from '@/base/customers/customers.entity';
import { Department } from '@/base/departments/departments.entity';
import { File } from '@/base/files/files.entity';
import { Message } from '@/base/messages/messages.entity';
import { Notification } from '@/base/notification/notification.entity';
import { Comment } from '@/base/posts/comments.entity';
import { Postmeta } from '@/base/posts/postmeta.entity';
import { Post } from '@/base/posts/posts.entity';
import { Role } from '@/base/roles/roles.entity';
import { User } from '@/base/users/users.entity';
import { EventsModule } from '@/events/events.module';
import { Offer } from '@/modules/crm/offers/offers.entity';
import { Project } from '@/modules/crm/projects/projects.entity';
import { Request } from '@/modules/crm/requests/requests.entity';
import { Task } from '@/modules/crm/tasks/tasks.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      Chat,
      Message,
      Role,
      Access,
      Customer,
      Department,
      Offer,
      Project,
      Request,
      Task,
      File,
      Connection,
      Notification,
      Post,
      Comment,
      Postmeta,
    ]),
    forwardRef(() => AuthModule)
  ],
  controllers: [],
  providers: [CommonsService],
  exports: [CommonsService],
})
export class CommonsModule {}
