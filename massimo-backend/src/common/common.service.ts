/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable dot-notation */
/* eslint-disable no-param-reassign */
/* eslint-disable unicorn/prefer-array-flat */
import { Injectable, NotAcceptableException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { WebSocketGateway, WebSocketServer } from '@nestjs/websockets';
import _ = require('lodash');
import { Server } from 'socket.io';
import traverse = require('traverse');
import {
  DeepPartial,
  DeleteResult,
  FindConditions,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';

import { Access } from '@/base/access/access.entity';
import { Chat } from '@/base/chats/chats.entity';
import { Connection } from '@/base/connections/connections.entity';
import { Customer } from '@/base/customers/customers.entity';
import { Department } from '@/base/departments/departments.entity';
import { File } from '@/base/files/files.entity';
import { Message } from '@/base/messages/messages.entity';
import { Notification } from '@/base/notification/notification.entity';
import { Comment } from '@/base/posts/comments.entity';
import { Postmeta } from '@/base/posts/postmeta.entity';
import { Post } from '@/base/posts/posts.entity';
import { Role } from '@/base/roles/roles.entity';
import { User } from '@/base/users/users.entity';
import { Offer } from '@/modules/crm/offers/offers.entity';
import { Project } from '@/modules/crm/projects/projects.entity';
import { Request } from '@/modules/crm/requests/requests.entity';
import { Task } from '@/modules/crm/tasks/tasks.entity';
import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';
import { expandData } from '@/utils/expand-data.util';
import { injectData } from '@/utils/inject.util';
import { PaginateTemplateOptions } from '@/utils/paginate/options.dto';
import { queryMaker } from '@/utils/query-maker.util';
import { alternateMeaing, updateList } from '@/utils/update-policy.util';
import { UpdateTemplate } from '@/utils/update/update.dto';
import { WsResponse } from '@/utils/ws-response.util';

export interface ReturnTypeList {
  users: User;
  roles: Role;
  accesss: Access;
  customers: Customer;
  departments: Department;
  offers: Offer;
  projects: Project;
  requests: Request;
  tasks: Task;
  files: File;
  chats: Chat;
  messages: Message;
  connections: Connection;
  notifications: Notification;
  posts: Post;
  comments: Comment;
  postmetas: Postmeta;
}

export interface PaginateResponse<T> {
  data: T[];
  relations?: {
    users?: User[];
    roles?: Role[];
    accesss?: Access[];
    customers?: Customer[];
    departments?: Department[];
    offers?: Offer[];
    projects?: Project[];
    requests?: Request[];
    tasks?: Task[];
    chats?: Chat[];
    messages?: Message[];
    files?: File[];
    connections?: Connection[];
    notifications?: Notification[];
    posts?: Post[];
    comments?: Comment[];
    postmetas?: Postmeta[];
  };
}

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
@Injectable()
export class CommonsService {
  @WebSocketServer()
  server: Server;

  constructor(
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
    @InjectRepository(Role)
    private readonly rolesRepo: Repository<Role>,
    @InjectRepository(Access)
    private readonly accesssRepo: Repository<Access>,
    @InjectRepository(Customer)
    private readonly customersRepo: Repository<Customer>,
    @InjectRepository(Department)
    private readonly departmentsRepo: Repository<Department>,
    @InjectRepository(Offer)
    private readonly offersRepo: Repository<Offer>,
    @InjectRepository(Project)
    private readonly projectsRepo: Repository<Project>,
    @InjectRepository(Request)
    private readonly requestsRepo: Repository<Request>,
    @InjectRepository(Task)
    private readonly tasksRepo: Repository<Task>,
    @InjectRepository(File)
    private readonly filesRepo: Repository<File>,
    @InjectRepository(Message)
    private readonly messagesRepo: Repository<Message>,
    @InjectRepository(Chat)
    private readonly chatsRepo: Repository<Chat>,
    @InjectRepository(Connection)
    private readonly connectionsRepo: Repository<Connection>,
    @InjectRepository(Notification)
    private readonly notificationsRepo: Repository<Notification>,
    @InjectRepository(Post)
    private readonly postsRepo: Repository<Post>,
    @InjectRepository(Comment)
    private readonly commentsRepo: Repository<Comment>,
    @InjectRepository(Postmeta)
    private readonly postmetasRepo: Repository<Postmeta>,
  ) {}

  async delete<T extends keyof ReturnTypeList>(
    target: T,
    rawQuery: FindConditions<ReturnTypeList[T] | ReturnTypeList[T][]> = {},
  ): Promise<DeleteResult> {
    const query = queryMaker(rawQuery);

    const targetRepo = this[
      `${target}Repo` as keyof this
    ] as unknown as Repository<ReturnTypeList[T]>;
    const qb = targetRepo.createQueryBuilder(target);

    const data = await qb.where(query).delete().execute();

    // this.event(target, null, {
    //   id: data.,
    // });

    return data;
  }

  async count<T extends keyof ReturnTypeList>(
    target: T,
    rawQuery: FindConditions<ReturnTypeList[T] | ReturnTypeList[T][]> = {},
  ): Promise<number> {
    const query = queryMaker(rawQuery);

    const targetRepo = this[
      `${target}Repo` as keyof this
    ] as unknown as Repository<ReturnTypeList[T]>;

    const data: number = await targetRepo.count({
      where: query,
    });

    return data;
  }

  async findAll<T extends keyof ReturnTypeList>(
    target: T,
    rawQuery: FindConditions<ReturnTypeList[T] | ReturnTypeList[T][]> = {},
    relations: string[] | true = [],
    options: AutoResolveOptions = {},
    lookFor: any = [],
  ): Promise<ReturnTypeList[T][]> {
    let data: ReturnTypeList[T][];
    const query = queryMaker(rawQuery);

    const targetRepo = this[
      `${target}Repo` as keyof this
    ] as unknown as Repository<ReturnTypeList[T]>;

    if (_.isBoolean(relations) || relations.length > 0) {
      const builder = autoResolve(
        target,
        targetRepo.createQueryBuilder(target),
        _.isBoolean(relations) ? [] : relations,
        options,
      );

      let qb = builder;

      if (_.isArray(lookFor)) {
        if (_.isNull(lookFor[1])) {
          qb = qb.where(lookFor[2]).andWhere(query);
        } else if (_.isString(lookFor[0])) {
          qb = qb
            .where(`${lookFor[0]}_id = :id`, { id: lookFor[1] })
            .andWhere(query);
        } else {
          qb = qb.where(query);
        }
      } else {
        qb = qb.where(query);
      }

      if (options.take) {
        qb.take(options.take);
      }

      if (options.skip) {
        qb.skip(options.skip);
      }

      data = await qb.getMany();
    } else {
      data = await targetRepo.find({
        where: (qb: SelectQueryBuilder<ReturnTypeList[T]>) => {
          if (_.isArray(lookFor)) {
            if (_.isNull(lookFor[1])) {
              qb.where(lookFor[2]).andWhere(query);
            } else if (_.isString(lookFor[0])) {
              qb.where(`${lookFor[0]}_id = :id`, { id: lookFor[1] }).andWhere(
                query,
              );
            } else {
              qb.where(query);
            }
          } else {
            qb.where(query);
          }
        },
        ..._.omit(options, 'desc'),
        order: {
          id: options.desc ? 'DESC' : 'ASC',
        } as any,
      });
    }

    return data;
  }

  async findOne<T extends keyof ReturnTypeList>(
    target: T,
    rawQuery: FindConditions<ReturnTypeList[T]>,
    relations: string[] = [],
  ): Promise<ReturnTypeList[T]> {
    let data: ReturnTypeList[T];
    const query = queryMaker(rawQuery);

    const targetRepo = this[
      `${target}Repo` as keyof this
    ] as unknown as Repository<ReturnTypeList[T]>;

    if (relations.length > 0) {
      const builder = autoResolve(
        target,
        targetRepo.createQueryBuilder(target),
        relations,
      );

      data = await builder.where(query).getOne();
    } else {
      data = await targetRepo.findOne(query);
    }

    return data;
  }

  // eslint-disable-next-line class-methods-use-this
  async updatePolicies<
    T extends keyof ReturnTypeList,
    U extends DeepPartial<ReturnTypeList[T]>[],
  >(target: T, dataList: U): Promise<U> {
    const result = _.cloneDeep(dataList);
    const targets = _.reduce(
      updateList,
      (acc, item, key) => {
        acc[key] = [];

        return acc;
      },
      {},
    ) as Record<keyof typeof updateList, number[]>;

    _.forEach(dataList, (data) => {
      _.forEach(_.entries(data), ([key, value]) => {
        if (_.has(targets, key)) {
          if (_.isArray(value)) {
            targets[key].push(...value);
          } else {
            targets[key].push(value);
          }
        }
      });
    });

    // eslint-disable-next-line no-restricted-syntax

    await Promise.all(
      _.map(targets, async (targetValue, targetKey) => {
        let datas: any[] = [];

        // eslint-disable-next-line unicorn/prefer-ternary
        if (
          (_.isArray(targetValue) && targetValue.length > 0) ||
          !_.isArray(targetValue)
        ) {
          datas = await this.findAll(updateList[targetKey][0], {
            id: targetValue as any,
          });
        } else {
          datas = [];
        }

        _.map(dataList, (item, i) => {
          if (_.isArray(item[targetKey])) {
            result[i][updateList[targetKey][1]] = item[targetKey].map((x) =>
              datas.find((y) => x === y.id),
            );
          } else if (_.isNumber(item[targetKey])) {
            result[i][updateList[targetKey][1]] = datas.find(
              (y) => y.id === item[targetKey],
            );
          }
        });
      }),
    );

    return result;
  }

  async update<T extends keyof ReturnTypeList>(
    target: T,
    dataList: DeepPartial<ReturnTypeList[T]>[],
    changes: Partial<UpdateTemplate['changes']> = {},
    order: UpdateTemplate['order'] = ['$'],
    isInjected: boolean | Record<any, any> = false,
  ): Promise<ReturnTypeList[T][]> {
    const targetRepo = this[
      `${target}Repo` as keyof this
    ] as unknown as Repository<ReturnTypeList[T]>;

    let response: ReturnTypeList[T][];
    const injectionData = {};

    // eslint-disable-next-line no-restricted-syntax
    for await (const o of order) {
      if (o === '$') {
        if (!isInjected) {
          const populated = await this.updatePolicies(
            target,
            injectData(dataList, injectionData) as any,
          );

          response = await targetRepo.save(
            expandData(target, populated as any),
          );

          this.event(target, null, {
            id: response.map((x) => x.id),
          });

          injectionData['$'] = _.cloneDeep(response);
        } else {
          const populated =
            order[0] === '$'
              ? await this.updatePolicies(
                  target,
                  injectData(
                    dataList,
                    _.isBoolean(isInjected) ? {} : isInjected,
                  ) as any,
                )
              : injectData(dataList, _.isBoolean(isInjected) ? {} : isInjected);

          response = await targetRepo.save(
            expandData(target, populated as any),
          );
        }
      } else if (
        o !== target &&
        changes[o] &&
        changes[o].length > 0 &&
        _.every(
          changes[o],
          (change) => _.isObject(change) && !_.isEmpty(change),
        )
      ) {
        const subData = await this.update(
          o as keyof ReturnTypeList,
          changes[o],
          undefined,
          undefined,
          injectionData,
        );

        if (!isInjected) {
          injectionData[o] = subData;
        }
      }
    }

    return response;
  }

  // eslint-disable-next-line class-methods-use-this
  relationArranger(
    target: string,
    relationList: PaginateTemplateOptions['relations'],
  ): PaginateTemplateOptions['relations'] {
    const relationValues: string[] = _.flatten(_.values(relationList));
    const result: PaginateTemplateOptions['relations'] = _.reduce(
      relationList,
      (acc, relation, key) => {
        if (_.isUndefined(acc[key])) {
          acc[key] = [];
        }

        const handle = (x: string) => {
          if (_.startsWith(x, `${target}.`)) {
            acc[key].push(x);
          } else {
            // eslint-disable-next-line consistent-return
            const loop = (splitted: string[], isLoop = false) => {
              const tail = splitted[0];

              const head = _.find(relationValues, (y) =>
                _.endsWith(y, `.${tail}`),
              );

              if (!head) {
                return false;
              }

              const splittedHead = head.split('.');
              const looping = loop(splittedHead, true);
              if (!looping && !isLoop) {
                acc[key].push(
                  [...splittedHead, ...splitted.slice(1)].join('.'),
                );
              } else if (looping && !isLoop) {
                acc[key].push(
                  [
                    ...looping.slice(0, -1),
                    ...splittedHead,
                    ...splitted.slice(1),
                  ].join('.'),
                );
              } else {
                return splittedHead;
              }
            };
            loop(x.split('.'));
          }
        };

        if (_.isString(relation)) {
          handle(relation);
        } else {
          relation.forEach((value) => {
            handle(value);
          });
        }

        return acc;
      },
      {},
    );

    return result;
  }

  // eslint-disable-next-line class-methods-use-this
  relationMapper(
    data: unknown[],
    arrangedRelations: PaginateTemplateOptions['relations'],
  ) {
    let err = false;
    const relationList = _.flatten(_.values(arrangedRelations)).map((x) =>
      _.first(_.split(_.last(_.split(x, '.')), ':')),
    );
    const relationEntries = _.entries(arrangedRelations);

    const result: Record<string, any[]> = _.reduce(
      arrangedRelations,
      (acc, value, key) => {
        acc[key] = [];

        return acc;
      },
      {},
    );

    traverse(_.cloneDeep(data)).forEach((v) => {
      if (_.isObject(v)) {
        const entries = _.entries(v);
        const matches = _.filter(entries, (value) =>
          _.includes(relationList, value[0]),
        );
        if (matches.length > 0) {
          matches
            .map((x) => [`${_.toLower(v.constructor.name)}s.${x[0]}`, x[1]])
            .forEach(([name, value]) => {
              const finded = _.find(relationEntries, ([, relations]) => {
                const condition = _.some(
                  alternateMeaing(relations),
                  (relation) => _.endsWith(relation.split(':')[0], name),
                );

                return condition;
              });

              if (!finded) {
                err = true;
              } else {
                const [dataName] = finded;

                if (_.isArray(value)) {
                  result[dataName].push(
                    ..._.map(value, (x) => _.omit(x, relationList)),
                  );
                } else {
                  result[dataName].push(_.omit(value, relationList));
                }
              }
            });
        }
      }
    });

    if (err) {
      throw new NotAcceptableException();
    }

    const actualResult: Record<string, any[]> = {};

    // eslint-disable-next-line no-restricted-syntax
    for (const [k, v] of Object.entries(result)) {
      actualResult[k] = _.uniqBy(v, 'id');
    }

    return actualResult;
  }

  // eslint-disable-next-line class-methods-use-this
  clearData<T>(data: T, relations: PaginateTemplateOptions['relations']): T {
    const relationList = _.flatten(_.values(relations)).map((x) =>
      _.last(_.split(x, '.')),
    );

    const result = _.map(_.cloneDeep(data) as any, (x) =>
      _.omit(x, [...relationList]),
    );

    return result as unknown as T;
  }

  // eslint-disable-next-line class-methods-use-this
  relationOrder(
    arrangedRelation: PaginateTemplateOptions['relations'],
  ): string[] {
    return _.map(
      _.orderBy(_.flatten(_.values(arrangedRelation)), 'length'),
      (data) => _.join(_.slice(_.split(data, '.'), -2), '.'),
    );
  }

  async paginate<T extends keyof ReturnTypeList>(
    target: T,
    query: FindConditions<ReturnTypeList[T] | ReturnTypeList[T][]> = {},
    rawRelations: PaginateTemplateOptions['relations'] = {},
    options: AutoResolveOptions = {},
    lookFor: any = [],
  ): Promise<PaginateResponse<ReturnTypeList[T]>> {
    const arrangedRelation = this.relationArranger(target, rawRelations);
    const arrangedRelationOrder = this.relationOrder(arrangedRelation);

    const data = await this.findAll(
      target,
      query,
      arrangedRelationOrder,
      options,
      lookFor,
    );

    const relations = this.relationMapper(data, arrangedRelation);

    if (
      _.some(arrangedRelationOrder, (relation) => {
        _.includes(relation, 'roles');
      })
    ) {
      const additionals = await this.findAll('roles', {
        isDefaultRole: true,
      });
      relations.roles = _.uniqBy(
        [...additionals, ...(relations.roles || [])],
        'id',
      );
    }

    return {
      data: this.clearData(data, arrangedRelation),
      relations,
    };
  }

  event(target: string, error: any, data: unknown) {
    this.server.emit(`update-${target}`, WsResponse(error, data));
  }

  deleteEvent(target: string, error: any, data: unknown) {
    this.server.emit(`delete-${target}`, WsResponse(error, data));
  }
}
