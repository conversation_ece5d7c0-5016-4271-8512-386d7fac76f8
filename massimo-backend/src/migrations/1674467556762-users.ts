import {MigrationInterface, QueryRunner} from "typeorm";

export class users1674467556762 implements MigrationInterface {
    name = 'users1674467556762'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_b36132b69976eb7994ca72f9c8"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" ADD "start" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.250Z"'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_75f98429d50e022de6bc5ffaf8"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" ADD "end" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.250Z"'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5afcb13bf2ecd200ed589626ea"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" ADD "start" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.272Z"'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6f653d754fbb934acc1a993796"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" ADD "end" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.272Z"'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7df51623ccf38bda723c011ad"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" ADD "start" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.298Z"'`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7323fd504dd1d78e4c3badd3cf"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" ADD "end" TIMESTAMP NOT NULL DEFAULT '"2023-01-23T09:52:44.298Z"'`);
        await queryRunner.query(`CREATE INDEX "IDX_b36132b69976eb7994ca72f9c8" ON "public"."offers" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_75f98429d50e022de6bc5ffaf8" ON "public"."offers" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_5afcb13bf2ecd200ed589626ea" ON "public"."requests" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_6f653d754fbb934acc1a993796" ON "public"."requests" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_f7df51623ccf38bda723c011ad" ON "public"."tasks" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_7323fd504dd1d78e4c3badd3cf" ON "public"."tasks" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_a3ffb1c0c8416b9fc6f907b743" ON "public"."users" ("id") `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP INDEX "public"."IDX_a3ffb1c0c8416b9fc6f907b743"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_7323fd504dd1d78e4c3badd3cf"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7df51623ccf38bda723c011ad"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_6f653d754fbb934acc1a993796"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_5afcb13bf2ecd200ed589626ea"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_75f98429d50e022de6bc5ffaf8"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_b36132b69976eb7994ca72f9c8"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" ADD "end" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_7323fd504dd1d78e4c3badd3cf" ON "public"."tasks" ("end") `);
        await queryRunner.query(`ALTER TABLE "public"."tasks" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."tasks" ADD "start" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_f7df51623ccf38bda723c011ad" ON "public"."tasks" ("start") `);
        await queryRunner.query(`ALTER TABLE "public"."requests" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" ADD "end" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_6f653d754fbb934acc1a993796" ON "public"."requests" ("end") `);
        await queryRunner.query(`ALTER TABLE "public"."requests" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."requests" ADD "start" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_5afcb13bf2ecd200ed589626ea" ON "public"."requests" ("start") `);
        await queryRunner.query(`ALTER TABLE "public"."offers" DROP COLUMN "end"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" ADD "end" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_75f98429d50e022de6bc5ffaf8" ON "public"."offers" ("end") `);
        await queryRunner.query(`ALTER TABLE "public"."offers" DROP COLUMN "start"`);
        await queryRunner.query(`ALTER TABLE "public"."offers" ADD "start" TIMESTAMP WITH TIME ZONE NOT NULL`);
        await queryRunner.query(`CREATE INDEX "IDX_b36132b69976eb7994ca72f9c8" ON "public"."offers" ("start") `);
    }

}
