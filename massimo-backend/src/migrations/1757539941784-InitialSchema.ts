import {MigrationInterface, QueryRunner} from "typeorm";

export class InitialSchema1757539941784 implements MigrationInterface {
    name = 'InitialSchema1757539941784'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "accesses" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "path" character varying NOT NULL, "default_roles" jsonb NOT NULL DEFAULT '{}', "departments_roles" jsonb NOT NULL DEFAULT '{}', "value" boolean NOT NULL DEFAULT false, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_576fd8c1c291f0fedd0732b0295" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_c4183e66013c34feebe80d106a" ON "accesses" ("name") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_34f28a786c8144020506eef8fd" ON "accesses" ("path") `);
        await queryRunner.query(`CREATE INDEX "IDX_13b540e8b6dc41a00bfaf421ed" ON "accesses" ("value") `);
        await queryRunner.query(`CREATE TABLE "posts" ("id" SERIAL NOT NULL, "type" character varying NOT NULL DEFAULT 'post', "description" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" integer, "organization_id" integer, CONSTRAINT "PK_2829ac61eff60fcec60d7274b9e" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "files" ("id" SERIAL NOT NULL, "uuid" uuid NOT NULL DEFAULT uuid_generate_v4(), "file" character varying NOT NULL, "type" character varying NOT NULL DEFAULT 'attachment', "access" text, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "owner_id" integer, "post_id" integer, CONSTRAINT "PK_6c16b9093a142e0e7613b04a3d9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_80216965527c9be0babd7ea5bb" ON "files" ("uuid") `);
        await queryRunner.query(`CREATE INDEX "IDX_74333f5f8455bc185519d7aa4a" ON "files" ("file") `);
        await queryRunner.query(`CREATE INDEX "IDX_bbb0f2912c320f6b76e04091e3" ON "files" ("type") `);
        await queryRunner.query(`CREATE TABLE "messages" ("id" SERIAL NOT NULL, "type" character varying NOT NULL, "content" jsonb NOT NULL DEFAULT '{}', "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "owner_id" integer, "chat_id" integer, CONSTRAINT "PK_18325f38ae6de43878487eff986" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_87183e91f31c528f4abc1cdc51" ON "messages" ("type") `);
        await queryRunner.query(`CREATE TABLE "offers" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "budget" integer NOT NULL, "start" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.009Z"', "end" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.009Z"', "status" character varying NOT NULL, "cancellation_reason" character varying, "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "project_id" integer, CONSTRAINT "PK_4c88e956195bba85977da21b8f4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f34d2ed1cd905ff6c3e30f62a1" ON "offers" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_e3b6d2ddd7414d8ca60f4056ef" ON "offers" ("description") `);
        await queryRunner.query(`CREATE INDEX "IDX_f4f65b5259103996f3165e6e24" ON "offers" ("budget") `);
        await queryRunner.query(`CREATE INDEX "IDX_b36132b69976eb7994ca72f9c8" ON "offers" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_75f98429d50e022de6bc5ffaf8" ON "offers" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_434239966cb60e2dbc6178f993" ON "offers" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_01d46f65f83464d8ecee0dc502" ON "offers" ("cancellation_reason") `);
        await queryRunner.query(`CREATE INDEX "IDX_2baeec535a3d32137537f799a1" ON "offers" ("search") `);
        await queryRunner.query(`CREATE TABLE "requests" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "description" character varying NOT NULL, "budget" integer NOT NULL, "start" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.013Z"', "end" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.013Z"', "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "customer_id" integer, "approver_id" integer, "project_id" integer, CONSTRAINT "REL_a4b773f59743c7611cf7c11f19" UNIQUE ("project_id"), CONSTRAINT "PK_0428f484e96f9e6a55955f29b5f" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e859d7c05d4aeb5e1f25f79865" ON "requests" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_78441702d742a61c1e7f09116f" ON "requests" ("description") `);
        await queryRunner.query(`CREATE INDEX "IDX_90ccd9328f595631067fc7988b" ON "requests" ("budget") `);
        await queryRunner.query(`CREATE INDEX "IDX_5afcb13bf2ecd200ed589626ea" ON "requests" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_6f653d754fbb934acc1a993796" ON "requests" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_1315f099b8f9d3f1de6e7a6e6e" ON "requests" ("search") `);
        await queryRunner.query(`CREATE TABLE "tasks" ("id" SERIAL NOT NULL, "title" character varying NOT NULL, "description" character varying, "status" character varying NOT NULL, "priority" character varying NOT NULL, "private" boolean NOT NULL DEFAULT true, "start" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.019Z"', "end" TIMESTAMP NOT NULL DEFAULT '"2025-09-10T21:32:23.019Z"', "customer_private" boolean NOT NULL DEFAULT true, "recurrence" jsonb, "contents" jsonb NOT NULL DEFAULT '[]', "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "project_id" integer, CONSTRAINT "PK_8d12ff38fcc62aaba2cab748772" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_067be4bd67747aa64451933929" ON "tasks" ("title") `);
        await queryRunner.query(`CREATE INDEX "IDX_c9f361efbefcdff99c1ccfd1a3" ON "tasks" ("description") `);
        await queryRunner.query(`CREATE INDEX "IDX_6086c8dafbae729a930c04d865" ON "tasks" ("status") `);
        await queryRunner.query(`CREATE INDEX "IDX_bd213ab7fa55f02309c5f23bbc" ON "tasks" ("priority") `);
        await queryRunner.query(`CREATE INDEX "IDX_7c180cdeb6926bc8414a332069" ON "tasks" ("private") `);
        await queryRunner.query(`CREATE INDEX "IDX_f7df51623ccf38bda723c011ad" ON "tasks" ("start") `);
        await queryRunner.query(`CREATE INDEX "IDX_7323fd504dd1d78e4c3badd3cf" ON "tasks" ("end") `);
        await queryRunner.query(`CREATE INDEX "IDX_082bdc218321baceaf94f41f46" ON "tasks" ("customer_private") `);
        await queryRunner.query(`CREATE INDEX "IDX_3bffb42d553aa86459c5011eb1" ON "tasks" ("search") `);
        await queryRunner.query(`CREATE TABLE "projects" ("id" SERIAL NOT NULL, "priority" character varying NOT NULL, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "request_id" integer, CONSTRAINT "REL_203cfc2c94d0fc3d13b6f258fb" UNIQUE ("request_id"), CONSTRAINT "PK_6271df0a7aed1d6c0691ce6ac50" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_bbcce409ce9ab4d8c65b0c8929" ON "projects" ("priority") `);
        await queryRunner.query(`CREATE TABLE "departments" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "label" character varying NOT NULL, "description" character varying NOT NULL DEFAULT '', "is_visible" boolean NOT NULL DEFAULT false, "color" character varying NOT NULL DEFAULT '#1864AB', "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_839517a681a86bb84cbcc6a1e9d" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8681da666ad9699d568b3e9106" ON "departments" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_759c59e7d5f8e06ad89b945765" ON "departments" ("label") `);
        await queryRunner.query(`CREATE INDEX "IDX_2a1fa8bd4561fb3ea0f738656a" ON "departments" ("description") `);
        await queryRunner.query(`CREATE INDEX "IDX_802e08c9521a037858341a4f55" ON "departments" ("is_visible") `);
        await queryRunner.query(`CREATE INDEX "IDX_aa358257929b884d11f050904b" ON "departments" ("color") `);
        await queryRunner.query(`CREATE INDEX "IDX_ca9812f109756311bf56923bd5" ON "departments" ("search") `);
        await queryRunner.query(`CREATE TABLE "roles" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "label" character varying NOT NULL, "weight" integer NOT NULL DEFAULT '0', "is_default_role" boolean NOT NULL DEFAULT false, "is_department_role" boolean NOT NULL DEFAULT true, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "department_id" integer, CONSTRAINT "PK_c1433d71a4838793a49dcad46ab" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_648e3f5447f725579d7d4ffdfb" ON "roles" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_54dfc4a418c052c458703ae7d8" ON "roles" ("label") `);
        await queryRunner.query(`CREATE INDEX "IDX_8998ff66222537ad6961f5c6f6" ON "roles" ("weight") `);
        await queryRunner.query(`CREATE INDEX "IDX_e41f603e686903a7d515b96dbf" ON "roles" ("is_default_role") `);
        await queryRunner.query(`CREATE INDEX "IDX_0742129e9b4e5d47509b8e9b56" ON "roles" ("is_department_role") `);
        await queryRunner.query(`CREATE TABLE "users" ("id" SERIAL NOT NULL, "name" character varying NOT NULL, "surname" character varying, "email" character varying NOT NULL, "phone" character varying, "is_customer" boolean NOT NULL DEFAULT false, "is_valid" boolean NOT NULL DEFAULT false, "is_banned" boolean NOT NULL DEFAULT false, "role_values" jsonb NOT NULL DEFAULT '{}', "password" character varying NOT NULL, "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_a3ffb1c0c8416b9fc6f907b7433" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_a3ffb1c0c8416b9fc6f907b743" ON "users" ("id") `);
        await queryRunner.query(`CREATE INDEX "IDX_51b8b26ac168fbe7d6f5653e6c" ON "users" ("name") `);
        await queryRunner.query(`CREATE INDEX "IDX_5d6bb8b8d415f4808a784b4aa9" ON "users" ("surname") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_97672ac88f789774dd47f7c8be" ON "users" ("email") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_a000cca60bcf04454e72769949" ON "users" ("phone") `);
        await queryRunner.query(`CREATE INDEX "IDX_6fc36a02716a643c8b4c4e0f6f" ON "users" ("is_customer") `);
        await queryRunner.query(`CREATE INDEX "IDX_86773323a353c113f8af50bbee" ON "users" ("is_valid") `);
        await queryRunner.query(`CREATE INDEX "IDX_828a06f7ee7c54015682a8ecf7" ON "users" ("is_banned") `);
        await queryRunner.query(`CREATE INDEX "IDX_f600be4d3e93115da5bb7b0d8e" ON "users" ("search") `);
        await queryRunner.query(`CREATE TABLE "customers" ("id" SERIAL NOT NULL, "short_name" character varying NOT NULL, "full_name" character varying NOT NULL, "location" character varying NOT NULL, "phone" character varying NOT NULL, "email" character varying NOT NULL, "tax_department" character varying NOT NULL, "tax_number" character varying NOT NULL, "address" character varying NOT NULL, "search" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_133ec679a801fab5e070f73d3ea" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_dcd8f54653b915c6bd657b3018" ON "customers" ("short_name") `);
        await queryRunner.query(`CREATE INDEX "IDX_bf14b22e55d577d5834fcf886b" ON "customers" ("full_name") `);
        await queryRunner.query(`CREATE INDEX "IDX_e4ebe64bb622ddb1acde8c0239" ON "customers" ("location") `);
        await queryRunner.query(`CREATE INDEX "IDX_88acd889fbe17d0e16cc4bc917" ON "customers" ("phone") `);
        await queryRunner.query(`CREATE UNIQUE INDEX "IDX_8536b8b85c06969f84f0c098b0" ON "customers" ("email") `);
        await queryRunner.query(`CREATE INDEX "IDX_606aac006f3b97b37fd73e8c4d" ON "customers" ("tax_department") `);
        await queryRunner.query(`CREATE INDEX "IDX_05c9fcfbb30f7071570c370f32" ON "customers" ("tax_number") `);
        await queryRunner.query(`CREATE INDEX "IDX_cb0313acf54b486381e2f0c6ad" ON "customers" ("address") `);
        await queryRunner.query(`CREATE INDEX "IDX_9cb7e16d0b608f31f88d310271" ON "customers" ("search") `);
        await queryRunner.query(`CREATE TABLE "chats" ("id" SERIAL NOT NULL, "type" character varying NOT NULL, "inactive" boolean NOT NULL DEFAULT false, "options" jsonb NOT NULL DEFAULT '{}', "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "offer_id" integer, "organization_id" integer, "person_id" integer, "owner_id" integer, "task_id" integer, CONSTRAINT "REL_602ac7bc7afd1af50d8c039792" UNIQUE ("offer_id"), CONSTRAINT "REL_6b0c7c74da18cb2194974bd70a" UNIQUE ("organization_id"), CONSTRAINT "REL_b3b1683813f00e1d73da8b2c87" UNIQUE ("task_id"), CONSTRAINT "PK_0117647b3c4a4e5ff198aeb6206" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_82098aa8bb9df492a810ff0be9" ON "chats" ("type") `);
        await queryRunner.query(`CREATE INDEX "IDX_b28f6d47fa01e32dd4692d16d7" ON "chats" ("inactive") `);
        await queryRunner.query(`CREATE TABLE "connections" ("id" SERIAL NOT NULL, "is_accepted1" boolean NOT NULL DEFAULT false, "is_accepted2" boolean NOT NULL DEFAULT false, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "target1_id" integer, "target2_id" integer, CONSTRAINT "PK_0a1f844af3122354cbd487a8d03" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "notifications" ("id" SERIAL NOT NULL, "target" character varying NOT NULL, "value" character varying NOT NULL, "stored" character varying, "is_readed" boolean NOT NULL DEFAULT false, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" integer, CONSTRAINT "PK_6a72c3c0f683f6462415e653c3a" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_07ed7b75996a83fc6274401ea2" ON "notifications" ("target") `);
        await queryRunner.query(`CREATE INDEX "IDX_69ec01e4b052f84dfbdb20dad8" ON "notifications" ("value") `);
        await queryRunner.query(`CREATE INDEX "IDX_40bf521ba2f8d4d3da01a23c1c" ON "notifications" ("stored") `);
        await queryRunner.query(`CREATE INDEX "IDX_75b7f19dde5c3ffa25fa66c3bb" ON "notifications" ("is_readed") `);
        await queryRunner.query(`CREATE TABLE "comments" ("id" SERIAL NOT NULL, "description" character varying, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" integer, "post_id" integer, CONSTRAINT "PK_8bf68bc960f2b69e818bdb90dcb" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "postmetas" ("id" SERIAL NOT NULL, "is_like" boolean NOT NULL DEFAULT false, "is_view" boolean NOT NULL DEFAULT true, "version" integer NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "user_id" integer, "post_id" integer, CONSTRAINT "PK_2cd88a66ff3f6eff4253ac348fe" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "assignees" ("task_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_209593f8a0115aa95b9b24f5c48" PRIMARY KEY ("task_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ea0434015a2b61c004dcf419f7" ON "assignees" ("task_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_1144124efbfd8d8a69ac16f007" ON "assignees" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "contributors" ("task_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_74c089c4349df72be5c11fc6504" PRIMARY KEY ("task_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_abd39bf5610be476cf72059e09" ON "contributors" ("task_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_b39831a1822a4cb3e5ba7cb27a" ON "contributors" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "projects_departments" ("project_id" integer NOT NULL, "department_id" integer NOT NULL, CONSTRAINT "PK_fa31fef1173050018a70253e1ec" PRIMARY KEY ("project_id", "department_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_adf68a3c19067faa114c968097" ON "projects_departments" ("project_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_6333ee22c103593b9ec61007f2" ON "projects_departments" ("department_id") `);
        await queryRunner.query(`CREATE TABLE "project_allowed_users" ("project_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_e0659c4963b2ca8c26d6f2c122f" PRIMARY KEY ("project_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_24f74e159f70cfb5a36dfadc17" ON "project_allowed_users" ("project_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8f1529eacd8c59634dc1b0a0f2" ON "project_allowed_users" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "project_restricted_users" ("project_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_5e425f682038b0ee7ff47285291" PRIMARY KEY ("project_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_1d5191d49b1ff2aeb3fbd18c16" ON "project_restricted_users" ("project_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_488efdff8fbfe14dd594871354" ON "project_restricted_users" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "departments_users" ("department_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_5d39f7528c084cdce70c150159e" PRIMARY KEY ("department_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_d1fd883d210abff1109645268f" ON "departments_users" ("department_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_e77268cbb031688beddd37b912" ON "departments_users" ("user_id") `);
        await queryRunner.query(`CREATE TABLE "default_roles" ("user_id" integer NOT NULL, "role_id" integer NOT NULL, CONSTRAINT "PK_59eef57b86dc59c750d11a3b04c" PRIMARY KEY ("user_id", "role_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_ae6c528ff104a4f076721312c1" ON "default_roles" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_985e1888fd5fa266674b2a296c" ON "default_roles" ("role_id") `);
        await queryRunner.query(`CREATE TABLE "users_roles" ("user_id" integer NOT NULL, "role_id" integer NOT NULL, CONSTRAINT "PK_c525e9373d63035b9919e578a9c" PRIMARY KEY ("user_id", "role_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_e4435209df12bc1f001e536017" ON "users_roles" ("user_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_1cf664021f00b9cc1ff95e17de" ON "users_roles" ("role_id") `);
        await queryRunner.query(`CREATE TABLE "customers_users" ("customer_id" integer NOT NULL, "user_id" integer NOT NULL, CONSTRAINT "PK_80f694c604f391d10652440d83f" PRIMARY KEY ("customer_id", "user_id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_88d53b8020bc6a24efcef43fbe" ON "customers_users" ("customer_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_8f76074079f1a375eb9fba0b8b" ON "customers_users" ("user_id") `);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_c4f9a7bd77b489e711277ee5986" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "posts" ADD CONSTRAINT "FK_47dffb39b4d5ab644bb67bf12d1" FOREIGN KEY ("organization_id") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "files" ADD CONSTRAINT "FK_4bc1db1f4f34ec9415acd88afdb" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "files" ADD CONSTRAINT "FK_3bafa3455a692c11471ac3bf375" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_1668626956fd00acef4b93d2943" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "messages" ADD CONSTRAINT "FK_7540635fef1922f0b156b9ef74f" FOREIGN KEY ("chat_id") REFERENCES "chats"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "offers" ADD CONSTRAINT "FK_972f4eb585900935484a2c1f5ec" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "requests" ADD CONSTRAINT "FK_210052a4b94dfd2394b07ebde1d" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "requests" ADD CONSTRAINT "FK_146a173dc8d160a3b64cd9664d1" FOREIGN KEY ("approver_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "requests" ADD CONSTRAINT "FK_a4b773f59743c7611cf7c11f198" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "tasks" ADD CONSTRAINT "FK_9eecdb5b1ed8c7c2a1b392c28d4" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "projects" ADD CONSTRAINT "FK_203cfc2c94d0fc3d13b6f258fb0" FOREIGN KEY ("request_id") REFERENCES "requests"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "roles" ADD CONSTRAINT "FK_f9842b21d158bb60985c03a9813" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chats" ADD CONSTRAINT "FK_602ac7bc7afd1af50d8c039792e" FOREIGN KEY ("offer_id") REFERENCES "offers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chats" ADD CONSTRAINT "FK_6b0c7c74da18cb2194974bd70a1" FOREIGN KEY ("organization_id") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chats" ADD CONSTRAINT "FK_27af33bf5e16ba4f446a61217f8" FOREIGN KEY ("person_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chats" ADD CONSTRAINT "FK_fe259bf83d8aac1091be2fac967" FOREIGN KEY ("owner_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "chats" ADD CONSTRAINT "FK_b3b1683813f00e1d73da8b2c873" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "connections" ADD CONSTRAINT "FK_b1bd1f6a69fb0bc6e6cde9be36b" FOREIGN KEY ("target1_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "connections" ADD CONSTRAINT "FK_6d7cda677feecf8a6d65dbbad36" FOREIGN KEY ("target2_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "notifications" ADD CONSTRAINT "FK_9a8a82462cab47c73d25f49261f" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_4c675567d2a58f0b07cef09c13d" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "comments" ADD CONSTRAINT "FK_259bf9825d9d198608d1b46b0b5" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "postmetas" ADD CONSTRAINT "FK_ff917f9f2af1a85aa612f96eb76" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "postmetas" ADD CONSTRAINT "FK_05fa7cef3e789811483e452d082" FOREIGN KEY ("post_id") REFERENCES "posts"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "assignees" ADD CONSTRAINT "FK_ea0434015a2b61c004dcf419f7a" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "assignees" ADD CONSTRAINT "FK_1144124efbfd8d8a69ac16f0073" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "contributors" ADD CONSTRAINT "FK_abd39bf5610be476cf72059e09e" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "contributors" ADD CONSTRAINT "FK_b39831a1822a4cb3e5ba7cb27a7" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "projects_departments" ADD CONSTRAINT "FK_adf68a3c19067faa114c9680978" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "projects_departments" ADD CONSTRAINT "FK_6333ee22c103593b9ec61007f22" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project_allowed_users" ADD CONSTRAINT "FK_24f74e159f70cfb5a36dfadc175" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "project_allowed_users" ADD CONSTRAINT "FK_8f1529eacd8c59634dc1b0a0f22" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "project_restricted_users" ADD CONSTRAINT "FK_1d5191d49b1ff2aeb3fbd18c16d" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "project_restricted_users" ADD CONSTRAINT "FK_488efdff8fbfe14dd594871354c" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "departments_users" ADD CONSTRAINT "FK_d1fd883d210abff1109645268f4" FOREIGN KEY ("department_id") REFERENCES "departments"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "departments_users" ADD CONSTRAINT "FK_e77268cbb031688beddd37b912d" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "default_roles" ADD CONSTRAINT "FK_ae6c528ff104a4f076721312c16" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "default_roles" ADD CONSTRAINT "FK_985e1888fd5fa266674b2a296c6" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "users_roles" ADD CONSTRAINT "FK_e4435209df12bc1f001e5360174" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "users_roles" ADD CONSTRAINT "FK_1cf664021f00b9cc1ff95e17de4" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "customers_users" ADD CONSTRAINT "FK_88d53b8020bc6a24efcef43fbef" FOREIGN KEY ("customer_id") REFERENCES "customers"("id") ON DELETE CASCADE ON UPDATE CASCADE`);
        await queryRunner.query(`ALTER TABLE "customers_users" ADD CONSTRAINT "FK_8f76074079f1a375eb9fba0b8b1" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "customers_users" DROP CONSTRAINT "FK_8f76074079f1a375eb9fba0b8b1"`);
        await queryRunner.query(`ALTER TABLE "customers_users" DROP CONSTRAINT "FK_88d53b8020bc6a24efcef43fbef"`);
        await queryRunner.query(`ALTER TABLE "users_roles" DROP CONSTRAINT "FK_1cf664021f00b9cc1ff95e17de4"`);
        await queryRunner.query(`ALTER TABLE "users_roles" DROP CONSTRAINT "FK_e4435209df12bc1f001e5360174"`);
        await queryRunner.query(`ALTER TABLE "default_roles" DROP CONSTRAINT "FK_985e1888fd5fa266674b2a296c6"`);
        await queryRunner.query(`ALTER TABLE "default_roles" DROP CONSTRAINT "FK_ae6c528ff104a4f076721312c16"`);
        await queryRunner.query(`ALTER TABLE "departments_users" DROP CONSTRAINT "FK_e77268cbb031688beddd37b912d"`);
        await queryRunner.query(`ALTER TABLE "departments_users" DROP CONSTRAINT "FK_d1fd883d210abff1109645268f4"`);
        await queryRunner.query(`ALTER TABLE "project_restricted_users" DROP CONSTRAINT "FK_488efdff8fbfe14dd594871354c"`);
        await queryRunner.query(`ALTER TABLE "project_restricted_users" DROP CONSTRAINT "FK_1d5191d49b1ff2aeb3fbd18c16d"`);
        await queryRunner.query(`ALTER TABLE "project_allowed_users" DROP CONSTRAINT "FK_8f1529eacd8c59634dc1b0a0f22"`);
        await queryRunner.query(`ALTER TABLE "project_allowed_users" DROP CONSTRAINT "FK_24f74e159f70cfb5a36dfadc175"`);
        await queryRunner.query(`ALTER TABLE "projects_departments" DROP CONSTRAINT "FK_6333ee22c103593b9ec61007f22"`);
        await queryRunner.query(`ALTER TABLE "projects_departments" DROP CONSTRAINT "FK_adf68a3c19067faa114c9680978"`);
        await queryRunner.query(`ALTER TABLE "contributors" DROP CONSTRAINT "FK_b39831a1822a4cb3e5ba7cb27a7"`);
        await queryRunner.query(`ALTER TABLE "contributors" DROP CONSTRAINT "FK_abd39bf5610be476cf72059e09e"`);
        await queryRunner.query(`ALTER TABLE "assignees" DROP CONSTRAINT "FK_1144124efbfd8d8a69ac16f0073"`);
        await queryRunner.query(`ALTER TABLE "assignees" DROP CONSTRAINT "FK_ea0434015a2b61c004dcf419f7a"`);
        await queryRunner.query(`ALTER TABLE "postmetas" DROP CONSTRAINT "FK_05fa7cef3e789811483e452d082"`);
        await queryRunner.query(`ALTER TABLE "postmetas" DROP CONSTRAINT "FK_ff917f9f2af1a85aa612f96eb76"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_259bf9825d9d198608d1b46b0b5"`);
        await queryRunner.query(`ALTER TABLE "comments" DROP CONSTRAINT "FK_4c675567d2a58f0b07cef09c13d"`);
        await queryRunner.query(`ALTER TABLE "notifications" DROP CONSTRAINT "FK_9a8a82462cab47c73d25f49261f"`);
        await queryRunner.query(`ALTER TABLE "connections" DROP CONSTRAINT "FK_6d7cda677feecf8a6d65dbbad36"`);
        await queryRunner.query(`ALTER TABLE "connections" DROP CONSTRAINT "FK_b1bd1f6a69fb0bc6e6cde9be36b"`);
        await queryRunner.query(`ALTER TABLE "chats" DROP CONSTRAINT "FK_b3b1683813f00e1d73da8b2c873"`);
        await queryRunner.query(`ALTER TABLE "chats" DROP CONSTRAINT "FK_fe259bf83d8aac1091be2fac967"`);
        await queryRunner.query(`ALTER TABLE "chats" DROP CONSTRAINT "FK_27af33bf5e16ba4f446a61217f8"`);
        await queryRunner.query(`ALTER TABLE "chats" DROP CONSTRAINT "FK_6b0c7c74da18cb2194974bd70a1"`);
        await queryRunner.query(`ALTER TABLE "chats" DROP CONSTRAINT "FK_602ac7bc7afd1af50d8c039792e"`);
        await queryRunner.query(`ALTER TABLE "roles" DROP CONSTRAINT "FK_f9842b21d158bb60985c03a9813"`);
        await queryRunner.query(`ALTER TABLE "projects" DROP CONSTRAINT "FK_203cfc2c94d0fc3d13b6f258fb0"`);
        await queryRunner.query(`ALTER TABLE "tasks" DROP CONSTRAINT "FK_9eecdb5b1ed8c7c2a1b392c28d4"`);
        await queryRunner.query(`ALTER TABLE "requests" DROP CONSTRAINT "FK_a4b773f59743c7611cf7c11f198"`);
        await queryRunner.query(`ALTER TABLE "requests" DROP CONSTRAINT "FK_146a173dc8d160a3b64cd9664d1"`);
        await queryRunner.query(`ALTER TABLE "requests" DROP CONSTRAINT "FK_210052a4b94dfd2394b07ebde1d"`);
        await queryRunner.query(`ALTER TABLE "offers" DROP CONSTRAINT "FK_972f4eb585900935484a2c1f5ec"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_7540635fef1922f0b156b9ef74f"`);
        await queryRunner.query(`ALTER TABLE "messages" DROP CONSTRAINT "FK_1668626956fd00acef4b93d2943"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_3bafa3455a692c11471ac3bf375"`);
        await queryRunner.query(`ALTER TABLE "files" DROP CONSTRAINT "FK_4bc1db1f4f34ec9415acd88afdb"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_47dffb39b4d5ab644bb67bf12d1"`);
        await queryRunner.query(`ALTER TABLE "posts" DROP CONSTRAINT "FK_c4f9a7bd77b489e711277ee5986"`);
        await queryRunner.query(`DROP INDEX "IDX_8f76074079f1a375eb9fba0b8b"`);
        await queryRunner.query(`DROP INDEX "IDX_88d53b8020bc6a24efcef43fbe"`);
        await queryRunner.query(`DROP TABLE "customers_users"`);
        await queryRunner.query(`DROP INDEX "IDX_1cf664021f00b9cc1ff95e17de"`);
        await queryRunner.query(`DROP INDEX "IDX_e4435209df12bc1f001e536017"`);
        await queryRunner.query(`DROP TABLE "users_roles"`);
        await queryRunner.query(`DROP INDEX "IDX_985e1888fd5fa266674b2a296c"`);
        await queryRunner.query(`DROP INDEX "IDX_ae6c528ff104a4f076721312c1"`);
        await queryRunner.query(`DROP TABLE "default_roles"`);
        await queryRunner.query(`DROP INDEX "IDX_e77268cbb031688beddd37b912"`);
        await queryRunner.query(`DROP INDEX "IDX_d1fd883d210abff1109645268f"`);
        await queryRunner.query(`DROP TABLE "departments_users"`);
        await queryRunner.query(`DROP INDEX "IDX_488efdff8fbfe14dd594871354"`);
        await queryRunner.query(`DROP INDEX "IDX_1d5191d49b1ff2aeb3fbd18c16"`);
        await queryRunner.query(`DROP TABLE "project_restricted_users"`);
        await queryRunner.query(`DROP INDEX "IDX_8f1529eacd8c59634dc1b0a0f2"`);
        await queryRunner.query(`DROP INDEX "IDX_24f74e159f70cfb5a36dfadc17"`);
        await queryRunner.query(`DROP TABLE "project_allowed_users"`);
        await queryRunner.query(`DROP INDEX "IDX_6333ee22c103593b9ec61007f2"`);
        await queryRunner.query(`DROP INDEX "IDX_adf68a3c19067faa114c968097"`);
        await queryRunner.query(`DROP TABLE "projects_departments"`);
        await queryRunner.query(`DROP INDEX "IDX_b39831a1822a4cb3e5ba7cb27a"`);
        await queryRunner.query(`DROP INDEX "IDX_abd39bf5610be476cf72059e09"`);
        await queryRunner.query(`DROP TABLE "contributors"`);
        await queryRunner.query(`DROP INDEX "IDX_1144124efbfd8d8a69ac16f007"`);
        await queryRunner.query(`DROP INDEX "IDX_ea0434015a2b61c004dcf419f7"`);
        await queryRunner.query(`DROP TABLE "assignees"`);
        await queryRunner.query(`DROP TABLE "postmetas"`);
        await queryRunner.query(`DROP TABLE "comments"`);
        await queryRunner.query(`DROP INDEX "IDX_75b7f19dde5c3ffa25fa66c3bb"`);
        await queryRunner.query(`DROP INDEX "IDX_40bf521ba2f8d4d3da01a23c1c"`);
        await queryRunner.query(`DROP INDEX "IDX_69ec01e4b052f84dfbdb20dad8"`);
        await queryRunner.query(`DROP INDEX "IDX_07ed7b75996a83fc6274401ea2"`);
        await queryRunner.query(`DROP TABLE "notifications"`);
        await queryRunner.query(`DROP TABLE "connections"`);
        await queryRunner.query(`DROP INDEX "IDX_b28f6d47fa01e32dd4692d16d7"`);
        await queryRunner.query(`DROP INDEX "IDX_82098aa8bb9df492a810ff0be9"`);
        await queryRunner.query(`DROP TABLE "chats"`);
        await queryRunner.query(`DROP INDEX "IDX_9cb7e16d0b608f31f88d310271"`);
        await queryRunner.query(`DROP INDEX "IDX_cb0313acf54b486381e2f0c6ad"`);
        await queryRunner.query(`DROP INDEX "IDX_05c9fcfbb30f7071570c370f32"`);
        await queryRunner.query(`DROP INDEX "IDX_606aac006f3b97b37fd73e8c4d"`);
        await queryRunner.query(`DROP INDEX "IDX_8536b8b85c06969f84f0c098b0"`);
        await queryRunner.query(`DROP INDEX "IDX_88acd889fbe17d0e16cc4bc917"`);
        await queryRunner.query(`DROP INDEX "IDX_e4ebe64bb622ddb1acde8c0239"`);
        await queryRunner.query(`DROP INDEX "IDX_bf14b22e55d577d5834fcf886b"`);
        await queryRunner.query(`DROP INDEX "IDX_dcd8f54653b915c6bd657b3018"`);
        await queryRunner.query(`DROP TABLE "customers"`);
        await queryRunner.query(`DROP INDEX "IDX_f600be4d3e93115da5bb7b0d8e"`);
        await queryRunner.query(`DROP INDEX "IDX_828a06f7ee7c54015682a8ecf7"`);
        await queryRunner.query(`DROP INDEX "IDX_86773323a353c113f8af50bbee"`);
        await queryRunner.query(`DROP INDEX "IDX_6fc36a02716a643c8b4c4e0f6f"`);
        await queryRunner.query(`DROP INDEX "IDX_a000cca60bcf04454e72769949"`);
        await queryRunner.query(`DROP INDEX "IDX_97672ac88f789774dd47f7c8be"`);
        await queryRunner.query(`DROP INDEX "IDX_5d6bb8b8d415f4808a784b4aa9"`);
        await queryRunner.query(`DROP INDEX "IDX_51b8b26ac168fbe7d6f5653e6c"`);
        await queryRunner.query(`DROP INDEX "IDX_a3ffb1c0c8416b9fc6f907b743"`);
        await queryRunner.query(`DROP TABLE "users"`);
        await queryRunner.query(`DROP INDEX "IDX_0742129e9b4e5d47509b8e9b56"`);
        await queryRunner.query(`DROP INDEX "IDX_e41f603e686903a7d515b96dbf"`);
        await queryRunner.query(`DROP INDEX "IDX_8998ff66222537ad6961f5c6f6"`);
        await queryRunner.query(`DROP INDEX "IDX_54dfc4a418c052c458703ae7d8"`);
        await queryRunner.query(`DROP INDEX "IDX_648e3f5447f725579d7d4ffdfb"`);
        await queryRunner.query(`DROP TABLE "roles"`);
        await queryRunner.query(`DROP INDEX "IDX_ca9812f109756311bf56923bd5"`);
        await queryRunner.query(`DROP INDEX "IDX_aa358257929b884d11f050904b"`);
        await queryRunner.query(`DROP INDEX "IDX_802e08c9521a037858341a4f55"`);
        await queryRunner.query(`DROP INDEX "IDX_2a1fa8bd4561fb3ea0f738656a"`);
        await queryRunner.query(`DROP INDEX "IDX_759c59e7d5f8e06ad89b945765"`);
        await queryRunner.query(`DROP INDEX "IDX_8681da666ad9699d568b3e9106"`);
        await queryRunner.query(`DROP TABLE "departments"`);
        await queryRunner.query(`DROP INDEX "IDX_bbcce409ce9ab4d8c65b0c8929"`);
        await queryRunner.query(`DROP TABLE "projects"`);
        await queryRunner.query(`DROP INDEX "IDX_3bffb42d553aa86459c5011eb1"`);
        await queryRunner.query(`DROP INDEX "IDX_082bdc218321baceaf94f41f46"`);
        await queryRunner.query(`DROP INDEX "IDX_7323fd504dd1d78e4c3badd3cf"`);
        await queryRunner.query(`DROP INDEX "IDX_f7df51623ccf38bda723c011ad"`);
        await queryRunner.query(`DROP INDEX "IDX_7c180cdeb6926bc8414a332069"`);
        await queryRunner.query(`DROP INDEX "IDX_bd213ab7fa55f02309c5f23bbc"`);
        await queryRunner.query(`DROP INDEX "IDX_6086c8dafbae729a930c04d865"`);
        await queryRunner.query(`DROP INDEX "IDX_c9f361efbefcdff99c1ccfd1a3"`);
        await queryRunner.query(`DROP INDEX "IDX_067be4bd67747aa64451933929"`);
        await queryRunner.query(`DROP TABLE "tasks"`);
        await queryRunner.query(`DROP INDEX "IDX_1315f099b8f9d3f1de6e7a6e6e"`);
        await queryRunner.query(`DROP INDEX "IDX_6f653d754fbb934acc1a993796"`);
        await queryRunner.query(`DROP INDEX "IDX_5afcb13bf2ecd200ed589626ea"`);
        await queryRunner.query(`DROP INDEX "IDX_90ccd9328f595631067fc7988b"`);
        await queryRunner.query(`DROP INDEX "IDX_78441702d742a61c1e7f09116f"`);
        await queryRunner.query(`DROP INDEX "IDX_e859d7c05d4aeb5e1f25f79865"`);
        await queryRunner.query(`DROP TABLE "requests"`);
        await queryRunner.query(`DROP INDEX "IDX_2baeec535a3d32137537f799a1"`);
        await queryRunner.query(`DROP INDEX "IDX_01d46f65f83464d8ecee0dc502"`);
        await queryRunner.query(`DROP INDEX "IDX_434239966cb60e2dbc6178f993"`);
        await queryRunner.query(`DROP INDEX "IDX_75f98429d50e022de6bc5ffaf8"`);
        await queryRunner.query(`DROP INDEX "IDX_b36132b69976eb7994ca72f9c8"`);
        await queryRunner.query(`DROP INDEX "IDX_f4f65b5259103996f3165e6e24"`);
        await queryRunner.query(`DROP INDEX "IDX_e3b6d2ddd7414d8ca60f4056ef"`);
        await queryRunner.query(`DROP INDEX "IDX_f34d2ed1cd905ff6c3e30f62a1"`);
        await queryRunner.query(`DROP TABLE "offers"`);
        await queryRunner.query(`DROP INDEX "IDX_87183e91f31c528f4abc1cdc51"`);
        await queryRunner.query(`DROP TABLE "messages"`);
        await queryRunner.query(`DROP INDEX "IDX_bbb0f2912c320f6b76e04091e3"`);
        await queryRunner.query(`DROP INDEX "IDX_74333f5f8455bc185519d7aa4a"`);
        await queryRunner.query(`DROP INDEX "IDX_80216965527c9be0babd7ea5bb"`);
        await queryRunner.query(`DROP TABLE "files"`);
        await queryRunner.query(`DROP TABLE "posts"`);
        await queryRunner.query(`DROP INDEX "IDX_13b540e8b6dc41a00bfaf421ed"`);
        await queryRunner.query(`DROP INDEX "IDX_34f28a786c8144020506eef8fd"`);
        await queryRunner.query(`DROP INDEX "IDX_c4183e66013c34feebe80d106a"`);
        await queryRunner.query(`DROP TABLE "accesses"`);
    }

}
