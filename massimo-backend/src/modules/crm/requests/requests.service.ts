import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, FindConditions, Repository, SaveOptions } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { Request } from './requests.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class RequestsService {
  constructor(
    @InjectRepository(Request)
    private readonly requestsRepo: Repository<Request>,
  ) {}

  async findAll(
    query: FindConditions<Request> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Request[]> {
    let requests: Request[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'request',
        this.requestsRepo.createQueryBuilder('request'),
        relations,
        options,
      );

      requests = await builder.where(query).getMany();
    } else {
      requests = await this.requestsRepo.find({
        where: query,
        ...options,
      });
    }

    return requests;
  }

  async findOne(
    query: FindConditions<Request>,
    relations: string[] = [],
  ): Promise<Request> {
    let request: Request;

    if (relations.length > 0) {
      const builder = autoResolve(
        'request',
        this.requestsRepo.createQueryBuilder('request'),
        relations,
      );

      request = await builder.where(query).getOne();
    } else {
      request = await this.requestsRepo.findOne(query);
    }

    // const request = await this.requestsRepo.findOne(query, options);
    return request;
  }

  async update(
    query: FindConditions<Request>,
    update: QueryDeepPartialEntity<Request>,
  ): Promise<true> {
    await this.requestsRepo.update(query, update);

    return true;
  }

  async save(
    query: DeepPartial<Request>[],
    options?: SaveOptions,
  ): Promise<true> {
    await this.requestsRepo.save(query, options);

    return true;
  }
}
