import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { Request } from './requests.entity';
import { RequestsService } from './requests.service';

import { RolesService } from '@/base/roles/roles.service';
import { CommonsService, PaginateResponse } from '@/common/common.service';

@Controller('api/crm/requests')
export class RequestsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly requestsService: RequestsService,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Request>> {
    const { query, options, page, limit } = data;

    const requests = await this.commonsService.paginate(
      'requests',
      query,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return requests;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(@Body() body: UpdateInput): Promise<Request[]> {
    const { data, changes = {}, order = ['$'] } = body;

    const result = await this.commonsService.update(
      'requests',
      data as any,
      changes,
      order,
    );

    return result;
  }
}
