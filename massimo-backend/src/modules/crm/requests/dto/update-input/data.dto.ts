import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON>A<PERSON>y,
  IsBoolean,
  IsDate,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

import { Request } from '../../requests.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData
  implements Partial<Omit<Request, 'start' | 'end' | 'id'>>
{
  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  id: number | string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  budget: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  start: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  end: string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  customerId: number;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  approverId: number;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  projectId: number;
}
