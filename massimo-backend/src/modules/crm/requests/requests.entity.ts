import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Project } from '../projects/projects.entity';

import { Customer } from '@/base/customers/customers.entity';
import { Department } from '@/base/departments/departments.entity';
import { User } from '@/base/users/users.entity';

@ObjectType()
@Entity()
export class Request {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  name: string;

  @Index()
  @Column()
  description: string;

  @Index()
  @Column()
  budget: number;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  start: Date;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  end: Date;

  @ManyToOne(() => Customer, (customer) => customer.requests, { onDelete: 'CASCADE' })
  customer: Customer;

  @RelationId((request: Request) => request.customer)
  customerId: number;

  @ManyToOne(() => User, (user) => user.approveds, { onDelete: 'CASCADE' })
  approver: User;

  @RelationId((request: Request) => request.approver)
  approverId: number;

  @OneToOne(() => Project, (project) => project.request, { onDelete: 'CASCADE' })
  @JoinColumn()
  project: Project;

  @RelationId((request: Request) => request.project)
  projectId: number;

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
