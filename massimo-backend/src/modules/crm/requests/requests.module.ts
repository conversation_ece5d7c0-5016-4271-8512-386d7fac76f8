import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RequestsController } from './requests.controller';
import { Request } from './requests.entity';
import { RequestsService } from './requests.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { RolesModule } from '@/base/roles/roles.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Request]),
    forwardRef(() => CommonsModule),
    forwardRef(() => RolesModule),
  ],
  controllers: [RequestsController],
  providers: [RequestsService],
  exports: [RequestsService],
})
export class RequestsModule {}
