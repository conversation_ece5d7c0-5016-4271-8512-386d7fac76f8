import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString, MinLength } from 'class-validator';

export class Query {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id?: number;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly search: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  projectId?: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  start?: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  end?: string;
}
