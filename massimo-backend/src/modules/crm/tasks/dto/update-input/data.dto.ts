import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData {
  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  id: number | string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  priority: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  private: boolean;

  @ApiProperty()
  @IsString()
  @IsOptional()
  start: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  end: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  customerPrivate: boolean;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  assigneeIds: number[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  contributorIds: number[];

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  projectId: number | string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  recurrence: { cron: string; waitForDeadline: boolean }[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  contents: any[];
}
