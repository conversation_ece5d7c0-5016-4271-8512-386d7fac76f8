import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  Req,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import {
  Between,
  In,
  LessThanOrEqual,
  MoreThan,
  MoreThanOrEqual,
} from 'typeorm';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { Task } from './tasks.entity';
import { TasksService } from './tasks.service';

import { User } from '@/base/users/users.entity';
import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/crm/tasks')
export class TasksController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly tasksService: TasksService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  // eslint-disable-next-line class-methods-use-this
  async findWithPagination(
    @Req() request: Request,
    @Body() body: PaginateInput,
  ): Promise<PaginateResponse<Task>> {
    const { query: rawQuery, options } = body;
    const user = request.user as User;

    const baseQuery: any = {};
    let query: any = {};

    if (user.restrictedProjectIds.includes(rawQuery.projectId)) {
      return [] as any;
    }

    if(rawQuery.id) {
      baseQuery.id = In([rawQuery.id]);

      query = baseQuery;
    }else {
      if (rawQuery.projectId) {
        baseQuery.project = rawQuery.projectId;
      }
  
      if (!_.isEmpty(user.assigneds)) {
        baseQuery.assignees = In(user.assigneds);
      }
  
      // eslint-disable-next-line unicorn/prefer-ternary
      if (rawQuery.start && rawQuery.end) {
        query = [
          {
            start: MoreThanOrEqual(new Date(rawQuery.start)),
            ...baseQuery,
          },
          {
            end: LessThanOrEqual(new Date(rawQuery.end)),
            ...baseQuery,
          },
        ];
      } else {
        query = baseQuery;
      }
    }

    

    const tasks = await this.commonsService.paginate(
      'tasks',
      query,
      (options as any).relations || {},
    );

    return tasks;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(@Body() body: UpdateInput): Promise<PaginateResponse<Task>> {
    const { data, changes = {}, order = ['$'] } = body;

    const response = await this.commonsService.update(
      'tasks',
      data as any,
      changes,
      order,
    );

    return secureResponse({
      data: response,
      relations: {},
    });
  }
}
