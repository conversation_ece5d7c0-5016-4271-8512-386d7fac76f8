import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, FindConditions, Repository, SaveOptions } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { Task } from './tasks.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class TasksService {
  constructor(
    @InjectRepository(Task)
    private readonly tasksRepo: Repository<Task>,
  ) {}

  async findAll(
    query: FindConditions<Task> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Task[]> {
    let tasks: Task[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'task',
        this.tasksRepo.createQueryBuilder('task'),
        relations,
        options,
      );

      tasks = await builder.where(query).getMany();
    } else {
      tasks = await this.tasksRepo.find({
        where: query,
        ...options,
      });
    }

    return tasks;
  }

  async findOne(
    query: FindConditions<Task>,
    relations: string[] = [],
  ): Promise<Task> {
    let task: Task;

    if (relations.length > 0) {
      const builder = autoResolve(
        'task',
        this.tasksRepo.createQueryBuilder('task'),
        relations,
      );

      task = await builder.where(query).getOne();
    } else {
      task = await this.tasksRepo.findOne(query);
    }

    // const task = await this.tasksRepo.findOne(query, options);
    return task;
  }

  async update(
    query: FindConditions<Task>,
    update: QueryDeepPartialEntity<Task>,
  ): Promise<true> {
    await this.tasksRepo.update(query, update);

    return true;
  }

  async save(query: DeepPartial<Task>[], options?: SaveOptions): Promise<true> {
    await this.tasksRepo.save(query, options);

    return true;
  }
}
