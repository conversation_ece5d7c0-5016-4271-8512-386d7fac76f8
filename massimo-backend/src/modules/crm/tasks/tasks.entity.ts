import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Project } from '../projects/projects.entity';

import { Chat } from '@/base/chats/chats.entity';
import { User } from '@/base/users/users.entity';

@ObjectType()
@Entity()
export class Task {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  title: string;

  @Index()
  @Column({
    nullable: true,
  })
  description: string | null;

  @Index()
  @Column()
  status: string;

  @Index()
  @Column()
  priority: string;

  @Index()
  @Column({
    default: true,
  })
  private: boolean;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  start: Date;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  end: Date;

  @Index()
  @Column({
    default: true,
  })
  customerPrivate: boolean;

  @ManyToMany(() => User, (user) => user.assigneds, {
    onDelete: 'CASCADE',
  })
  @JoinTable({
    name: 'assignees',
  })
  assignees: User[];

  @RelationId((task: Task) => task.assignees)
  assigneeIds: number[];

  @ManyToMany(() => User, {
    onDelete: 'CASCADE',
  })
  @JoinTable({
    name: 'contributors',
  })
  contributors: User[];

  @RelationId((task: Task) => task.contributors)
  contributorIds: number[];

  @ManyToOne(() => Project, (project) => project.tasks, { onDelete: 'CASCADE' })
  project: Project;

  @RelationId((task: Task) => task.project)
  projectId: number;

  @Column('jsonb', {
    nullable: true,
  })
  recurrence?: { cron: string; waitForDeadline: boolean }[];

  @Column('jsonb', {
    default: [],
  })
  contents: any[];

  @OneToOne(() => Chat, (chat) => chat.task, { onDelete: 'CASCADE' })
  chat: Chat;

  @RelationId((task: Task) => task.chat)
  chatId: number;

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
