import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Project } from '../projects/projects.entity';
// eslint-disable-next-line import/no-cycle
import { Request } from '../requests/requests.entity';

import { Chat } from '@/base/chats/chats.entity';

@ObjectType()
@Entity()
export class Offer {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  name: string;

  @Index()
  @Column()
  description: string;

  @Index()
  @Column()
  budget: number;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  start: Date;

  @Index()
  @Column({ type: 'timestamp without time zone', default: new Date() })
  end: Date;

  @Index()
  @Column()
  status: string;

  @Index()
  @Column({
    nullable: true,
  })
  cancellationReason: string | null;

  @ManyToOne(() => Project, (project) => project.offers, { onDelete: 'CASCADE' })
  project: Project;

  @RelationId((offer: Offer) => offer.project)
  projectId: number[];

  @OneToOne(() => Chat, (chat) => chat.offer, { onDelete: 'CASCADE' })
  chat: Chat;

  @RelationId((offer: Offer) => offer.chat)
  chatId: number;

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
