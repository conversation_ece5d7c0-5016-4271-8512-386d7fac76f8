import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, FindConditions, Repository, SaveOptions } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { Offer } from './offers.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class OffersService {
  constructor(
    @InjectRepository(Offer)
    private readonly offersRepo: Repository<Offer>,
  ) {}

  async findAll(
    query: FindConditions<Offer> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Offer[]> {
    let offers: Offer[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'offer',
        this.offersRepo.createQueryBuilder('offer'),
        relations,
        options,
      );

      offers = await builder.where(query).getMany();
    } else {
      offers = await this.offersRepo.find({
        where: query,
        ...options,
      });
    }

    return offers;
  }

  async findOne(
    query: FindConditions<Offer>,
    relations: string[] = [],
  ): Promise<Offer> {
    let offer: Offer;

    if (relations.length > 0) {
      const builder = autoResolve(
        'offer',
        this.offersRepo.createQueryBuilder('offer'),
        relations,
      );

      offer = await builder.where(query).getOne();
    } else {
      offer = await this.offersRepo.findOne(query);
    }

    // const offer = await this.offersRepo.findOne(query, options);
    return offer;
  }

  async update(
    query: FindConditions<Offer>,
    update: QueryDeepPartialEntity<Offer>,
  ): Promise<true> {
    await this.offersRepo.update(query, update);

    return true;
  }

  async save(
    query: DeepPartial<Offer>[],
    options?: SaveOptions,
  ): Promise<true> {
    await this.offersRepo.save(query, options);

    return true;
  }
}
