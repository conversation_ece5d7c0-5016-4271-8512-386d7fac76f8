import { ApiProperty } from '@nestjs/swagger';
import {
  IsA<PERSON>y,
  IsBoolean,
  IsDate,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

import { Offer } from '../../offers.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData
  implements Partial<Omit<Offer, 'id' | 'start' | 'end' | 'projectId'>>
{
  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  id: number | string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  budget: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  start: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  end: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  status: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  cancellationReason: string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  projectId: number;
}
