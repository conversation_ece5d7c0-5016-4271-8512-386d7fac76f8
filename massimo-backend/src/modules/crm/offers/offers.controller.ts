import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { Offer } from './offers.entity';
import { OffersService } from './offers.service';

import { CommonsService, PaginateResponse } from '@/common/common.service';

@Controller('api/crm/offers')
export class OffersController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly offersService: OffersService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Offer>> {
    const { query, options, page, limit } = data;

    const offers = await this.commonsService.paginate(
      'offers',
      query,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return offers;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(@Body() body: UpdateInput): Promise<true> {
    const { data, changes = {}, order = ['$'] } = body;

    await this.commonsService.update('offers', data as any, changes, order);

    return true;
  }
}
