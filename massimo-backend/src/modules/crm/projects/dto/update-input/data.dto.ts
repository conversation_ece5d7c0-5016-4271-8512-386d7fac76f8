import { ApiProperty } from '@nestjs/swagger';
import {
  IsA<PERSON>y,
  IsBoolean,
  IsDate,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData {
  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  id: number | string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  priority: string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  offerIds: (number | string)[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  departmentIds: (number | string)[];

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  requestId: number | string;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  allowedUserIds: (number | string)[];

  @ApiProperty()
  @IsArray()
  @IsOptional()
  restrictedUserIds: (number | string)[];
}
