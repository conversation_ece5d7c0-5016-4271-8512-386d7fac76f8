import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Offer } from '../offers/offers.entity';
// eslint-disable-next-line import/no-cycle
import { Request } from '../requests/requests.entity';
// eslint-disable-next-line import/no-cycle
import { Task } from '../tasks/tasks.entity';

import { Department } from '@/base/departments/departments.entity';
import { User } from '@/base/users/users.entity';

@ObjectType()
@Entity()
export class Project {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  priority: string;

  @OneToMany(() => Offer, (offer) => offer.project, { onDelete: 'CASCADE' })
  offers: Offer[];

  @RelationId((project: Project) => project.offers)
  offerIds: number[];

  @ManyToMany(() => Department, (department) => department.projects, {
    onDelete: 'CASCADE',
  })
  @JoinTable()
  departments: Department[];

  @RelationId((project: Project) => project.departments)
  departmentIds: number[];

  @OneToOne(() => Request, (request) => request.project, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  request: Request;

  @RelationId((project: Project) => project.request)
  requestId: number;

  @ManyToMany(() => User, (user) => user.allowedProjects, {
    onDelete: 'CASCADE',
  })
  @JoinTable({
    name: 'project_allowed_users',
  })
  allowedUsers: User[];

  @RelationId((project: Project) => project.allowedUsers)
  allowedUserIds: number[];

  @ManyToMany(() => User, (user) => user.restrictedProjects, {
    onDelete: 'CASCADE',
  })
  @JoinTable({
    name: 'project_restricted_users',
  })
  restrictedUsers: User[];

  @RelationId((project: Project) => project.restrictedUsers)
  restrictedUserIds: number[];

  @OneToMany(() => Task, (task) => task.project, { onDelete: 'CASCADE' })
  @JoinColumn()
  tasks: Task[];

  @RelationId((project: Project) => project.tasks)
  taskIds: number[];

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
