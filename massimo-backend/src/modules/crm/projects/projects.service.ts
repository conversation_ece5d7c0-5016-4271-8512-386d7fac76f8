import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DeepPartial, FindConditions, Repository, SaveOptions } from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';

import { Project } from './projects.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectRepository(Project)
    private readonly projectsRepo: Repository<Project>,
  ) {}

  async findAll(
    query: FindConditions<Project> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Project[]> {
    let projects: Project[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'project',
        this.projectsRepo.createQueryBuilder('project'),
        relations,
        options,
      );

      projects = await builder.where(query).getMany();
    } else {
      projects = await this.projectsRepo.find({
        where: query,
        ...options,
      });
    }

    return projects;
  }

  async findOne(
    query: FindConditions<Project>,
    relations: string[] = [],
  ): Promise<Project> {
    let project: Project;

    if (relations.length > 0) {
      const builder = autoResolve(
        'project',
        this.projectsRepo.createQueryBuilder('project'),
        relations,
      );

      project = await builder.where(query).getOne();
    } else {
      project = await this.projectsRepo.findOne(query);
    }

    // const project = await this.projectsRepo.findOne(query, options);
    return project;
  }

  async update(
    query: FindConditions<Project>,
    update: QueryDeepPartialEntity<Project>,
  ): Promise<true> {
    await this.projectsRepo.update(query, update);

    return true;
  }

  async save(
    query: DeepPartial<Project>[],
    options?: SaveOptions,
  ): Promise<true> {
    await this.projectsRepo.save(query, options);

    return true;
  }
}
