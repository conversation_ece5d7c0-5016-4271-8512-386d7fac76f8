import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { Project } from './projects.entity';
import { ProjectsService } from './projects.service';

import { CommonsService, PaginateResponse } from '@/common/common.service';

@Controller('api/crm/projects')
export class ProjectsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly projectsService: ProjectsService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Project>> {
    const { query, options = {}, page, limit } = data;

    const projects = await this.commonsService.paginate(
      'projects',
      query,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return projects;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(@Body() body: UpdateInput): Promise<Project[]> {
    const { data, changes = {}, order = ['$'] } = body;

    const response = await this.commonsService.update(
      'projects',
      data as any,
      changes,
      order,
    );

    return response;
  }
}
