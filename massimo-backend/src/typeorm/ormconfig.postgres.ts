import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { plainToClass } from 'class-transformer';
import { validateSync } from 'class-validator';

import { EnvConfig } from '../config/config.env';

import { TypeOrmNamingStrategy } from './typeorm-naming-strategy';

const env = plainToClass(
  EnvConfig,
  { ...EnvConfig.getDefaultObject(), ...process.env },
  { enableImplicitConversion: true },
);
const errors = validateSync(env, { whitelist: true });
if (errors.length > 0) {
  // eslint-disable-next-line no-console
  console.error(JSON.stringify(errors, undefined, '  '));
  throw new Error('Invalid env.');
}

const options: TypeOrmModuleOptions = {
  type: 'postgres',
  host: env.TYPEORM_HOST,
  port: env.TYPEORM_PORT,
  username: env.TYPEORM_USERNAME,
  password: env.TYPEORM_PASSWORD,
  database: env.TYPEORM_DATABASE,
  entities: [`${__dirname}/../**/*.entity.{ts,js}`],
  migrations: [`${__dirname}/../migrations/*.{ts,js}`],
  namingStrategy: new TypeOrmNamingStrategy(),
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  seeds: [`${__dirname}/../seeds/**/*{.ts,.js}`],
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  factories: [`${__dirname}/../factories/**/*{.ts,.js}`],
  logging: env.TYPEORM_LOGGING,
  cli: {
    migrationsDir: `${__dirname}/../migrations`,
  },
};

export = options;
