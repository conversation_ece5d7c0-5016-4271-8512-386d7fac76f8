import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _ = require('lodash');
import { Repository } from 'typeorm';

import { Access } from './access.entity';

import { RolesService } from '@/base/roles/roles.service';
import { User } from '@/base/users/users.entity';
import { UsersService } from '@/base/users/users.service';
import { CommonsService } from '@/common/common.service';

@Injectable()
export class AccessesService {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    @InjectRepository(Access)
    private readonly accessesRepo: Repository<Access>,
    private readonly rolesService: RolesService,
    private readonly usersService: UsersService,
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
  ) {}

  // eslint-disable-next-line consistent-return, class-methods-use-this
  async calculateAccess(id: number): Promise<AccessResponse> {
    if (!id) {
      return [];
    }

    try {
      const user = await this.commonsService.findOne(
        'users',
        {
          id,
        },
        ['users.roles', 'roles.departments'],
      );

      if (!user) {
        throw new NotFoundException();
      }

      const accessRules = await this.accessesRepo.find();

      // Super admin & admin are able to see every thing
      if (user.roles.some((role) => role.weight < 0)) {
        return accessRules.map((rule) => ({ path: rule.path, value: true }));
      }

      const response: AccessResponse = [];

      const edit = [];
      const view = [];
      const restricted = [];
      const eager = _.reduce(
        accessRules,
        (result, targetAccess) => {
          // check for
          const { defaultRoles, departmentsRoles } = targetAccess;

          const splitData = (authority) => {
            // eslint-disable-next-line default-case
            switch (authority) {
              case 1: {
                restricted.push(targetAccess);

                break;
              }
              case 2: {
                result.push(targetAccess);

                break;
              }
              case 3: {
                view.push(targetAccess);

                break;
              }
              case 4: {
                edit.push(targetAccess);

                break;
              }
              // No default
            }
          };

          _.forOwn(defaultRoles, splitData);

          _.forOwn(departmentsRoles, (department) => {
            // eslint-disable-next-line default-case
            _.forOwn(department, splitData);
          });

          return result;
        },
        [],
      );

      return response;
    } catch (error) {
      console.error(error);

      return [];
    }
  }
}

export type AccessResponse = {
  path: string;
  value: boolean;
}[];
