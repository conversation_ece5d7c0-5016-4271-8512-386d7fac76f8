import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';

import { UpdateInputData } from './update-input/data.dto';

export class UpdateInput {
  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  @Type(() => UpdateInputData)
  @ValidateNested({ each: true })
  readonly data: UpdateInputData[];
}
