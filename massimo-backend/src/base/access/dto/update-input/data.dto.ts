import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmptyObject, IsObject, MinLength } from 'class-validator';

import { Access } from '../../access.entity';

export class UpdateInputData implements Partial<Access> {
  @ApiProperty()
  @MinLength(1)
  readonly name: string;

  @ApiProperty()
  @MinLength(1)
  readonly path: string;

  @ApiProperty()
  @IsObject()
  @IsNotEmptyObject()
  readonly defaultRoles: Record<string, number>;

  @ApiProperty()
  @IsObject()
  @IsNotEmptyObject()
  readonly departmentsRoles: Record<string, Record<string, number>>;
}
