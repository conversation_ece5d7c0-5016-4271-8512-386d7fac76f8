import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsObject, ValidateNested } from 'class-validator';

import { DeleteInputData } from './data.dto';

import { UpdateTemplate } from '@/utils/update/update.dto';

export class DeleteInput extends UpdateTemplate {
  @ApiProperty()
  @Type(() => DeleteInputData)
  @IsObject()
  readonly data: DeleteInputData;
}
