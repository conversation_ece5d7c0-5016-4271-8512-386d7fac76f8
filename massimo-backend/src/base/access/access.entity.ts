import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  Unique,
  VersionColumn,
} from 'typeorm';

@Entity()
export class Access {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  name: string;

  @Index({ unique: true })
  @Column()
  path: string;

  @Column('jsonb', {
    default: {},
  })
  defaultRoles: Record<string, number>;

  @Column('jsonb', {
    default: {},
  })
  departmentsRoles: Record<string, Record<string, number>>;

  @Index()
  @Column('boolean', {
    default: false,
  })
  value: boolean;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
