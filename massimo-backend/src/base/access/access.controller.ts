import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Delete,
  Get,
  Inject,
  Param,
  Patch,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Access } from './access.entity';
import { AccessResponse, AccessesService } from './access.service';
import { DeleteInput } from './dto/delete-input/delete-input.dto';
import { UpdateInput } from './dto/update-input.dto';

import { Roles } from '@/base/roles/guards/roles.decorator';
import { RolesGuard } from '@/base/roles/guards/roles.guard';
import { CommonsService } from '@/common/common.service';

@Controller('api/access')
export class AccessesController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly accessesService: AccessesService,
    @InjectRepository(Access)
    private readonly accessesRepo: Repository<Access>,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Get(':id')
  async addRole(@Param('id') id: number): Promise<AccessResponse> {
    const result = await this.accessesService.calculateAccess(id);

    return result;
  }

  @Roles('admin')
  @UseGuards(AuthGuard(), RolesGuard)
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Patch('update')
  // eslint-disable-next-line class-methods-use-this
  async update(@Body() data: UpdateInput): Promise<true> {
    this.accessesRepo.save(data.data);

    return true;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Delete('delete')
  // eslint-disable-next-line class-methods-use-this
  async delete(@Body() body: DeleteInput): Promise<true> {
    // this.accessesRepo.save(data.data);

    await this.commonsService.delete(body.data.target as any, {
      id: body.data.id as any,
    });

    return true;
  }
}
