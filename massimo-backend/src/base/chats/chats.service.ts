import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { Chat } from './chats.entity';

import { CommonsService } from '@/common/common.service';

@Injectable()
export class ChatsService {
  constructor(
    @InjectRepository(Chat)
    private readonly chatsRepo: Repository<Chat>,
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
  ) {}

  // eslint-disable-next-line class-methods-use-this
  async checkChat(
    sourceId: number,
    targetId: number,
    type: string,
    inactive = true,
  ): Promise<void> {
    if (type === 'person') {
      const availChatPaginate = await this.commonsService.paginate('chats', [
        {
          owner: sourceId as any,
          person: targetId as any,
        },
        {
          owner: targetId as any,
          person: sourceId as any,
        },
      ]);

      const availChat = availChatPaginate.data[0];

      // eslint-disable-next-line unicorn/prefer-ternary
      if (!availChat) {
        await this.commonsService.update('chats', [
          {
            ownerId: sourceId,
            personId: targetId,
            type: 'person',
            inactive: false,
          },
        ]);
      } else {
        await this.commonsService.update('chats', [
          {
            id: availChat.id,
            inactive,
          },
        ]);
      }

      // this.commonsService.update();
    }
  }
}
