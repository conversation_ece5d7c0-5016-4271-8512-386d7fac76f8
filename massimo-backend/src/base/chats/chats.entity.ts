import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Customer } from '../customers/customers.entity';
// eslint-disable-next-line import/no-cycle
import { Message } from '../messages/messages.entity';
// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

import { Offer } from '@/modules/crm/offers/offers.entity';
import { Task } from '@/modules/crm/tasks/tasks.entity';

@ObjectType()
@Entity()
export class Chat {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({
    fulltext: true,
  })
  @Column()
  type: string;

  @Index()
  @Column('boolean', {
    default: false,
  })
  inactive: boolean;

  @OneToOne(() => Offer, (offer) => offer.chat, { onDelete: 'CASCADE' })
  @JoinColumn()
  offer: Offer;

  @RelationId((chat: Chat) => chat.offer)
  offerId: number;

  @OneToOne(() => Customer, (customer) => customer.chat, { onDelete: 'CASCADE' })
  @JoinColumn()
  organization: Customer;

  @RelationId((chat: Chat) => chat.organization)
  organizationId: number;

  @ManyToOne(() => User, (person) => person.chats, { onDelete: 'CASCADE' })
  person: User;

  @RelationId((chat: Chat) => chat.person)
  personId: number;

  @ManyToOne(() => User, (person) => person.ownedChats, { onDelete: 'CASCADE' })
  owner: User;

  @RelationId((chat: Chat) => chat.owner)
  ownerId: number;

  @OneToOne(() => Task, (task) => task.chat, { onDelete: 'CASCADE' })
  @JoinColumn()
  task: Task;

  @RelationId((chat: Chat) => chat.task)
  taskId: number;

  @Column('jsonb', {
    default: {},
  })
  options: Record<string, any>;

  @OneToMany(() => Message, (message) => message.chat, { onDelete: 'CASCADE' })
  messages: Message[];

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
