import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

import { Chat } from '../../chats.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData
  implements
    Partial<
      Omit<
        Chat,
        'taskId' | 'ownerId' | 'personId' | 'organizationId' | 'offerId'
      >
    >
{
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type: string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  taskId: number | string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  ownerId: number | string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  personId: number | string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  organizationId: number | string;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  offerId: number | string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  options?: Record<string, any>;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  defaultRoleId: number[];
}
