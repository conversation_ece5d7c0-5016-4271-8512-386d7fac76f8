import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');

import { Chat } from './chats.entity';
import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';

import { RolesService } from '@/base/roles/roles.service';
import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/chats')
export class ChatsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Chat>> {
    const { query, options = {}, page, limit } = data;

    const chats = await this.commonsService.paginate(
      'chats',
      query as Chat,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return secureResponse(chats);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateChats(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<true> {
    const user = req.user as Chat;
    const { data, changes = {}, order = ['$'] } = body;

    if (!_.every(data, (item) => item.id === user.id)) {
      const { ableTo } = await this.rolesService.calculatePerm(user.id, [
        'super-admin',
        'admin',
      ]);

      if (ableTo.length === 0) {
        throw new UnauthorizedException();
      }
    }

    await this.commonsService.update(
      'chats',
      _.map(data, (item) => {
        const res = _.cloneDeep(item);

        if (res.id) {
          res.id = +res.id;
        }

        return res;
      }) as any,
      changes,
      order,
    );

    return true;
  }
}
