import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  MinLength,
} from 'class-validator';

import { Department } from '../../departments.entity';

export class UpdateInputData implements Partial<Department> {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsOptional()
  @MinLength(1)
  name: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly label: string;

  @ApiProperty()
  @IsOptional()
  readonly description: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly color: string;

  @ApiProperty()
  @IsArray()
  userIds: number[];

  @ApiProperty()
  @IsArray()
  roleIds: number[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  readonly isVisible?: boolean;
}
