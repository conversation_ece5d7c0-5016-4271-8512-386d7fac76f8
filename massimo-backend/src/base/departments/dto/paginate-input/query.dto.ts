import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlphanumeric,
  IsNumber,
  IsOptional,
  MinLength,
} from 'class-validator';

import { Department } from '../../departments.entity';

export class Query implements Partial<Department> {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  readonly id: number;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly search: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly label: string;
}
