import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { In, Repository } from 'typeorm';

import { Department } from './departments.entity';
import { DepartmentsService } from './departments.service';
import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';

import { Role } from '@/base/roles/roles.entity';
import { RolesService } from '@/base/roles/roles.service';
import { User } from '@/base/users/users.entity';
import { UsersService } from '@/base/users/users.service';
import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/departments')
export class DepartmentsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly departmentsService: DepartmentsService,
    @InjectRepository(Department)
    private readonly departmentsRepo: Repository<Department>,
    @InjectRepository(Role)
    private readonly rolesRepo: Repository<Role>,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
    @Inject(UsersService)
    private readonly usersService: UsersService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Department>> {
    const { query, options = {}, page, limit } = data;

    const departments = await this.commonsService.paginate(
      'departments',
      query as Department,
      (options as any).relations || [],
      {
        skip: page * limit,
        take: limit,
      },
    );

    return secureResponse(departments);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<true> {
    const user = req.user as User;
    const { data, changes, order } = body;

    if (!_.every(data, (item) => item.id === user.id)) {
      const { ableTo } = await this.rolesService.calculatePerm(user.id, [
        'super-admin',
        'admin',
      ]);

      if (ableTo.length === 0) {
        throw new UnauthorizedException();
      }
    }

    await this.commonsService.update(
      'departments',
      _.map(data, (item) => {
        const res = _.cloneDeep(item);

        if (res.id) {
          res.id = +res.id;
        }

        return res;
      }),
      changes,
      order,
    );

    return true;
  }

  // @UseGuards(AuthGuard())
  // @ApiBearerAuth()
  // @UseInterceptors(ClassSerializerInterceptor)
  // @Post('update')
  // // eslint-disable-next-line class-methods-use-this
  // async updateUser(
  //   @Req() req: Request,
  //   @Body() body: UpdateInput,
  // ): Promise<true> {
  //   const { target, data } = body;
  //   const user = req.user as User;

  //   if (target !== user.id) {
  //     const havePerm = await this.rolesService.calculatePerm(user.id, [
  //       'super-admin',
  //       'admin',
  //     ]);
  //     if (havePerm.ableTo.length > 0) {
  //       throw new UnauthorizedException();
  //     }
  //   }

  //   (data as Department).name = _.join(
  //     _.split(_.toLower(data.label), ' '),
  //     '-',
  //   );

  //   (data as Department).roles = await this.rolesService.findAll({
  //     id: In(data.roleIds),
  //   });

  //   (data as Department).users = await this.commonsService.findAll('users', {
  //     id: In(data.userIds),
  //   });

  //   // eslint-disable-next-line unicorn/prefer-ternary
  //   if (!target) {
  //     await this.departmentsRepo.save(_.omit(data, ['id']));
  //   } else {
  //     await this.departmentsRepo.save(_.omit(data, ['password']));
  //   }

  //   return true;
  // }
}
