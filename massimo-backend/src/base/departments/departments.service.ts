import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindConditions, Repository } from 'typeorm';

import { Department } from './departments.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class DepartmentsService {
  constructor(
    @InjectRepository(Department)
    private readonly departmentsRepo: Repository<Department>,
  ) {}

  async findAll(
    query: FindConditions<Department> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Department[]> {
    let departments: Department[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'departments',
        this.departmentsRepo.createQueryBuilder('departments'),
        relations,
        options,
      );

      departments = await builder.where(query).getMany();
    } else {
      departments = await this.departmentsRepo.find({
        where: query,
        ...options,
      });
    }

    return departments;
  }

  async findOne(
    query: FindConditions<Department>,
    relations: string[] = [],
  ): Promise<Department> {
    let user: Department;

    if (relations.length > 0) {
      const builder = autoResolve(
        'departments',
        this.departmentsRepo.createQueryBuilder('departments'),
        relations,
      );

      user = await builder.where(query).getOne();
    } else {
      user = await this.departmentsRepo.findOne(query);
    }

    // const user = await this.departmentsRepo.findOne(query, options);
    return user;
  }
}
