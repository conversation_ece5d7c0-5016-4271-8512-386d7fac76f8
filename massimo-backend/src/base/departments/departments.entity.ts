import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

import { Role } from '@/base/roles/roles.entity';
import { User } from '@/base/users/users.entity';
import { Project } from '@/modules/crm/projects/projects.entity';

@ObjectType()
@Entity()
export class Department {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({ unique: true })
  @Column()
  name: string;

  @Index()
  @Column()
  label: string;

  @Index()
  @Column({
    default: '',
  })
  description: string;

  @Index()
  @Column({
    default: false,
  })
  isVisible: boolean;

  @Index()
  @Column({
    default: '#1864AB',
  })
  color: string;

  @OneToMany(() => Role, (role) => role.department, { onDelete: 'CASCADE' })
  @JoinColumn()
  roles: Role[];

  @RelationId((departments: Department) => departments.roles)
  roleIds: number[];

  @ManyToMany(() => User, (user) => user.departments, { onDelete: 'CASCADE' })
  @JoinTable()
  users: User[];

  @RelationId((departments: Department) => departments.users)
  userIds: number[];

  @ManyToMany(() => Project, (project) => project.departments, { onDelete: 'CASCADE' })
  projects: Project[];

  @RelationId((department: Department) => department.projects)
  projectIds: number[];

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
