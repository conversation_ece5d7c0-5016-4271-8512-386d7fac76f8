import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { DepartmentsController } from './departments.controller';
import { Department } from './departments.entity';
import { DepartmentsService } from './departments.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { Role } from '@/base/roles/roles.entity';
import { RolesModule } from '@/base/roles/roles.module';
import { UsersModule } from '@/base/users/users.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Department, Role]),
    forwardRef(() => CommonsModule),
    forwardRef(() => RolesModule),
    forwardRef(() => UsersModule),
  ],
  controllers: [DepartmentsController],
  providers: [DepartmentsService],
  exports: [DepartmentsService],
})
export class DepartmentsModule {}
