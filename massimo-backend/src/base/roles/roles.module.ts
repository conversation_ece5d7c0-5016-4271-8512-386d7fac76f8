import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { RolesController } from './roles.controller';
import { Role } from './roles.entity';
import { RolesService } from './roles.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { DepartmentsModule } from '@/base/departments/departments.module';
import { UsersModule } from '@/base/users/users.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Role]),
    forwardRef(() => CommonsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => DepartmentsModule),
  ],
  controllers: [RolesController],
  providers: [RolesService],
  exports: [RolesService],
})
export class RolesModule {}
