import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  ArrayNotEmpty,
  IsArray,
  IsNotEmpty,
  ValidateNested,
} from 'class-validator';

import { UpdateInputData } from './data.dto';

import { UpdateTemplate } from '@/utils/update/update.dto';

export class UpdateInput extends UpdateTemplate {
  @ApiProperty()
  @IsArray()
  @IsNotEmpty()
  @ArrayNotEmpty()
  @Type(() => UpdateInputData)
  @ValidateNested({ each: true })
  readonly data: UpdateInputData[];
}
