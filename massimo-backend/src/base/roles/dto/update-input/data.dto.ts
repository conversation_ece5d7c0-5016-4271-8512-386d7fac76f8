import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsN<PERSON>ber,
  IsOptional,
  Min,
  MinLength,
} from 'class-validator';

import { Role } from '../../roles.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData implements Omit<Partial<Role>, "departmentId">  {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  readonly id: number;

  @ApiProperty()
  @IsOptional()
  @MinLength(1)
  name: string;

  @ApiProperty()
  @IsOptional()
  @MinLength(1)
  label: string;

  @ApiProperty()
  @IsOptional()
  @Min(0)
  weight: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isDefaultRole: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isDepartmentRole: boolean;

  @ApiProperty()
  @IsOptional()
  @IsType(["number", "string"])
  departmentId: number | string;
}
