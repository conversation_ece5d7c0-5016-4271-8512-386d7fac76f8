import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import _ = require('lodash');
import { FindConditions, Repository } from 'typeorm';

import { UsersService } from '../users/users.service';

import { Role } from './roles.entity';

import { User } from '@/base/users/users.entity';
import { CommonsService } from '@/common/common.service';
import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class RolesService {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    @InjectRepository(Role)
    private readonly rolesRepo: Repository<Role>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
  ) {}

  async findAll(
    query: FindConditions<Role> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Role[]> {
    let roles: Role[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'roles',
        this.rolesRepo.createQueryBuilder('roles'),
        relations,
        options,
      );

      roles = await builder.where(query).getMany();
    } else {
      roles = await this.rolesRepo.find({
        where: query,
        ...options,
      });
    }

    return roles;
  }

  async findOne(
    query: FindConditions<Role>,
    relations: string[] = [],
  ): Promise<Role> {
    let role: Role;

    if (relations.length > 0) {
      const builder = autoResolve(
        'roles',
        this.rolesRepo.createQueryBuilder('roles'),
        relations,
      );

      role = await builder.where(query).getOne();
    } else {
      role = await this.rolesRepo.findOne(query);
    }

    // const role = await this.rolesRepo.findOne(query, options);
    return role;
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/explicit-module-boundary-types
  async generatePermResponse(perms: string[], ableTo: string[]) {
    const response: {
      ableTo: string[];
      notAbleTo: string[];
      passing: boolean;
    } = {
      ableTo,
      notAbleTo: _.filter(perms, (perm) => !_.includes(ableTo, perm)),
      passing: perms.length === ableTo.length,
    };

    return response;
  }

  async calculatePerm(
    id: number | User,
    perms: string[] = [],
  ): Promise<ReturnType<RolesService['generatePermResponse']>> {
    const user: User =
      typeof id === 'number'
        ? await this.commonsService.findOne('users', { id }, [
            'users.roles',
            'users.defaultRole',
            'roles.department',
          ])
        : id;

    if (user.defaultRole[0]) {
      user.roles.push(user.defaultRole[0]);
    }

    const bindedPermResponse: (
      ableTo: Parameters<typeof this.generatePermResponse>['1'],
    ) => ReturnType<RolesService['generatePermResponse']> =
      this.generatePermResponse.bind(this, perms);

    if (!user) {
      // eslint-disable-next-line no-return-await
      return await bindedPermResponse([]);
    }
    
    const isAdmin = _.some(user.roles, (role) => role.weight === -1);
    const isSuperAdmin = _.some(user.roles, (role) => role.weight === -2);

    if (isSuperAdmin) {
      // eslint-disable-next-line no-return-await
      return await bindedPermResponse(perms);
    }

    let response = await bindedPermResponse([]);

    if (perms.length > 0) {
      const targetPerms = await this.rolesRepo
        .createQueryBuilder('role')
        .leftJoinAndSelect('role.department', 'departments')
        .where('role.name IN (:...perms)', {
          perms,
        })
        .getMany();

      // Check for perms if its exists in user
      const notContained: Role[] = [];
      const notAbleTo: Role[] = [];
      const contained = _.reduce(
        targetPerms,
        (result, targetRole) => {
          const finded = _.find(
            user.roles,
            (userRole) => userRole.name === targetRole.name,
          );

          if (
            finded &&
            (user.roleValues[finded.departmentId] || [])[finded.id] === true
          ) {
            result.push(targetRole);
          } else if (
            finded &&
            (user.roleValues[finded.departmentId] || [])[finded.id] === false
          ) {
            notAbleTo.push(targetRole);
          } else {
            notContained.push(targetRole);
          }

          return result;
        },
        [],
      );

      // allow if all roles are approved and match each other
      if (contained.length === targetPerms.length) {
        response = await bindedPermResponse(perms);
      } else if (isAdmin) {
        response =
          notAbleTo.length === 0
            ? await bindedPermResponse(perms)
            : await bindedPermResponse(
                _.map(
                  _.filter(targetPerms, (role) =>
                    _.some(
                      notAbleTo,
                      (restricredRole) => restricredRole.name === role.name,
                    ),
                  ),
                  (role) => role.name,
                ),
              );
      } else {
        // In else case check for weights in responding types (departmentRole or default role)
        const defaultRole = _.find(user.roles, (role) => role.isDefaultRole);

        const canOverride: string[] = _.reduce(
          notContained,
          (result, role) => {
            if (defaultRole && defaultRole.weight >= role.weight) {
              result.push(role.name);
            } else if (!defaultRole && !role.isDefaultRole) {
              const sameDepartment = _.filter(
                user.roles,
                (userRole) =>
                  userRole.department &&
                  role.department &&
                  userRole.department.id === role.department.id,
              );

              if (
                sameDepartment.length > 0 &&
                _.some(
                  sameDepartment,
                  (sameRole) => sameRole.weight >= role.weight,
                )
              ) {
                result.push(role.name);
              }
            }

            return result;
          },
          [],
        );

        response = await bindedPermResponse(canOverride);
      }
    }

    return response;
  }
}
