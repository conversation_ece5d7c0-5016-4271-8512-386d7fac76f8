import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

import { Department } from '@/base/departments/departments.entity';

@ObjectType()
@Entity()
export class Role {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  name: string;

  @Index()
  @Column()
  label: string;

  @Index()
  @Column({
    default: 0,
  })
  weight: number;

  @Index()
  @Column({
    default: false,
  })
  isDefaultRole: boolean;

  @Index()
  @Column({
    default: true,
  })
  isDepartmentRole: boolean;

  @ManyToOne(() => Department, (department) => department.roles, { onDelete: 'CASCADE' })
  department: Department;

  @RelationId((roles: Role) => roles.department)
  departmentId: number;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
