import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';

import { User } from '@/base/users/users.entity';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const roles = this.reflector.get<string[]>('roles', context.getHandler());
    if (!roles) {
      return true;
    }
    const request = context.switchToHttp().getRequest();
    const user = request.user as User;

    if (!user) {
      throw new UnauthorizedException();
    }

    let containsAll = true;

    if (user.roles.some((role) => role.weight === -2)) {
      return true;
    }

    roles.forEach((role) => {
      if (!user.roles.some((userRole) => userRole.name === role)) {
        containsAll = false;
      }
    });

    if (!containsAll) {
      throw new UnauthorizedException();
    }

    return containsAll;
  }
}
