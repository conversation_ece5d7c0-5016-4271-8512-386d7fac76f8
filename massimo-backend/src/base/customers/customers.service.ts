import { Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindConditions, Repository } from 'typeorm';

import { UsersService } from '../users/users.service';

import { Customer } from './customers.entity';

import { AutoResolveOptions, autoResolve } from '@/utils/auto-resolve.util';

@Injectable()
export class CustomersService {
  constructor(
    @InjectRepository(Customer)
    private readonly customersRepo: Repository<Customer>,
    @Inject(UsersService)
    private readonly usersService: UsersService,
  ) {}

  async findAll(
    query: FindConditions<Customer> = {},
    relations: string[] = [],
    options: AutoResolveOptions = {},
  ): Promise<Customer[]> {
    let customers: Customer[];

    if (relations.length > 0) {
      const builder = autoResolve(
        'customers',
        this.customersRepo.createQueryBuilder('customers'),
        relations,
        options,
      );

      customers = await builder.where(query).getMany();
    } else {
      customers = await this.customersRepo.find({
        where: query,
        ...options,
      });
    }

    return customers;
  }

  async findOne(
    query: FindConditions<Customer>,
    relations: string[] = [],
  ): Promise<Customer> {
    let customer: Customer;

    if (relations.length > 0) {
      const builder = autoResolve(
        'customers',
        this.customersRepo.createQueryBuilder('customers'),
        relations,
      );

      customer = await builder.where(query).getOne();
    } else {
      customer = await this.customersRepo.findOne(query);
    }

    // const customer = await this.customersRepo.findOne(query, options);
    return customer;
  }
}
