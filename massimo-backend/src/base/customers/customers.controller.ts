import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');

import { RolesService } from '../roles/roles.service';
import { User } from '../users/users.entity';

import { Customer } from './customers.entity';
import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';

import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';



@Controller('api/customers')
export class CustomersController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Customer>> {
    const { query, options = {}, page, limit } = data;

    const customers = await this.commonsService.paginate(
      'customers',
      query as Customer,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return secureResponse(customers);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateUser(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<true> {
    const user = req.user as User;
    const { data, changes = {}, order = ['$'] } = body;

    if (!_.every(data, (item) => item.id === user.id)) {
      const { ableTo } = await this.rolesService.calculatePerm(user.id, [
        'super-admin',
        'admin',
      ]);

      if (ableTo.length === 0) {
        throw new UnauthorizedException();
      }
    }

    await this.commonsService.update(
      'customers',
      data,
      changes,
      order,
    );

    return true;
  }
}
