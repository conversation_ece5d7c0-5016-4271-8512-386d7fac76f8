import { Field, InputType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

import { Query } from './query.dto';

import { PaginateTemplate } from '@/utils/paginate/paginate.dto';

export class PaginateInput extends PaginateTemplate {
  @ApiProperty()
  @Type(() => Query)
  @ValidateNested({ each: true })
  readonly query: Query;
}
