import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlphanumeric,
  IsBoolean,
  IsOptional,
  MinLength,
} from 'class-validator';

import { Customer } from '../../customers.entity';

import { IsType } from '@/utils/is-type.util';

export class Query
  implements
    Partial<
      Omit<Customer, 'id'> & {
        id: number[] | number;
      }
    >
{
  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly id: number | number[];

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly search: string;
}
