import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { IsOptional } from 'class-validator';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Chat } from '../chats/chats.entity';
// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

import { Request } from '@/modules/crm/requests/requests.entity';

@ObjectType()
@Entity()
export class Customer {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({
    unique: true,
  })
  @Column()
  shortName: string;

  @Index()
  @Column()
  fullName: string;

  @Index()
  @Column()
  location: string;

  @Index()
  @Column()
  phone: string;

  @Index({
    unique: true,
  })
  @Column()
  email: string;

  @Index()
  @Column()
  taxDepartment: string;

  @Index()
  @Column()
  taxNumber: string;

  @Index()
  @Column()
  address: string;

  @ManyToMany(() => User, (user) => user.organizations, { onDelete: 'CASCADE' })
  @JoinTable()
  persons: User[];

  @RelationId((customers: Customer) => customers.persons)
  personIds: number[];

  @OneToMany(() => Request, (request) => request.customer, { onDelete: 'CASCADE' })
  @JoinColumn()
  requests: Request[];

  @RelationId((customer: Customer) => customer.requests)
  requestIds: number[];

  @OneToOne(() => Chat, (chat) => chat.organization, { onDelete: 'CASCADE' })
  chat: Chat;

  @RelationId((customer: Customer) => customer.chat)
  chatId: number;

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
