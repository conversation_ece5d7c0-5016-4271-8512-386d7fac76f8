import {
  <PERSON>umn,
  CreateDateC<PERSON>umn,
  <PERSON>tity,
  <PERSON>in<PERSON><PERSON>umn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Customer } from '../customers/customers.entity';
// eslint-disable-next-line import/no-cycle
import { File } from '../files/files.entity';
// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

@Entity()
export class Post {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Column({
    default: 'post',
  })
  type: string;

  @Column({
    nullable: true,
  })
  description?: string;

  @OneToMany(() => File, (file) => file.post, {
    cascade: true,
  })
  files: File[];

  @RelationId((post: Post) => post.files)
  fileIds?: number[];

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  user?: User;

  @RelationId((post: Post) => post.user)
  userId?: number;

  @ManyToOne(() => Customer, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  organization?: Customer;

  @RelationId((post: Post) => post.organization)
  organizationId?: number;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // likes: number[];
  // comments?: CommentType[];
  // views?: number[];
}
