import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { Comment } from './comments.entity';
import { Postmeta } from './postmeta.entity';
import { PostsController } from './posts.controller';
import { Post } from './posts.entity';
import { PostsService } from './posts.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { RolesModule } from '@/base/roles/roles.module';
import { User } from '@/base/users/users.entity';
import { UsersModule } from '@/base/users/users.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Post, User, Comment, Postmeta]),
    forwardRef(() => CommonsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => RolesModule),
  ],
  controllers: [PostsController],
  providers: [PostsService],
  exports: [PostsService],
})
export class PostsModule {}
