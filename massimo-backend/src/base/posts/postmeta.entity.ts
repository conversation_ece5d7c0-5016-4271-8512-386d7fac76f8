import {
  <PERSON>umn,
  CreateDateColumn,
  <PERSON>tity,
  <PERSON>inC<PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';

import { User } from '../users/users.entity';

import { Post } from './posts.entity';

@Entity()
export class Postmeta {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Column({
    default: false,
  })
  isLike: boolean;

  @Column({
    default: true,
  })
  isView: boolean;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: "CASCADE"
  })
  @JoinColumn()
  user?: User;

  @RelationId((comment: Postmeta) => comment.user)
  userId?: number;

  @ManyToOne(() => Post, { onDelete: 'CASCADE' })
  @JoinColumn()
  post: Post;

  @RelationId((comment: Postmeta) => comment.post)
  postId?: number;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
