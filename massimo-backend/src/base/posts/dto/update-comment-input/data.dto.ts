import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { Comment } from '../../comments.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData implements Partial<Comment> {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  user?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  post?: number;
}
