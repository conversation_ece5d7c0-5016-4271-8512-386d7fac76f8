import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';

import { Postmeta } from '../../postmeta.entity';

export class UpdateInputData implements Partial<Postmeta> {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isLike?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isView?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  user?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  post?: number;
}
