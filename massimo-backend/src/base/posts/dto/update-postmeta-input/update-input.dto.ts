import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayNotEmpty, ValidateNested } from 'class-validator';

import { UpdateInputData } from './data.dto';

import { UpdateTemplate } from '@/utils/update/update.dto';

export class UpdatePostmetaInput extends UpdateTemplate {
  @ApiProperty()
  @Type(() => UpdateInputData)
  @ValidateNested({ each: true })
  @ArrayNotEmpty()
  readonly data: UpdateInputData[];
}
