import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { Post } from '../../posts.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData implements Partial<Post> {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  type: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty()
  @IsOptional()
  @IsType(['numberArray'])
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  files?: number[];

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  user?: number;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  organization?: number;
}
