import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';

import { User } from '../users/users.entity';

import { Post } from './posts.entity';

@Entity()
export class Comment {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Column({
    nullable: true,
  })
  description?: string;

  @ManyToOne(() => User, {
    nullable: true,
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  user?: User;

  @RelationId((comment: Comment) => comment.user)
  userId?: number;

  @ManyToOne(() => Post, { onDelete: 'CASCADE' })
  @JoinColumn()
  post: Post;

  @RelationId((comment: Comment) => comment.post)
  postId?: number;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
