import { Inject, Injectable } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import _ = require('lodash');
import { DateTime } from 'luxon';
import { In, IsNull, <PERSON><PERSON>han, LessThanOrEqual, Not } from 'typeorm';

import { User } from '../users/users.entity';

import { Post } from './posts.entity';

import { CommonsService, PaginateResponse } from '@/common/common.service';

@Injectable()
export class PostsService {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
  ) {}

  async getMetas(
    postsPromise: Promise<Post[]>,
    activeUser: User,
  ): Promise<Post[]> {
    const posts = await postsPromise;

    const response = await Promise.all(
      posts.map(async (x, i) => {
        const [likeCount, [isLiked, isViewed], commentCount] =
          await Promise.all([
            this.commonsService.count('postmetas', {
              isLike: true,
              isView: false,
              post: x.id as any,
            }),
            (async () => {
              const metas = await this.commonsService.findAll('postmetas', {
                post: x.id as any,
                user: activeUser.id as any,
              });

              const findedLike = metas.find((meta) => meta.isLike);
              const findedView = metas.find((meta) => meta.isView);

              return [
                findedLike ? findedLike.id : null,
                findedView ? findedView.id : null,
              ];
            })(),
            x.type === 'post'
              ? this.commonsService.count('comments', {
                  post: x.id as any,
                })
              : 0,
          ]);

        if (x.userId === activeUser.id) {
          // eslint-disable-next-line @typescript-eslint/ban-ts-comment
          // @ts-ignore
          // eslint-disable-next-line no-param-reassign
          x.viewCount = await this.commonsService.count('postmetas', {
            isLike: false,
            isView: true,
            post: x.id as any,
          });
        }

        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        // eslint-disable-next-line no-param-reassign
        x.commentCount = commentCount;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        // eslint-disable-next-line no-param-reassign
        x.likeCount = likeCount;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        // eslint-disable-next-line no-param-reassign
        x.isViewed = isViewed;
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        // eslint-disable-next-line no-param-reassign
        x.isLiked = isLiked;

        return x;
      }),
    );

    return response;
  }

  async getOrganization(
    organizationId: number,
    page: number,
  ): Promise<PaginateResponse<Post>> {
    const response = await this.commonsService.paginate(
      'posts',
      [
        {
          type: 'post',
        },
      ],
      {},
      {
        desc: true,
        skip: 20 * page,
        take: 20,
      },
      ['organization', organizationId],
    );

    return response;
  }

  async getFeed(friendIds: number[], page: number): Promise<Post[]> {
    const [posts, stories] = await Promise.all([
      this.commonsService.paginate(
        'posts',
        [
          {
            organization: Not(IsNull()),
            type: 'post',
          },
          {
            organization: IsNull(),
            user: In(friendIds) as any,
            type: 'post',
          },
        ],
        {},
        {
          desc: true,
          skip: 20 * page,
          take: 20,
        },
      ),
      page === 0
        ? this.commonsService.paginate(
            'posts',
            [
              {
                organization: Not(IsNull()),
                type: 'story',
              },
              {
                organization: IsNull(),
                user: In(friendIds) as any,
                type: 'story',
              },
            ],
            {},
            {
              desc: true,
            },
          )
        : {
            data: [],
          },
    ]);

    return [...posts.data, ...stories.data];
  }

  async getProfile(id: number, page: number): Promise<Post[]> {
    const [posts, stories] = await Promise.all([
      this.commonsService.paginate(
        'posts',
        [
          {
            organization: IsNull(),
            type: 'post',
          },
        ],
        {},
        {
          desc: true,
          skip: 20 * page,
          take: 20,
        },
        ['user', id],
      ),
      page === 0
        ? this.commonsService.paginate(
            'posts',
            [
              {
                organization: IsNull(),
                type: 'story',
              },
            ],
            {},
            { desc: true },
            ['user', id],
          )
        : {
            data: [],
          },
    ]);

    return [...posts.data, ...stories.data];
  }

  @Cron('0 * * * *')
  async handleCron() {
    const response = await this.commonsService.paginate('posts', {
      type: 'story',
      updatedAt: LessThanOrEqual(
        DateTime.fromJSDate(new Date())
          .minus({
            day: 1,
          })
          .toJSDate(),
      ),
    });

    await this.commonsService.delete('posts', {
      id: In(response.data.map((x) => x.id)),
    });
  }
}
