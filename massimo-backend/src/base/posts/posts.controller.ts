import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Param,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { In, IsNull, Not } from 'typeorm';

import { Customer } from '../customers/customers.entity';
import { File } from '../files/files.entity';
import { User } from '../users/users.entity';

import { Comment } from './comments.entity';
import { UpdateCommentInput } from './dto/update-comment-input/update-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { UpdatePostmetaInput } from './dto/update-postmeta-input/update-input.dto';
import { Postmeta } from './postmeta.entity';
import { Post as PostDB } from './posts.entity';
import { PostsService } from './posts.service';

import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/posts')
export class PostsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly postsService: PostsService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updatePost(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<PaginateResponse<PostDB>> {
    const activeUser = req.user as User;

    if (body.isDelete) {
      await this.commonsService.delete('posts', {
        id: In(body.data.map((x) => x.id).filter((x) => !!x)),
      });

      return secureResponse({
        data: [],
        relations: {},
      });
    }
    const fileIds = [];

    body.data.forEach((x) => {
      fileIds.push(...x.files);
    });

    const files = await this.commonsService.findAll('files', {
      id: In(fileIds),
    });

    const response = await this.commonsService.update(
      'posts',
      body.data.map(
        (data) =>
          ({
            ...data,
            files: files.filter((file) => data.files.includes(file.id)),
          } as any),
      ),
      body.changes,
    );

    const actualResponse = await this.commonsService.paginate(
      'posts',
      {
        id: In(response.map((x) => x.id)),
      },
      {
        users: ['posts.user'],
        customers: ['posts.organization'],
        files: ['posts.files'],
      },
    );

    actualResponse.data = await this.postsService.getMetas(
      Promise.resolve(actualResponse.data),
      activeUser,
    );

    return secureResponse(actualResponse);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('feed/:page')
  // eslint-disable-next-line class-methods-use-this
  async getFeed(
    @Req() req: Request,
    @Param('page') page: string,
  ): Promise<PaginateResponse<PostDB[]>> {
    const activeUser = req.user as User;

    const connections = await this.commonsService.paginate('connections', [
      {
        target2: activeUser.id as any,
        isAccepted1: true,
        isAccepted2: true,
      },
      {
        target1: activeUser.id as any,
        isAccepted1: true,
        isAccepted2: true,
      },
    ]);

    const friendIds: number[] = [];

    connections.data.forEach((x) => {
      friendIds.push(x.target1Id, x.target2Id);
    });

    friendIds.push(activeUser.id);

    const generalData = await this.postsService.getMetas(
      this.postsService.getFeed(friendIds, +page),
      activeUser,
    );

    const { posts, stories } = _.reduce(
      generalData,
      (acc, curr) => {
        if (curr.type === 'story') {
          acc.stories.push(curr);
        } else if (curr.type === 'post') {
          acc.posts.push(curr);
        }

        return acc;
      },
      {
        posts: [] as PostDB[],
        stories: [] as PostDB[],
      },
    );

    return secureResponse({
      data: [posts, stories],
      relations: {},
    });
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('profile/:id/:page')
  // eslint-disable-next-line class-methods-use-this
  async getProfile(
    @Req() req: Request,
    @Param('id') id: string,
    @Param('page') page: string,
  ): Promise<PaginateResponse<PostDB[]>> {
    const activeUser = req.user as User;

    const generalData = await this.postsService.getMetas(
      this.postsService.getProfile(+id, +page),
      activeUser,
    );

    const { posts, stories } = _.reduce(
      generalData,
      (acc, curr) => {
        if (curr.type === 'story') {
          acc.stories.push(curr);
        } else if (curr.type === 'post') {
          acc.posts.push(curr);
        }

        return acc;
      },
      {
        posts: [] as PostDB[],
        stories: [] as PostDB[],
      },
    );

    return secureResponse({
      data: [posts, stories],
      relations: {},
    });
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('organization/:id/:page')
  // eslint-disable-next-line class-methods-use-this
  async getOrganization(
    @Req() req: Request,
    @Param('id') id: string,
    @Param('page') page: string,
  ): Promise<PaginateResponse<PostDB>> {
    const activeUser = req.user as User;
    const response = await this.postsService.getOrganization(+id, +page);

    response.data = await this.postsService.getMetas(
      Promise.resolve(response.data),
      activeUser,
    );

    return secureResponse(response);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update-comments')
  // eslint-disable-next-line class-methods-use-this
  async updateComment(
    @Req() req: Request,
    @Body() body: UpdateCommentInput,
  ): Promise<PaginateResponse<Comment>> {
    if (body.isDelete) {
      await this.commonsService.delete('comments', {
        id: In(body.data.map((x) => x.id).filter((x) => !!x)),
      });

      return secureResponse({
        data: [],
        relations: {},
      });
    }

    const response = await this.commonsService.update(
      'comments',
      body.data as unknown as Comment[],
      body.changes,
    );

    const actualResponse = await this.commonsService.paginate(
      'comments',
      {
        id: In(response.map((x) => x.id)),
      },
      {
        users: ['comments.user'],
      },
    );

    return secureResponse(actualResponse);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('comments/:id/:page')
  // eslint-disable-next-line class-methods-use-this
  async getComments(
    @Req() req: Request,
    @Param('id') id: string,
    @Param('page') page: string,
  ): Promise<PaginateResponse<Comment>> {
    const actualResponse = await this.commonsService.paginate(
      'comments',
      {
        post: In([+id]),
      },
      {
        users: ['comments.user'],
      },
      {
        desc: true,
        skip: 20 * +page,
        take: 20,
      },
    );

    return secureResponse(actualResponse);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update-postmeta')
  // eslint-disable-next-line class-methods-use-this
  async updatePostmeta(
    @Req() req: Request,
    @Body() body: UpdatePostmetaInput,
  ): Promise<PaginateResponse<Postmeta>> {
    if (body.isDelete) {
      await this.commonsService.delete('postmetas', {
        id: In(body.data.map((x) => x.id).filter((x) => !!x)),
      });

      return secureResponse({
        data: [],
        relations: {},
      });
    }

    const response = await this.commonsService.update(
      'postmetas',
      body.data as unknown as Comment[],
      body.changes,
    );

    const actualResponse = await this.commonsService.paginate(
      'postmetas',
      {
        id: In(response.map((x) => x.id)),
      },
      {
        users: ['postmetas.user'],
      },
    );

    return secureResponse(actualResponse);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('postmeta-like/:id/:page')
  // eslint-disable-next-line class-methods-use-this
  async getPostmetaLike(
    @Req() req: Request,
    @Param('id') id: string,
    @Param('page') page: string,
  ): Promise<PaginateResponse<Postmeta>> {
    const actualResponse = await this.commonsService.paginate(
      'postmetas',
      {
        post: In([+id]),
        isLike: true,
      },
      {
        users: ['postmetas.user'],
      },
      {
        desc: true,
        skip: 20 * +page,
        take: 20,
      },
    );

    return secureResponse(actualResponse);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('postmeta-view/:id/:page')
  // eslint-disable-next-line class-methods-use-this
  async getPostmetaView(
    @Req() req: Request,
    @Param('id') id: string,
    @Param('page') page: string,
  ): Promise<PaginateResponse<Postmeta>> {
    const actualResponse = await this.commonsService.paginate(
      'postmetas',
      {
        post: In([+id]),
        isView: true,
      },
      {
        users: ['postmetas.user'],
      },
      {
        desc: true,
        skip: 20 * +page,
        take: 20,
      },
    );

    return secureResponse(actualResponse);
  }
}
