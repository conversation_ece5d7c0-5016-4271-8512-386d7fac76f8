import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';

import { Notification } from '../../notification.entity';

export class UpdateInputData implements Partial<Omit<Notification, "user">> {
  @ApiProperty()
  @IsOptional()
  @IsNumber()
  id: number;

  @ApiProperty()
  @IsOptional()
  @IsString()
  target: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  value: string;

  @ApiProperty()
  @IsOptional()
  @IsNumber()
  user: number;
}
