import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';

import { User } from '../users/users.entity';

@Entity()
export class Notification {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index()
  @Column()
  target: string;

  @Index()
  @Column()
  value: string;

  @Index()
  @Column({
    nullable: true,
  })
  stored?: string;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn()
  user: User;

  @RelationId((notification: Notification) => notification.user)
  userId: number;

  @Index()
  @Column('boolean', {
    default: false,
  })
  isReaded: boolean;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
