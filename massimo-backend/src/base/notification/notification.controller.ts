import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Param,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { In } from 'typeorm';

import { User } from '../users/users.entity';

import { UpdateInput } from './dto/update-input/update-input.dto';
import { Notification } from './notification.entity';

import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/notifications')
export class NotificationsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('list')
  async getNotifications(
    @Req() req: Request,
  ): Promise<PaginateResponse<Notification>> {
    const activeUser = req.user as User;

    const notifications = await this.commonsService.paginate(
      'notifications',
      {
        user: +activeUser.id as any,
        isReaded: false,
      },
      {
        users: ['notifications.user'],
      },
    );

    return secureResponse(notifications);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('add')
  async addNotification(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<PaginateResponse<Notification>> {
    const activeUser = req.user as User;

    let response: any = [];

    const fetchedUsers = await this.commonsService.findAll('users', {
      id: In(body.data.map((x) => x.user)),
    });

    fetchedUsers.push(activeUser);

    await Promise.all(
      body.data.map(async ({ target, value, user }) => {
        const findedUser = fetchedUsers.find((x) => x.id === (user || +activeUser.id));
        const finded = await this.commonsService.paginate(
          'notifications',
          {
            target,
            value,
            user: findedUser as any,
          },
          {
            users: ['notifications.user'],
          },
        );

        response.push(
          ...(finded.data.length > 0
            ? await (finded.data[0].isReaded
                ? this.commonsService.update('notifications', [
                    {
                      id: finded.data[0].id,
                      isReaded: false,
                    },
                  ])
                : this.commonsService.update('notifications', [
                    {
                      id: finded.data[0].id,
                      version: finded.data[0].version + 1,
                    },
                  ]))
            : await this.commonsService.update('notifications', [
                {
                  user: findedUser,
                  target,
                  value,
                  isReaded: false,
                },
              ])),
        );
      }),
    );

    {
      const users: User[] = [];

      response = _.isArray(response)
        ? {
            data: response.map((notification) => {
              const targetUser = _.cloneDeep(notification.user);

              if (targetUser) {
                users.push(targetUser);

                return _.defaults(_.omit(notification, ['user']), {
                  userId: targetUser.id,
                });
              }

              return _.omit(notification, ['user']);
            }),
            relations: {
              users,
            },
          }
        : {
            data: [],
            relations: {
              users: [],
            },
          };
    }

    this.commonsService.event('notifications', null, response.data);

    return secureResponse(response);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('view/:id')
  async viewNotification(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<boolean> {
    const activeUser = req.user as User;
    const actualId = +id;

    const finded = await this.commonsService.paginate(
      'notifications',
      {
        id: actualId,
        user: +activeUser.id as any,
      },
      {
        users: ['notifications.user'],
      },
    );

    if (finded.data.length > 0) {
      await this.commonsService.delete('notifications', {
        id: actualId,
      });
    } else {
      throw new BadRequestException();
    }

    return true;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('view')
  async viewMultiNotification(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<boolean> {
    const activeUser = req.user as User;
    const actualId = body.data.map((x) => x.id);

    const finded = await this.commonsService.paginate(
      'notifications',
      {
        id: In(actualId),
        user: +activeUser.id as any,
      },
      {
        users: ['notifications.user'],
      },
    );

    if (finded.data.length > 0) {
      await this.commonsService.delete('notifications', {
        id: In(actualId),
      });
    } else {
      throw new BadRequestException();
    }

    return true;
  }
}
