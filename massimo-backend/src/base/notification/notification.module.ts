import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { NotificationsController } from './notification.controller';
import { Notification } from './notification.entity';
import { NotificationsService } from './notification.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { RolesModule } from '@/base/roles/roles.module';
import { User } from '@/base/users/users.entity';
import { UsersModule } from '@/base/users/users.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Notification, User]),
    forwardRef(() => CommonsModule),
    forwardRef(() => UsersModule),
    forwardRef(() => RolesModule),
  ],
  controllers: [NotificationsController],
  providers: [NotificationsService],
  exports: [NotificationsService],
})
export class NotificationsModule {}
