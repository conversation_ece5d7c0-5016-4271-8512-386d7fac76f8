import { Module, forwardRef } from '@nestjs/common';
import { JwtModule, JwtModuleOptions } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthOptionsService } from './auth-options.service';
import { AuthController } from './auth.controller';
import { AuthService } from './auth.service';
import { JwtStrategy } from './jwt.strategy';
import { MockStrategy } from './mock.strategy';

import { User } from '@/base/users/users.entity';
import { UsersModule } from '@/base/users/users.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';
import { ConfigService } from '@/config/config.service';

@Module({
  imports: [
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (config: ConfigService): JwtModuleOptions => ({
        // "jsonwebtoken" option to sign
        secret: config.env.JWT_SECRET,
        signOptions: {
          expiresIn: config.env.JWT_EXPIRES_IN,
        },
      }),
    }),
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    forwardRef(() => CommonsModule),
    TypeOrmModule.forFeature([User]),
    forwardRef(() => UsersModule),
    ConfigModule, // for JwtStrategy
  ],
  controllers: [AuthController],
  providers: [AuthService, JwtStrategy, MockStrategy],
  exports: [AuthService],
})
export class AuthModule {}
