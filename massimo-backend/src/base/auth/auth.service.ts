import { Inject, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import * as bcrypt from 'bcrypt';
import _ = require('lodash');
import { Repository } from 'typeorm';

import { JwtPayload } from './dto/jwt-payload.dto';
import { SignInInput } from './dto/sign-in-input.dto';
import { SignInResult } from './dto/sign-in-result.dto';
import { SignUpInput } from './dto/sign-up-input.dto';

import { User } from '@/base/users/users.entity';
import { UsersService } from '@/base/users/users.service';
import { CommonsService } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Injectable()
export class AuthService {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly jwtService: JwtService,
    private readonly usersService: UsersService,
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
  ) {}

  async signUp(input: SignUpInput): Promise<User> {
    const u = new User();
    Object.assign(u, input);
    u.password = this.encryptPassword(u.password);

    const result = await this.commonsService.update('users', [u]);

    return result[0];
  }

  // eslint-disable-next-line class-methods-use-this
  encryptPassword(password): string {
    const saltRounds = 10;
    const salt = bcrypt.genSaltSync(saltRounds);
    return bcrypt.hashSync(password, salt);
  }

  async signIn(input: SignInInput): Promise<SignInResult> {
    const user = await this.commonsService.findOne('users', {
      email: input.email,
    });

    if (!user) {
      return new SignInResult();
    }

    const valid = await bcrypt.compare(input.password, user.password);
    if (!valid) {
      return new SignInResult();
    }

    const payload: JwtPayload = {
      id: user.id,
      name: user.name,
      email: user.email,
      password: user.password,
    };
    const token = this.jwtService.sign(payload);

    return { ...user, token };
  }

  async validateUser(payload: JwtPayload): Promise<User> {
    const user = await this.commonsService.findOne('users', {
      email: payload.email,
      password: payload.password,
    });
    return user;
  }
}
