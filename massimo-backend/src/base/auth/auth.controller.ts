import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';

import { AuthService } from './auth.service';
import { SignInInput } from './dto/sign-in-input.dto';
import { SignInResult } from './dto/sign-in-result.dto';
import { SignUpInput } from './dto/sign-up-input.dto';

import { User } from '@/base/users/users.entity';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @UseInterceptors(ClassSerializerInterceptor)
  @Post('signup')
  async signUp(@Body() input: SignUpInput): Promise<User> {
    const user = await this.authService.signUp(input);
    return secureResponse(user);
  }

  @Post('signin')
  async signIn(@Body() input: SignInInput): Promise<SignInResult> {
    const result = await this.authService.signIn(input);
    if (!result.token || result.isBanned || !result.isValid) {
      throw new UnauthorizedException();
    }
    return secureResponse(result);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Get('verify')
  // eslint-disable-next-line class-methods-use-this
  async verify(@Req() request: Request): Promise<User> {
    const user = request.user as User;

    if (user.isBanned || !user.isValid) {
      throw new UnauthorizedException();
    }

    return secureResponse(user);
  }
}
