import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsObject, IsOptional, IsString } from 'class-validator';

import { Message } from '../../messages.entity';

import { IsType } from '@/utils/is-type.util';

export class UpdateInputData implements Partial<Omit<Message, 'chatId'>> {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  type: string;

  @ApiProperty()
  @IsNumber()
  @IsOptional()
  ownerId: number;

  @ApiProperty()
  @IsType(['number', 'string'])
  @IsOptional()
  chatId: number | string;

  @ApiProperty()
  @IsObject()
  @IsOptional()
  content: Record<string, any>;
}
