import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlphanumeric,
  IsBoolean,
  IsOptional,
  MinLength,
} from 'class-validator';

import { Message } from '../../messages.entity';

import { IsType } from '@/utils/is-type.util';

export class Query
  implements
    Partial<
      Omit<Message, 'id' | 'chatId' | 'chat'> & {
        id: number[] | number;
        chatId: number[] | number;
        chat: number[] | number;
      }
    >
{
  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly id: number | number[];

  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly chatId: number | number[];

  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly chat: number | number[];
}
