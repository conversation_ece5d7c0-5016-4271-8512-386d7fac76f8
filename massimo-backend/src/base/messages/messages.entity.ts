import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Chat } from '../chats/chats.entity';
// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

@ObjectType()
@Entity()
export class Message {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({
    fulltext: true,
  })
  @Column()
  type: string;

  @ManyToOne(() => User, (person) => person.ownedMessages, { onDelete: 'CASCADE' })
  owner: User;

  @RelationId((message: Message) => message.owner)
  ownerId: number;

  @ManyToOne(() => Chat, (chat) => chat.messages, { onDelete: 'CASCADE' })
  @JoinColumn()
  chat: Chat;

  @RelationId((message: Message) => message.chat)
  chatId: number;

  @Column('jsonb', {
    default: {},
  })
  content: Record<string, any>;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
