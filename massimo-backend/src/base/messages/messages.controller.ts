import { faker } from '@faker-js/faker';
import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { In, Repository } from 'typeorm';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { Message } from './messages.entity';
import { MessagesService } from './messages.service';

import { AuthService } from '@/base/auth/auth.service';
import { Role } from '@/base/roles/roles.entity';
import { RolesService } from '@/base/roles/roles.service';
import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/messages')
export class MessagesController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly messagesService: MessagesService,
    @InjectRepository(Message)
    private readonly messagesRepo: Repository<Message>,
    @InjectRepository(Role)
    private readonly rolesRepo: Repository<Role>,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
    @Inject(AuthService)
    private readonly authService: AuthService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('init')
  async init(@Body() data: PaginateInput): Promise<PaginateResponse<Message>> {
    const { query, options = {} } = data;

    const messages = await this.commonsService.paginate(
      'messages',
      query as unknown as Message,
      (options as any).relations || {},
      {
        skip: 0,
        take: 100,
        desc: true,
      },
    );

    const fileIds = [];

    messages.data.forEach((message) => {
      if (message.content.fileId) {
        fileIds.push(message.content.fileId);
      }
    });

    const files = await this.commonsService.findAll('files', {
      id: In(fileIds),
    });

    return {
      data: secureResponse(messages.data).reverse(),
      relations: {
        files,
      },
    };
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Message>> {
    const { query, options = {}, page, limit, desc } = data;

    const messages = await this.commonsService.paginate(
      'messages',
      query as unknown as Message,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
        desc,
      },
    );

    return secureResponse(messages);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  // eslint-disable-next-line class-methods-use-this
  async updateMessages(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<true | PaginateResponse<Message>> {
    const user = req.user as Message;
    const { data: rawData, changes = {}, order = ['$'], isDelete } = body;

    const data = _.map(rawData, (item) => {
      const res = _.cloneDeep(item);

      if (res.id) {
        res.id = +res.id;
      }

      return res;
    });

    if (!_.every(data, (item) => item.ownerId === user.id)) {
      const { ableTo } = await this.rolesService.calculatePerm(user.id, [
        'super-admin',
        'admin',
      ]);

      if (ableTo.length === 0) {
        throw new UnauthorizedException();
      }
    }

    // eslint-disable-next-line unicorn/prefer-ternary
    if (isDelete) {
      await this.commonsService.delete('messages', data as any);

      return true;
    }
    const response = await this.commonsService.update(
      'messages',
      data as any,
      changes,
      order,
    );

    return secureResponse({
      data: response,
      relations: {},
    });
  }
}
