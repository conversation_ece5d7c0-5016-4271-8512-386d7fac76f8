import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Post,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import _ = require('lodash');

import { AnalyticsService } from './analytics.service';
import { PaginateInput } from './dto/paginate-input/paginate-input.dto';

import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/analytics')
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('general')
  async generalReport(): Promise<unknown> {
    const generalReport = await this.analyticsService.general();

    return secureResponse(generalReport);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('crm')
  async crmReport(
    @Body('projectId') projectId: number,
    @Body('date') date: Date,
    @Body('userId') userId?: number,
  ): Promise<unknown> {
    const crmReport = await this.analyticsService.crm(projectId, date, userId);

    return secureResponse(crmReport);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('user')
  async userReport(
    @Body('projectId') projectId: number,
    @Body('userId') userId: number,
    @Body('date') date: Date,
  ): Promise<unknown> {
    const crmReport = await this.analyticsService.user(projectId, userId, date);

    return secureResponse(crmReport);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('target')
  async targetReport(@Body() body: PaginateInput): Promise<unknown> {
    const targetReport = await this.analyticsService.target(body);

    return secureResponse(targetReport);
  }
}
