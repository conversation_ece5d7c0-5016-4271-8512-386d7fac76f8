/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable prefer-destructuring */
import { Inject, Injectable } from '@nestjs/common';
import deepmerge = require('deepmerge');
import _ = require('lodash');
import { DateTime } from 'luxon';
import { Any, Between, In, LessThan, LessThanOrEqual, Not } from 'typeorm';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';

import { CommonsService, ReturnTypeList } from '@/common/common.service';

const activeStatus = ['To Do', 'Active', 'Problems'];

@Injectable()
export class AnalyticsService {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
  ) {}

  repoList: (keyof ReturnTypeList)[] = [
    'accesss',
    'chats',
    'customers',
    'departments',
    'files',
    'offers',
    'projects',
    'requests',
    'roles',
    'tasks',
    'users',
  ];

  async general(): Promise<Record<keyof ReturnTypeList, number>> {
    const rawData = await Promise.all(
      this.repoList.map((repo) => this.commonsService.count(repo)),
    );

    const data = _.reduce(
      this.repoList,
      (acc, value, i) => {
        acc[value] = rawData[i];

        return acc;
      },
      {},
    ) as Record<keyof ReturnTypeList, number>;

    return data;
  }

  async target(
    options: PaginateInput,
  ): Promise<Partial<Record<keyof ReturnTypeList, number>>> {
    const rawData = await this.commonsService.count(
      options.target as keyof ReturnTypeList,
      options.query as any,
    );

    return {
      [options.target]: rawData,
    };
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/explicit-module-boundary-types
  async user(project: number, user: number, rawDate: Date) {
    const date = _.isDate(rawDate) ? rawDate : new Date(rawDate);

    const result = {
      overdueTasks: [],
      activeTasks: [],
      relations: {},
    };

    {
      const activeTasksData = await this.commonsService.paginate(
        'tasks',
        {
          status: Any(activeStatus),
          private: true,
          end: Not(
            LessThan(
              DateTime.fromJSDate(date)
                .setZone('UTC')
                .startOf('day')
                .minus({
                  hour: 3,
                })
                .toJSDate(),
            ),
          ),
          ...(project ? { project: project as any } : {}),
        },
        {
          assignees: ['tasks.assignees'],
        },
        {},
        ['user', user],
      );

      const overdueTasksData = await this.commonsService.paginate(
        'tasks',
        {
          status: Any(activeStatus),
          private: true,
          end: LessThanOrEqual(
            DateTime.fromJSDate(date)
              .setZone('UTC')
              .startOf('day')
              .minus({
                hour: 3,
              })
              .toJSDate(),
          ),
          ...(project ? { project: project as any } : {}),
        },
        {
          assignees: ['tasks.assignees'],
        },
        {},
        ['user', user],
      );

      result.activeTasks = activeTasksData.data;
      result.overdueTasks = overdueTasksData.data;

      result.relations = deepmerge(
        deepmerge(_.cloneDeep(result.relations), activeTasksData.relations),
        overdueTasksData.relations,
      );
    }

    return result;
  }

  // eslint-disable-next-line class-methods-use-this, @typescript-eslint/no-empty-function, @typescript-eslint/explicit-module-boundary-types
  async crm(project: number, rawDate: Date, user?: number) {
    const date = _.isDate(rawDate) ? rawDate : new Date(rawDate);

    const result = {
      taskCount: [
        {
          name: 'To Do',
          value: 0,
        },
        {
          name: 'Active',
          value: 0,
        },
        {
          name: 'Completed',
          value: 0,
        },
        {
          name: 'Problems',
          value: 0,
        },
        {
          name: 'Archived',
          value: 0,
        },
      ],
      totalCompletedTasks: 0,
      highPriorityTasks: [],
      weekslyOverview: [
        {
          type: 'column',
          name: 'New',
          data: [0, 0, 0, 0, 0, 0, 0],
        },
        {
          type: 'line',
          name: 'Completed',
          data: [0, 0, 0, 0, 0, 0, 0],
        },
      ],
      tasksTimeline: [
        {
          name: 'overdue',
          color: 'red',
          tasks: [],
        },
        {
          name: 'today',
          color: 'orange',
          tasks: [],
        },
        {
          name: 'nextWeek',
          color: 'blue',
          tasks: [],
        },
        {
          name: 'nextMonth',
          color: 'green',
          tasks: [],
        },
      ],

      relations: {},
    };

    const taskCount = await Promise.all([
      this.commonsService.count('tasks', {
        status: 'To Do',
        ...(project ? { project: project as any } : {}),
      }),
      this.commonsService.count('tasks', {
        status: 'Active',
        ...(project ? { project: project as any } : {}),
      }),
      this.commonsService.count('tasks', {
        status: 'Completed',
        ...(project ? { project: project as any } : {}),
      }),
      this.commonsService.count('tasks', {
        status: 'Problems',
        ...(project ? { project: project as any } : {}),
      }),
      this.commonsService.count('tasks', {
        status: 'Archived',
        ...(project ? { project: project as any } : {}),
      }),
    ]);

    result.taskCount[0].value = taskCount[0];
    result.taskCount[1].value = taskCount[1];
    result.taskCount[2].value = taskCount[2];
    result.taskCount[3].value = taskCount[3];
    result.taskCount[4].value = taskCount[4];

    result.totalCompletedTasks = taskCount[2] + taskCount[4];

    {
      const highPriorityTasksData = await this.commonsService.paginate(
        'tasks',
        [
          {
            priority: 'High',
            status: Any(activeStatus),
            ...(project ? { project: project as any } : {}),
          },
          {
            priority: 'Very High',
            status: Any(activeStatus),
            ...(project ? { project: project as any } : {}),
          },
        ],
      );

      const { data, relations } = highPriorityTasksData;

      result.highPriorityTasks = data;
      result.relations = deepmerge(_.cloneDeep(result.relations), relations);
    }

    // {
    //   const overdue = this.commonsService.paginate("tasks", ())
    // }

    result.weekslyOverview[0].data = await Promise.all(
      Array.from({ length: 7 }).map((_x, i) =>
        this.commonsService.count('tasks', {
          createdAt: Between(
            DateTime.fromJSDate(date)
              .setZone('UTC')
              .startOf('day')
              .plus({
                day: i,
              })
              .minus({
                hour: 3,
              })
              .toJSDate(),
            DateTime.fromJSDate(date)
              .setZone('UTC')
              .startOf('day')
              .plus({
                day: i + 1,
              })
              .startOf('day')
              .minus({
                hour: 3,
              })
              .toJSDate(),
          ),
        }),
      ),
    );

    {
      const timelineDatas = await Promise.all([
        // Overdue
        this.commonsService.paginate(
          'tasks',
          user
            ? {
                status: Any(activeStatus),
                private: true,
                ...(project ? { project: project as any } : {}),
                end: LessThanOrEqual(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              }
            : {
                status: Any(activeStatus),
                ...(project ? { project: project as any } : {}),
                end: LessThanOrEqual(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              },
          {
            assignees: ['tasks.assignees'],
          },
          {},
          user ? ['user', user] : undefined,
        ),

        // Today
        this.commonsService.paginate(
          'tasks',
          user
            ? {
                status: Any(activeStatus),
                private: true,
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              }
            : {
                status: Any(activeStatus),
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              },
          {
            assignees: ['tasks.assignees'],
          },
          {},
          user ? ['user', user] : undefined,
        ),
        // Week
        this.commonsService.paginate(
          'tasks',
          user
            ? {
                status: Any(activeStatus),
                private: true,
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      day: 1,
                    })
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      week: 1,
                    })
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              }
            : {
                status: Any(activeStatus),
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      day: 1,
                    })
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      week: 1,
                    })
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              },
          {
            assignees: ['tasks.assignees'],
          },
          {},
          user ? ['user', user] : undefined,
        ),
        // Month
        this.commonsService.paginate(
          'tasks',
          user
            ? {
                status: Any(activeStatus),
                private: true,
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      week: 1,
                      day: 1,
                    })
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      month: 1,
                    })
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              }
            : {
                status: Any(activeStatus),
                ...(project ? { project: project as any } : {}),
                end: Between(
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      week: 1,
                      day: 1,
                    })
                    .startOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                  DateTime.fromJSDate(date)
                    .setZone('UTC')
                    .startOf('day')
                    .plus({
                      month: 1,
                    })
                    .endOf('day')
                    .minus({
                      hour: 3,
                    })
                    .toJSDate(),
                ),
              },
          {
            assignees: ['tasks.assignees'],
          },
          {},
          user ? ['user', user] : undefined,
        ),
      ]);

      result.tasksTimeline[0].tasks = timelineDatas[0].data;
      result.tasksTimeline[1].tasks = timelineDatas[1].data;
      result.tasksTimeline[2].tasks = timelineDatas[2].data;
      result.tasksTimeline[3].tasks = timelineDatas[3].data;

      result.relations = deepmerge.all([
        {},
        result.relations,
        timelineDatas[0].relations,
        timelineDatas[1].relations,
        timelineDatas[2].relations,
        timelineDatas[3].relations,
      ]);
    }

    result.weekslyOverview[1].data = await Promise.all(
      Array.from({ length: 7 }).map((_x, i) => {
        const common = {
          end: Between(
            DateTime.fromJSDate(date)
              .setZone('UTC')
              .startOf('day')
              .plus({
                day: i,
              })
              .minus({
                hour: 3,
              })
              .toJSDate(),
            DateTime.fromJSDate(date)
              .setZone('UTC')
              .startOf('day')
              .plus({
                day: i + 1,
              })
              .endOf('day')
              .minus({
                hour: 3,
              })
              .toJSDate(),
          ),
        };

        return this.commonsService.count('tasks', [
          {
            ...common,
            status: 'Completed',
            ...(project ? { project: project as any } : {}),
          },
          {
            ...common,
            status: 'Archived',
            ...(project ? { project: project as any } : {}),
          },
        ]);
      }),
    );

    return result;
  }
}
