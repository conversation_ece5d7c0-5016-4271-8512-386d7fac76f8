import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';
import { S3Module } from 'nestjs-s3';

import options = require('./config.digitalocean');
import { FilesController } from './files.controller';
import { File } from './files.entity';
import { FilesService } from './files.service';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    S3Module.forRoot({
      config: options,
    }),
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([File]),
    forwardRef(() => CommonsModule),
  ],
  controllers: [FilesController],
  providers: [FilesService],
  exports: [FilesService],
})
export class FilesModule {}
