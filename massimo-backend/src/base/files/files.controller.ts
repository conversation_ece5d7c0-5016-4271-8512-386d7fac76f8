import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Post,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');

import { User } from '../users/users.entity';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { FilesService } from './files.service';

import { PaginateResponse } from '@/common/common.service';

@Controller('api/files')
export class FilesController {
  constructor(private readonly filesService: FilesService) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<string>> {
    const { query } = data;

    const response = await this.filesService.getFiles(
      _.isArray(query.id) ? query.id : [query.id],
    );

    if (response && response.length > 0) {
      return {
        data: response,
      };
    }

    return {
      data: [],
    };
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @Post('upload')
  @UseInterceptors(
    ClassSerializerInterceptor,
    FileFieldsInterceptor(
      [
        { name: 'avatar', maxCount: 1 },
        { name: 'attachment', maxCount: 20 },
        { name: 'post', maxCount: 20 },
        { name: 'story', maxCount: 20 },
      ],
      {
        limits: {
          fileSize: 10_000_000,
        },
      },
    ),
  )
  // eslint-disable-next-line class-methods-use-this
  async add(
    @Req() req: Request,
    @UploadedFiles()
    files: {
      avatar?: Express.Multer.File[];
      attachment?: Express.Multer.File[];
      post?: Express.Multer.File[];
      story?: Express.Multer.File[];
    },
    @Body() body: UpdateInput,
  ): Promise<string[]> {
    if (!body.update) {
      throw new BadRequestException();
    }

    if (!+body.userId) {
      throw new BadRequestException();
    }

    let updateList: Record<string, number>;

    try {
      updateList = JSON.parse(body.update);

      // eslint-disable-next-line unicorn/prefer-object-from-entries
      updateList = _.fromPairs(
        _.entries(updateList).map(([filename, updateId]) => [
          filename,
          Number.parseInt(updateId as unknown as string, 10),
        ]),
      );
    } catch (error) {
      throw new BadRequestException(error);
    }

    if (!_.isObject(updateList)) {
      throw new BadRequestException();
    }

    const response = await Promise.all(
      _.map(_.entries(files), async ([type, fileList]) => {
        const res =
          fileList && fileList.length > 0
            ? await this.filesService.addFiles(
                updateList,
                files[type],
                +body.userId,
                type,
              )
            : [];

        return res;
      }),
    );

    // eslint-disable-next-line unicorn/prefer-array-flat
    return _.uniq(_.flatten(response));
  }
}
