import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import {
  Column,
  CreateDateColumn,
  Entity,
  Generated,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Post } from '../posts/posts.entity';
// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

@ObjectType()
@Entity()
export class File {
  @Field((type) => ID)
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({
    unique: true,
  })
  @Column()
  @Generated('uuid')
  uuid: string;

  @Index()
  @Column()
  file: string;

  @Index()
  @Column({
    default: 'attachment',
  })
  type: string;

  @Column({
    type: 'simple-array',
    nullable: true,
  })
  access: string[] | null;

  @ManyToOne(() => User, (user) => user.files, { onDelete: 'CASCADE' })
  @JoinColumn()
  owner: User;

  @RelationId((files: File) => files.owner)
  ownerId: number;

  @ManyToOne(() => Post, (post) => post.files, {
    onDelete: 'CASCADE',
  })
  post: Post;

  @RelationId((files: File) => files.post)
  postId: number;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
