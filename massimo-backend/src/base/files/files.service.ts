import { RedisService } from '@liaoliaots/nestjs-redis';
import {
  Inject,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { all } from 'deepmerge';
import Redis from 'ioredis';
import _ = require('lodash');
import { InjectS3, S3 } from 'nestjs-s3';
import sanitizeFilename = require('sanitize-filename');
import { In, Repository } from 'typeorm';

import { File } from './files.entity';

import { CommonsService } from '@/common/common.service';
import { filePath } from '@/utils/file-path.util';

const seperate = '-';

@Injectable()
export class FilesService {
  private readonly redis: Redis;

  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    @InjectRepository(File)
    private readonly filesRepo: Repository<File>,
    private readonly redisService: RedisService,
    @InjectS3() private readonly s3: S3,
  ) {
    this.redis = this.redisService.getClient();
  }

  async getFiles(ids: number[]): Promise<string[]> {
    if (ids.length === 0) {
      return [];
    }

    const cache = await this.redis.mget(...ids.map((id) => `${id}`));

    const notAvails = _.filter(
      _.map(ids, (id, i) => (_.isNull(cache[i]) ? [id, i] : undefined)),
      (tuple) => !_.isUndefined(tuple),
    );

    const files = await this.commonsService.findAll('files', {
      id: _.map(notAvails, (n) => n[0]) as any,
    });

    if (files && files.length > 0) {
      const redisData = [];
      _.forEach(files, (file) => {
        const value = filePath(`${file.uuid}${seperate}${file.file}`);

        redisData.push(file.id, value);

        return value;
      });

      await this.redis.mset(redisData);

      try {
        await Promise.all(
          _.map(files, (file) => this.redis.expire(`${file.id}`, 300)),
        );
        // eslint-disable-next-line no-empty
      } catch {}

      return _.uniq(
        _.filter(
          [
            ..._.map(ids, (id, i) => {
              const findedFile = files.find((file) => file.id === id);

              const finded = findedFile
                ? filePath(`${findedFile?.uuid}${seperate}${findedFile?.file}`)
                : cache[i];

              // eslint-disable-next-line no-unneeded-ternary
              return finded ? finded : null;
            }),
          ],
          (x) => _.isString(x),
        ),
      );
    }

    return _.map(ids, (id, i) => {
      const finded = cache[i];

      // eslint-disable-next-line no-unneeded-ternary
      return finded ? finded : null;
    });

    // return _.uniq(_.filter(cache, (x) => _.isString(x)));
  }

  // eslint-disable-next-line class-methods-use-this
  async addFiles(
    updateList: Record<string, number>,
    rawFiles: Express.Multer.File[],
    userId: number,
    type = 'attachment',
  ): Promise<string[]> {
    if (!userId) {
      throw new UnauthorizedException();
    }

    const user = await this.commonsService.findOne('users', { id: userId });

    if (!user) {
      throw new UnauthorizedException();
    }

    const response = [];
    const nonExistingFiles: Express.Multer.File[] = [];

    // eslint-disable-next-line no-param-reassign
    rawFiles = _.map(rawFiles, (file) => {
      const prevName = _.cloneDeep(file.originalname);

      // eslint-disable-next-line no-param-reassign
      file.originalname = `${user.id}${seperate}${sanitizeFilename(
        file.originalname,
      )}`;

      const updateId = updateList[prevName];

      if (updateId) {
        // eslint-disable-next-line no-param-reassign
        updateList = {
          ..._.omit(updateList, prevName),
          [file.originalname]: updateId,
        };
      } else {
        nonExistingFiles.push(file);
      }

      return file;
    });

    const deleteList = _.values(updateList).map((x) => `${x}`);

    if (deleteList.length > 0) {
      await this.redis.del(deleteList);
    }

    const existingFiles = await this.commonsService.findAll('files', {
      id: _.map(_.entries(updateList), ([_name, id]) => id) as any,
    });

    const dbRecords: File[] = [];
    const dbNewRecords: File[] = [];

    if (existingFiles.length > 0) {
      const updateResponse = await this.commonsService.update(
        'files',
        _.map(existingFiles, (file) => {
          const findedUpdateListName = _.find(
            _.entries(updateList),
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            ([_name, id]) => id === file.id,
          )[0];
          const findedRawFilesData = rawFiles.find(
            (rawFile) => rawFile.originalname === findedUpdateListName,
          );

          return {
            ...file,
            file: findedRawFilesData.originalname,
          };
        }),
      );

      dbRecords.push(...updateResponse);
    }

    if (nonExistingFiles.length > 0) {
      const saveResponse = await this.commonsService.update(
        'files',
        _.map(nonExistingFiles, (file) => {
          const data: Partial<File> = {
            owner: user,
            file: file.originalname,
            type,
          };

          return data;
        }),
      );

      dbNewRecords.push(...saveResponse);
    }

    try {
      await Promise.all(
        _.map([...dbRecords, ...dbNewRecords], async (file, i) => {
          const findedRawFile = _.find(
            rawFiles,
            (rawFile) => rawFile.originalname === file.file,
          );

          const res = await Promise.all([
            this.s3
              .upload({
                Bucket: 'massimo',
                Key: `files/${file.uuid}${seperate}${file.file}`,
                Body: findedRawFile.buffer,
                ACL: 'public-read',
              })
              .promise(),
            this.s3
              .upload({
                Bucket: 'massimo',
                Key: `file-backups/${file.uuid}${seperate}${file.file}`,
                Body: findedRawFile.buffer,
                ACL: 'private',
              })
              .promise(),
          ]);

          return res[0];
        }),
      );
    } catch (error) {
      if (error) {
        await this.commonsService.delete('files', {
          id: In(dbNewRecords.map((x) => x.id)),
        });

        await this.commonsService.update(
          'files',
          _.map(dbRecords, (file) =>
            all([
              file,
              existingFiles.find((existingFile) => existingFile.id === file.id),
            ]),
          ),
        );

        throw error;
      }
    }

    _.forEach(rawFiles, (rawFile, i) => {
      const findedFile = _.find(
        [...dbRecords, ...dbNewRecords],
        (dbRecord) => dbRecord.file === rawFile.originalname,
      );

      response[i] = findedFile ? findedFile.id || null : null;
    });

    return response;

    /*
    if (!userId) {
      throw new UnauthorizedException();
    }

    const user = await this.commonsService.findOne('users', { id: userId });

    if (!user) {
      throw new UnauthorizedException();
    }

    // eslint-disable-next-line no-param-reassign
    rawFiles = _.map(rawFiles, (file) => {
      const prevName = _.cloneDeep(file.originalname);

      // eslint-disable-next-line no-param-reassign
      file.originalname = `${user.id}${seperate}${sanitizeFilename(
        file.originalname,
      )}`;

      const updateId = updateList[prevName];

      if (updateId) {
        // eslint-disable-next-line no-param-reassign
        updateList = {
          ..._.omit(updateList, prevName),
          [file.originalname]: updateId,
        };
      }

      return file;
    });

    const deleteList = _.values(updateList).map((x) => `${x}`);

    if (deleteList.length > 0) {
      await this.redis.del(deleteList);
    }

    const response = [];

    const existingFiles = await this.commonsService.findAll('files', {
      file: _.map(rawFiles, (file) => file.originalname) as any,
    });
    const existingFileNames = _.map(existingFiles, (file) => file.file);

    const files = _.filter(
      // eslint-disable-next-line consistent-return
      _.map(rawFiles, (file, i) => {
        const found = _.indexOf(existingFileNames, file.originalname);
        if (found > -1) {
          response[i] = existingFiles[found].id;
        } else {
          return [file, i];
        }
      }),
      (tuple: any) => !_.isUndefined(tuple),
    ) as [Express.Multer.File, number][];

    const added = await this.commonsService.update(
      'files',
      _.map(
        files,
        (file): Partial<File> => ({
          file: file[0].originalname,
          id: updateList[file[0].originalname],
          owner: user,
          type,
        }),
      ),
    );

    console.log(added, files, existingFiles);

    try {
      await Promise.all(
        _.map(files, async (file, i) => {
          const res = await Promise.all([
            this.s3
              .upload({
                Bucket: 'massimo',
                Key: `files/${added[i].uuid}${seperate}${added[i].file}`,
                Body: file[0].buffer,
                ACL: 'public-read',
              })
              .promise(),
            this.s3
              .upload({
                Bucket: 'massimo',
                Key: `file-backups/${added[i].uuid}${seperate}${added[i].file}`,
                Body: file[0].buffer,
                ACL: 'private',
              })
              .promise(),
          ]);

          return res[0];
        }),
      );
    } catch (error) {
      if (error) {
        await this.commonsService.delete('files', {
          id: In(added.map((x) => x.id)),
        });
      }
    }

    _.forEach(files, ([_file, i]) => {
      response[i] = added[i].id;
    });

    return response;
    */
  }
}
