import { ApiProperty } from '@nestjs/swagger';
import {
  IsAlphanumeric,
  IsBoolean,
  IsOptional,
  MinLength,
} from 'class-validator';

import { File } from '../../files.entity';

import { IsType } from '@/utils/is-type.util';

export class Query
  implements
    Partial<
      Omit<File, 'id'> & {
        id: number[] | number;
      }
    >
{
  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly id: number | number[];
}
