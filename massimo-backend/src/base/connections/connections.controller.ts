import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Inject,
  Param,
  Post,
  Req,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { Any, In, Raw } from 'typeorm';

import { User } from '../users/users.entity';

import { Connection } from './connections.entity';
import { PaginateInput } from './dto/paginate-input/paginate-input.dto';

import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/connections')
export class ConnectionsController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<Connection>> {
    const { query, options = {}, page, limit } = data;

    const connections = await this.commonsService.paginate(
      'connections',
      query as unknown as Connection,
      (options as any).relations || [],
      {
        skip: page * limit,
        take: limit,
      },
    );

    return secureResponse(connections);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('connected/:id')
  async getConnected(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<PaginateResponse<Connection>> {
    const activeUser = req.user as User;
    const targetId =
      // eslint-disable-next-line no-nested-ternary
      id && id.length > 0 ? (+id ? +id : +activeUser.id) : +activeUser.id;

    const connections = await this.commonsService.paginate(
      'connections',
      [
        {
          target2: targetId as any,
          isAccepted1: true,
          isAccepted2: true,
        },
        {
          target1: targetId as any,
          isAccepted1: true,
          isAccepted2: true,
        },
      ],
      {
        users: ['connections.target1', 'connections.target2'],
      },
    );

    return secureResponse(connections);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('disconnected')
  async getDisconnected(
    @Req() req: Request,
  ): Promise<PaginateResponse<Connection>> {
    const activeUser = req.user as User;

    const connections = await this.commonsService.paginate(
      'connections',
      [
        {
          target1: +activeUser.id as any,
          isAccepted1: false,
          isAccepted2: true,
        },
        {
          target1: +activeUser.id as any,
          isAccepted1: true,
          isAccepted2: false,
        },
        {
          target2: +activeUser.id as any,
          isAccepted1: false,
          isAccepted2: true,
        },
        {
          target2: +activeUser.id as any,
          isAccepted1: true,
          isAccepted2: false,
        },
      ],
      {
        users: ['connections.target1', 'connections.target2'],
      },
    );

    return secureResponse(connections);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('connect/:id')
  // eslint-disable-next-line class-methods-use-this
  async connect(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<PaginateResponse<Connection>> {
    const activeUser = req.user as User;
    let response;

    const actualData = await this.commonsService.paginate(
      'connections',
      [
        {
          target1: +id as any,
          target2: +activeUser.id as any,
        },
        {
          target1: +activeUser.id as any,
          target2: +id as any,
        },
      ],
      {
        users: ['connections.target1', 'connections.target2'],
      },
    );

    const users = await this.commonsService.findAll('users', [
      {
        id: In([+activeUser.id, +id]),
      },
    ]);

    if (actualData.data.length === 0) {
      if (users.length !== 2) {
        throw new BadRequestException();
      }

      response = await (users[0].id === activeUser.id
        ? this.commonsService.update('connections', [
            {
              isAccepted1: true,
              target1: users[0],
              target2: users[1],
            },
          ])
        : this.commonsService.update('connections', [
            {
              isAccepted1: true,
              target1: users[1],
              target2: users[0],
            },
          ]));
    } else if (
      actualData.data[0].isAccepted1 &&
      actualData.data[0].isAccepted2
    ) {
      response = await this.commonsService.delete('connections', [
        {
          id: actualData.data[0].id,
        },
      ]);

      const chatData = await this.commonsService.paginate(
        'chats',
        [
          {
            person: In([actualData.data[0].target1Id]) as any,
            owner: In([actualData.data[0].target2Id]) as any,
          },
          {
            person: In([actualData.data[0].target2Id]) as any,
            owner: In([actualData.data[0].target1Id]) as any,
          },
        ],
        {},
        {
          take: 1,
        },
      );

      this.commonsService.deleteEvent('connections', null, {
        data: [actualData.data[0].id],
      });

      if (chatData?.data[0]) {
        await this.commonsService.delete('chats', [
          {
            id: In([chatData.data[0].id]),
          },
        ]);

        this.commonsService.deleteEvent('chats', null, {
          data: [chatData.data[0].id],
        });
      }
    } else if (actualData.data[0].target1Id === activeUser.id) {
      response = await this.commonsService.update('connections', [
        {
          ...actualData.data[0],
          isAccepted1: !actualData.data[0].isAccepted1,
        },
      ]);

      if (actualData.data[0].isAccepted2 && !actualData.data[0].isAccepted1) {
        await this.commonsService.update('chats', [
          {
            owner: actualData.data[0].target2Id as any,
            person: actualData.data[0].target1Id as any,
            type: 'person',
          },
        ]);
      }
    } else if (actualData.data[0].target2Id === activeUser.id) {
      response = await this.commonsService.update('connections', [
        {
          ...actualData.data[0],
          isAccepted2: !actualData.data[0].isAccepted2,
        },
      ]);

      if (actualData.data[0].isAccepted1 && !actualData.data[0].isAccepted2) {
        await this.commonsService.update('chats', [
          {
            owner: actualData.data[0].target1Id as any,
            person: actualData.data[0].target2Id as any,
            type: 'person',
          },
        ]);
      }
    }

    {
      let target1;
      let target2;

      response = _.isArray(response)
        ? {
            data: response.map((connection) => {
              target1 = _.cloneDeep(connection.target1);
              target2 = _.cloneDeep(connection.target2);

              return _.omit(connection, ['target1', 'target2']);
            }),
            relations: {
              users:
                // eslint-disable-next-line no-nested-ternary
                target1 && target2
                  ? [target1, target2]
                  : actualData
                  ? actualData.relations.users
                  : [],
            },
          }
        : {
            data: [],
            relations: {
              users: [],
            },
          };
    }

    this.commonsService.event('connections', null, {
      id:
        // eslint-disable-next-line no-nested-ternary
        response.data.length > 0
          ? [response.data[0].id]
          : actualData.data.length > 0
          ? [actualData.data[0].id]
          : [],
    });

    return response;
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('reject/:id')
  // eslint-disable-next-line class-methods-use-this
  async reject(
    @Req() req: Request,
    @Param('id') id: string,
  ): Promise<PaginateResponse<Connection>> {
    const activeUser = req.user as User;
    let response;

    const actualData = await this.commonsService.paginate(
      'connections',
      [
        {
          target1: +id as any,
          target2: +activeUser.id as any,
        },
        {
          target1: +activeUser.id as any,
          target2: +id as any,
        },
      ],
      {
        users: ['connections.target1', 'connections.target2'],
      },
    );

    if (
      (actualData.data[0].target1Id === activeUser.id ||
        actualData.data[0].target2Id === activeUser.id) &&
      (!actualData.data[0].isAccepted1 || !actualData.data[0].isAccepted2)
    ) {
      response = await this.commonsService.delete('connections', [
        {
          id: actualData.data[0].id,
        },
      ]);
    }

    {
      let target1;
      let target2;

      response = _.isArray(response)
        ? {
            data: response.map((connection) => {
              target1 = _.cloneDeep(connection.target1);
              target2 = _.cloneDeep(connection.target2);

              return _.omit(connection, ['target1', 'target2']);
            }),
            relations: {
              users:
                // eslint-disable-next-line no-nested-ternary
                target1 && target2
                  ? [target1, target2]
                  : actualData
                  ? actualData.relations.users
                  : [],
            },
          }
        : {
            data: [],
            relations: {
              users: [],
            },
          };
    }

    this.commonsService.event('connections', null, {
      id:
        // eslint-disable-next-line no-nested-ternary
        response.data.length > 0
          ? [response.data[0].id]
          : actualData.data.length > 0
          ? [actualData.data[0].id]
          : [],
    });

    return response;
  }
}
