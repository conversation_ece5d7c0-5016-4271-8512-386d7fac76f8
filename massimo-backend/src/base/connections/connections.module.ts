import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthOptionsService } from '../auth/auth-options.service';

import { ConnectionsController } from './connections.controller';
import { Connection } from './connections.entity';
import { ConnectionsService } from './connections.service';

import { Role } from '@/base/roles/roles.entity';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([Connection, Role]),
    forwardRef(() => CommonsModule),
  ],
  controllers: [ConnectionsController],
  providers: [ConnectionsService],
  exports: [ConnectionsService],
})
export class ConnectionsModule {}
