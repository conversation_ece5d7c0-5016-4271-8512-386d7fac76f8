import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  <PERSON>inColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { User } from '../users/users.entity';

@Entity()
export class Connection {
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Column({
    default: false,
  })
  isAccepted1: boolean;

  @Column({
    default: false,
  })
  isAccepted2: boolean;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  target1: User;

  @RelationId((connection: Connection) => connection.target1)
  target1Id: number;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  target2: User;

  @RelationId((connection: Connection) => connection.target2)
  target2Id: number;

  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
