import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>Array, IsBoolean, IsNumber, IsOptional } from 'class-validator';

import { Connection } from '../../connections.entity';

export class UpdateInputData implements Partial<Connection> {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isAccepted: boolean;

  @ApiProperty()
  @IsArray()
  targetIds: number[];

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  readonly isVisible?: boolean;
}
