import { faker } from '@faker-js/faker';
import {
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Inject,
  Param,
  Post,
  Req,
  UnauthorizedException,
  UseGuards,
  UseInterceptors
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth } from '@nestjs/swagger';
import { InjectRepository } from '@nestjs/typeorm';
// eslint-disable-next-line import/no-extraneous-dependencies
import { Request } from 'express';
import _ = require('lodash');
import { Repository } from 'typeorm';

import { ChatsService } from '../chats/chats.service';

import { PaginateInput } from './dto/paginate-input/paginate-input.dto';
import { UpdateInput } from './dto/update-input/update-input.dto';
import { User } from './users.entity';
import { UsersService } from './users.service';

import { AuthService } from '@/base/auth/auth.service';
import { Role } from '@/base/roles/roles.entity';
import { RolesService } from '@/base/roles/roles.service';
import { CommonsService, PaginateResponse } from '@/common/common.service';
import { secureResponse } from '@/utils/secure-response.util';

@Controller('api/users')
export class UsersController {
  constructor(
    @Inject(CommonsService)
    private readonly commonsService: CommonsService,
    private readonly usersService: UsersService,
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
    @InjectRepository(Role)
    private readonly rolesRepo: Repository<Role>,
    @Inject(RolesService)
    private readonly rolesService: RolesService,
    @Inject(ChatsService)
    private readonly chatsService: ChatsService,
    @Inject(AuthService)
    private readonly authService: AuthService,
  ) {}

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Get(':name')
  async findOneByName(@Param('name') name: string): Promise<User> {
    const user = await this.commonsService.findOne('users', { name });

    return secureResponse(user);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('paginate')
  async findWithPagination(
    @Body() data: PaginateInput,
  ): Promise<PaginateResponse<User>> {
    const { query, options = {}, page, limit } = data;

    const users = await this.commonsService.paginate(
      'users',
      query as User,
      (options as any).relations || {},
      {
        skip: page * limit,
        take: limit,
      },
    );

    return secureResponse(users);
  }

  @UseGuards(AuthGuard())
  @ApiBearerAuth()
  @UseInterceptors(ClassSerializerInterceptor)
  @Post('update')
  async updateUser(
    @Req() req: Request,
    @Body() body: UpdateInput,
  ): Promise<User[]> {
    const user = req.user as User;
    const { data, changes = {}, order = ['$'] } = body;

    if (!_.every(data, (item) => item.id === user.id)) {
      const { ableTo } = await this.rolesService.calculatePerm(user.id, [
        'super-admin',
        'admin',
      ]);

      if (ableTo.length === 0) {
        throw new UnauthorizedException();
      }
    }

    const response = await this.commonsService.update(
      'users',
      _.map(data, (item) => {
        const res = _.cloneDeep(item);

        if (res.id) {
          res.id = +res.id;
        }

        if (_.isString(res.password) && res.password.length > 0) {
          res.password = this.authService.encryptPassword(res.password);
        }

        if (!res.id && !res.password) {
          res.password = this.authService.encryptPassword(
            faker.internet.password(20),
          );
        }

        return res;
      }),
      changes,
      order,
    );

    return response;
  }
}
