import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { User } from './users.entity';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private readonly usersRepo: Repository<User>,
  ) {}

  async banOneById(id: number, data: null | boolean = true): Promise<true> {
    let targetData: boolean;

    if (data === null) {
      const user = await this.usersRepo.findOne(id);

      targetData = !user.isBanned;
    } else {
      targetData = data;
    }

    await this.usersRepo.update(id, {
      isBanned: targetData,
    });

    return true;
  }

  async validateOneById(
    id: number,
    data: null | boolean = true,
  ): Promise<true> {
    let targetData: boolean;

    if (data === null) {
      const user = await this.usersRepo.findOne(id);

      targetData = !user.isValid;
    } else {
      targetData = data;
    }

    await this.usersRepo.update(id, {
      isValid: targetData,
    });

    return true;
  }

  async makeCustomerOneById(
    id: number,
    data: null | boolean = true,
  ): Promise<true> {
    let targetData: boolean;

    if (data === null) {
      const user = await this.usersRepo.findOne(id);

      targetData = !user.isCustomer;
    } else {
      targetData = data;
    }

    await this.usersRepo.update(id, {
      isCustomer: targetData,
    });

    return true;
  }
}
