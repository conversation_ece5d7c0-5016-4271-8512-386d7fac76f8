import { Field, InputType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  MinLength,
} from 'class-validator';

import { User } from '../../users.entity';

export class UpdateInputData implements Partial<User> {
  @ApiProperty()
  @IsNumber()
  @IsOptional()
  id: number;

  @ApiProperty()
  @IsString()
  @IsOptional()
  name: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  surname: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  phone: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isCustomer: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isValid: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isBanned: boolean;

  @ApiProperty()
  @IsArray()
  @IsOptional()
  defaultRoleId: number[];

  @ApiProperty()
  @IsObject()
  @IsOptional()
  roleValues: Record<string, Record<string, boolean>>;

  @ApiProperty()
  @IsString()
  @MinLength(8)
  @IsOptional()
  password: string;
}
