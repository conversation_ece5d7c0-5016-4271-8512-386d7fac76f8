import { Field, InputType } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsAlphanumeric,
  IsArray,
  IsBoolean,
  IsNumber,
  IsOptional,
  MinLength,
} from 'class-validator';
import { isType } from 'graphql';
import _ = require('lodash');

import { User } from '../../users.entity';

import { IsType } from '@/utils/is-type.util';

export class Query
  implements
    Partial<
      Omit<User, 'id'> & {
        id: number[] | number;
      }
    >
{
  @ApiProperty()
  @IsType(['number', 'numberArray'])
  @IsOptional()
  readonly id: number | number[];

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly search: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly name: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly surname: string;

  @ApiProperty()
  @MinLength(1)
  @IsOptional()
  readonly email: string;

  @ApiProperty()
  @IsAlphanumeric()
  @MinLength(1)
  @IsOptional()
  phone: string;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isCustomer: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isValid: boolean;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  isBanned: boolean;
}
