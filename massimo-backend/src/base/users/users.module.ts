import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AuthOptionsService } from '../auth/auth-options.service';
// eslint-disable-next-line import/no-cycle
import { AuthModule } from '../auth/auth.module';
import { ChatsModule } from '../chats/chats.module';

import { UsersController } from './users.controller';
import { User } from './users.entity';
import { UsersService } from './users.service';

import { Role } from '@/base/roles/roles.entity';
import { RolesModule } from '@/base/roles/roles.module';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    TypeOrmModule.forFeature([User, Role]),
    forwardRef(() => CommonsModule),
    forwardRef(() => RolesModule),
    forwardRef(() => ChatsModule),
    forwardRef(() => AuthModule),
  ],
  controllers: [UsersController],
  providers: [UsersService],
  exports: [UsersService],
})
export class UsersModule {}
