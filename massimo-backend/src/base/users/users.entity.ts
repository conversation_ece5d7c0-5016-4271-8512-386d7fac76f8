import { Field, ID, Int, ObjectType } from '@nestjs/graphql';
import { Exclude } from 'class-transformer';
import _ = require('lodash');
import {
  AfterInsert,
  AfterLoad,
  AfterUpdate,
  BeforeInsert,
  BeforeUpdate,
  Column,
  CreateDateColumn,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  RelationId,
  VersionColumn,
} from 'typeorm';

// eslint-disable-next-line import/no-cycle
import { Chat } from '../chats/chats.entity';
// eslint-disable-next-line import/no-cycle
import { Connection } from '../connections/connections.entity';
// eslint-disable-next-line import/no-cycle
import { Customer } from '../customers/customers.entity';
// eslint-disable-next-line import/no-cycle
import { File } from '../files/files.entity';
// eslint-disable-next-line import/no-cycle
import { Message } from '../messages/messages.entity';
import { Role } from '../roles/roles.entity';

import { Department } from '@/base/departments/departments.entity';
import { Project } from '@/modules/crm/projects/projects.entity';
import { Request } from '@/modules/crm/requests/requests.entity';
import { Task } from '@/modules/crm/tasks/tasks.entity';
import { secureResponse } from '@/utils/secure-response.util';

@ObjectType()
@Entity()
export class User {
  @Field((type) => ID)
  @Index()
  @PrimaryGeneratedColumn()
  readonly id: number;

  @Index({
    fulltext: true,
  })
  @Column()
  name: string;

  @Index({
    fulltext: true,
  })
  @Column({
    nullable: true,
  })
  surname: string | null;

  @Index({ unique: true, fulltext: true })
  @Column()
  email: string;

  @Index({ unique: true, fulltext: true })
  @Column({
    nullable: true,
  })
  phone: string | null;

  @Index()
  @Column({
    default: false,
  })
  isCustomer: boolean;

  @Index()
  @Column({
    default: false,
  })
  isValid: boolean;

  @Index()
  @Column({
    default: false,
  })
  isBanned: boolean;

  @ManyToMany((type) => Role, { onDelete: 'CASCADE' })
  @JoinTable({
    name: 'default_roles',
  })
  defaultRole: Role[];

  @RelationId((user: User) => user.defaultRole)
  defaultRoleId: number[];

  @ManyToMany((type) => Role, { onDelete: 'CASCADE' })
  @JoinTable()
  roles: Role[];

  @RelationId((user: User) => user.roles)
  roleIds: number[];

  @Column('jsonb', {
    default: {},
  })
  roleValues: Record<string, Record<string, boolean | undefined>>;

  @Exclude({ toPlainOnly: true })
  @Column()
  password: string;

  @ManyToMany(() => Task, (task) => task.assignees, { onDelete: 'CASCADE' })
  assigneds: Task[];

  @RelationId((user: User) => user.assigneds)
  assignedIds: number[];

  @ManyToMany(() => Department, (department) => department.users, {
    onDelete: 'CASCADE',
  })
  departments: Department[];

  @RelationId((user: User) => user.departments)
  departmentIds: number[];

  @OneToMany(() => File, (file) => file.owner, { onDelete: 'CASCADE' })
  files: File[];

  @RelationId((user: User) => user.files)
  fileIds: number[];

  @ManyToMany(() => Customer, (customer) => customer.persons, {
    onDelete: 'CASCADE',
  })
  organizations: Customer[];

  @RelationId((user: User) => user.organizations)
  organizationIds: number[];

  @ManyToMany(() => Project, (request) => request.allowedUsers, {
    onDelete: 'CASCADE',
  })
  allowedProjects: Project[];

  @RelationId((user: User) => user.allowedProjects)
  allowedProjectIds: number[];

  @ManyToMany(() => Project, (request) => request.restrictedUsers, {
    onDelete: 'CASCADE',
  })
  restrictedProjects: Project[];

  @RelationId((user: User) => user.restrictedProjects)
  restrictedProjectIds: number[];

  @OneToMany(() => Request, (request) => request.approver, {
    onDelete: 'CASCADE',
  })
  @JoinColumn()
  approveds: Request[];

  @RelationId((user: User) => user.approveds)
  approvedIds: number[];

  @OneToMany(() => Chat, (chat) => chat.person, { onDelete: 'CASCADE' })
  chats: Chat[];

  @RelationId((user: User) => user.chats)
  chatIds: number[];

  @OneToMany(() => Chat, (chat) => chat.owner, { onDelete: 'CASCADE' })
  ownedChats: Chat[];

  @RelationId((user: User) => user.ownedChats)
  ownedChatIds: number[];

  @OneToMany(() => Message, (message) => message.owner, { onDelete: 'CASCADE' })
  ownedMessages: Message[];

  @Index({
    fulltext: true,
    unique: false,
  })
  @Column({ nullable: true })
  public search: string | null;

  @Field((type) => Int)
  @VersionColumn()
  readonly version: number;

  @CreateDateColumn()
  createdAt: Date;
}
