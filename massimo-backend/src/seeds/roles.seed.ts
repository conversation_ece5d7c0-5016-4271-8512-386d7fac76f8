// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable class-methods-use-this */

import { Connection } from 'typeorm';
import { Factory, Seeder } from 'typeorm-seeding';

import { Role } from '../base/roles/roles.entity';

export default class CreateRoles implements Seeder {
  public async run(factory: Factory, connection: Connection): Promise<void> {
    await connection
      .createQueryBuilder()
      .insert()
      .into(Role)
      .values([
        {
          isDefaultRole: true,
          isDepartmentRole: false,
          name: 'super-admin',
          label: 'Developer',
          weight: -2,
        },
        {
          isDefaultRole: true,
          isDepartmentRole: false,
          name: 'admin',
          label: 'Admin',
          weight: -1,
        },
        {
          isDefaultRole: true,
          isDepartmentRole: false,
          name: 'staff',
          label: 'staff',
          weight: 0,
        },
      ])
      .execute();
  }
}
