import { Module, forwardRef } from '@nestjs/common';
import { PassportModule } from '@nestjs/passport';

import { ChatsGateway } from './chats.gateway';

import { AuthOptionsService } from '@/base/auth/auth-options.service';
import { CommonsModule } from '@/common/common.module';
import { ConfigModule } from '@/config/config.module';

@Module({
  imports: [
    PassportModule.registerAsync({
      imports: [ConfigModule],
      useClass: AuthOptionsService,
    }),
    forwardRef(() => CommonsModule),
  ],

  providers: [ChatsGateway],
})
export class EventsModule {}
