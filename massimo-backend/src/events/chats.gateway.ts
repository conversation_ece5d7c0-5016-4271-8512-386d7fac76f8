// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable class-methods-use-this */
// eslint-disable-next-line eslint-comments/disable-enable-pair
/* eslint-disable @typescript-eslint/explicit-module-boundary-types */
import { Inject, UseFilters, UseGuards, forwardRef } from '@nestjs/common';
import {
  ConnectedSocket,
  MessageBody,
  SubscribeMessage,
  WebSocketGateway,
  WebSocketServer,
} from '@nestjs/websockets';
import _ = require('lodash');
import { Server, Socket } from 'socket.io';
// eslint-disable-next-line import/no-extraneous-dependencies

import { CommonsService } from '@/common/common.service';
import { SuperAuthGuard } from '@/utils/ws-auth.guard';
import { WsExceptionFilter } from '@/utils/ws-exception-filter.util';
import { WsResponse } from '@/utils/ws-response.util';

@WebSocketGateway({
  cors: {
    origin: '*',
  },
})
export class ChatsGateway {
  @WebSocketServer()
  server: Server;

  constructor(
    @Inject(forwardRef(() => CommonsService))
    private readonly commonsService: CommonsService,
  ) {}

  @UseFilters(WsExceptionFilter)
  @UseGuards(SuperAuthGuard)
  @SubscribeMessage('message')
  async message(
    @MessageBody() data: ChatsEventType,
    @ConnectedSocket() client: Socket,
  ) {
    if (!data.chatId) {
      return WsResponse('ChatID');
    }

    const chatData = await this.commonsService.paginate(
      'chats',
      {
        id: data.chatId,
      },
      {
        offers: ['chats.offer'],
        organizations: ['chats.organization'],
        users: ['chats.person', 'chat.owner'],
        tasks: ['chats.task'],
      },
    );

    if (
      !chatData.data ||
      chatData.data.length === 0 ||
      chatData.data[0].inactive
    ) {
      return WsResponse('ChatID');
    }

    const updateData = await this.commonsService.update(
      'messages',
      [data],
      {},
      ['$'],
    );

    client.broadcast.emit('new-message', {
      err: null,
      data: updateData[0],
    });

    return WsResponse(null, updateData[0]);
  }

  @UseFilters(WsExceptionFilter)
  @UseGuards(SuperAuthGuard)
  @SubscribeMessage('delete')
  async deleteMessage(
    @MessageBody() data: ChatsEventType,
    @ConnectedSocket() client: Socket,
  ) {
    if (!_.isObject(data)) {
      return WsResponse('Data');
    }

    const { chatId, id } = data;

    if (!id) {
      return WsResponse('Id');
    }

    await this.commonsService.delete('messages', {
      id,
    });

    client.broadcast.emit('delete-message', {
      err: null,
      data: {
        chatId,
        id,
      },
    });

    return true;
  }
}

interface ChatsEventType {
  id: number;
  chatId: number;
}
