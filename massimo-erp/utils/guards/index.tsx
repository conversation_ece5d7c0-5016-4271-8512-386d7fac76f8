import {
  Box,
  Button,
  Center,
  Dialog,
  Group,
  Loader,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { useFullscreen, useOs, useViewportSize } from "@mantine/hooks";
import { openConfirmModal } from "@mantine/modals";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useEffect, useRef, useState } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { sdk } from "~utils/sdk";
import { useStore } from "~utils/store";

export type Guards = "user";

export const GuardMaster: CC<{
  guards: Guards[];
}> = (props) => {
  const { guards, children } = props;
  const { t } = useTranslation();
  const { colorScheme, colors } = useMantineTheme();
  const { replace, asPath, beforePopState } = useRouter();
  const { isLogin, activeUserId, setToken, setSelectedProject } = useStore(
    "global",
    (state) => ({
      isLogin: state.isLogin!,
      setToken: state.setToken!,
      activeUserId: state.activeUserId!,
      setSelectedProject: state.setSelectedProject!,
    }),
    shallow
  );
  const { initialized, toggleInitialized } = useStore(
    "temp",
    (state) => ({
      initialized: state.initialized!,
      toggleInitialized: state.toggleInitialized!,
    }),
    shallow
  );

  const { users, actionGetUsers, actionGetConnectionRequests } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      actionGetUsers: state.actionGetUsers!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
    }),
    shallow
  );

  const { actionGetGeneralReport } = useStore(
    "analytics",
    (state) => ({
      actionGetGeneralReport: state.actionGetGeneralReport!,
    }),
    shallow
  );
  const { actionGetNotifications } = useStore(
    "notifications",
    (state) => ({
      actionGetNotifications: state.actionGetNotifications!,
    }),
    shallow
  );

  const activeUser = users.find((user) => user.id === activeUserId);

  useEffect(() => {
    (async () => {
      if (!isLogin && asPath !== "/" && asPath !== "/register") {
        await replace("/");
      }

      if (!initialized) {
        await setToken();
        setSelectedProject();

        if (isLogin) {
          const { data, error } = await sdk.verify();

          if (data && users.findIndex((user) => user.id === data.id) === -1) {
            await actionGetUsers([data.id]);
            await actionGetGeneralReport();

            await actionGetConnectionRequests();
            await actionGetNotifications();

            console.debug("initialized");
            toggleInitialized(true);
          }
        } else {
          toggleInitialized(true);
        }
      } else if (isLogin && asPath === "/") {
        if (activeUser) {
          if (activeUser.isCustomer) {
            await replace("/profile");
          } else {
            await replace("/dashboard/crm");
          }
        } else {
          await replace("/apps/calendar");
        }
      }
    })();
  }, [
    actionGetConnectionRequests,
    actionGetGeneralReport,
    actionGetNotifications,
    actionGetUsers,
    activeUser,
    activeUserId,
    asPath,
    initialized,
    isLogin,
    replace,
    setSelectedProject,
    setToken,
    toggleInitialized,
    users,
  ]);

  const { toggle, fullscreen } = useFullscreen();
  const os = useOs();

  useEffect(() => {
    if (!fullscreen && (os === "ios" || os === "android")) {
      openConfirmModal({
        title: t("fullscreenModal.title"),
        children: <Text size="sm">{t("fullscreenModal.message")}</Text>,
        labels: { confirm: t("ok"), cancel: t("cancel") },
        closeOnCancel: false,
        onCancel: () => {},
        onConfirm: () => {
          toggle();
        },
        closeOnEscape: false,
        withCloseButton: false,
        closeOnClickOutside: false,
      });
    }
  }, [fullscreen, os, t, toggle]);

  return initialized ? (
    <>{children}</>
  ) : (
    <>
      <Box
        style={{
          position: "absolute",
          height: "100vh",
          width: "100vw",
          top: "0",
          left: "0",
          background: colorScheme === "dark" ? colors.dark[7] : "#fdfdfd",
          zIndex: Number.MAX_SAFE_INTEGER,
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
        }}
      >
        <Loader size="xl" variant="bars" />
      </Box>
    </>
  );
};
