import deepmerge from "deepmerge";
import { serverSideTranslations } from "next-i18next/serverSideTranslations";

import { <PERSON><PERSON>, Renderers, RenderVariant } from "./types/Common";

export interface GetTranslationsType {
  namespaces: string[];
  forcedLocale?: Langs;
}

type RequiredRenderers<T> = Required<Renderers<T>>;

export const getTranslations = async <
  T = {},
  U extends keyof Renderers<T> = keyof Renderers<T>
>(
  context: Parameters<RequiredRenderers<T>[U]>["0"],
  options: GetTranslationsType,
  defaults?: T
): Promise<T> => {
  const { namespaces, forcedLocale } = options;

  const customProps = await serverSideTranslations(
    forcedLocale ?? context.locale,
    namespaces
  );


  return deepmerge(defaults ?? {}, { props: customProps }) as any;
};
