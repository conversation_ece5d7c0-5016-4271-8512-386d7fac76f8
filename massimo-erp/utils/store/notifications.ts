import produce from "immer";
import { uniq, uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { NotificationType } from "~utils/types/Notification";
import { getStore } from ".";

export interface NotificationsState {
  notifications: NotificationType[];

  readAllNotifications(): void;

  actionGetNotifications(): Promise<void>;
  actionSendNotifications(
    dataList: {
      target: string;
      value: string;
      user?: number;
    }[]
  ): Promise<void>;
  actionViewNotification(idList: { id: number }[]): Promise<void>;
}

export const initNotificationsStore = (
  initial: Partial<NotificationsState> = {}
) => {
  return create<NotificationsState>()((set, get) => ({
    notifications: [],

    ...initial,

    readAllNotifications() {
      set(
        produce<NotificationsState>((state) => {
          state.notifications = get().notifications.map((e) => ({
            ...e,
            isReaded: true,
          }));
        })
      );
    },

    async actionGetNotifications() {
      const { data, error } = await sdk.make<{
        data: NotificationType[];
        relations: {};
      }>({
        method: "POST",
        url: `/notifications/list`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        const chatState = getStore("chat").getState();
        const tasksState = getStore("tasks").getState();
        const projectsState = getStore("projects").getState();
        const offersState = getStore("offers").getState();

        data.data.forEach(async (notification: NotificationType) => {
          if (notification.target === "messages") {
            await chatState.actionGetChats([+notification.value]);
            await chatState.actionGetMessages(+notification.value, 0);

            const activeChat = chatState.chats.find(
              (e) => e.id === +notification.value
            );

            if (activeChat?.type === "task") {
              await tasksState.actionGetTasks({
                id: activeChat.taskId,
              });
            } else if (activeChat?.type === "offer") {
              // await projectsState.actionGetProjects({});
            }
          } else if (notification.target === "tasks") {
            await tasksState.actionGetTasks({
              id: +notification.value,
            });
          }
        });

        set({
          notifications: data.data,
        });
      }
    },

    async actionSendNotifications(dataList) {
      const { data, error } = await sdk.make<{
        data: NotificationType[];
        relations: {};
      }>({
        method: "POST",
        url: `/notifications/add`,
        data: {
          data: dataList,
        },
      });

      if (error) {
        throw error;
      }
    },

    async actionViewNotification(idList) {
      const { data, error } = await sdk.make<boolean>({
        method: "POST",
        url: `/notifications/view`,
        data: {
          data: idList,
        },
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          notifications: get().notifications.filter(
            (e) => !idList.some((i) => i.id === e.id)
          ),
        });
      }
    },
  }));
};
