import produce from "immer";
import { findLast, isEmpty, isNull, isObject, reduce, uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { conflictMerge } from "~utils/tools";

import { ChatType, MessageType } from "~utils/types/Chat";
import { FileType } from "~utils/types/File";
import { getStore } from ".";

export interface ChatState {
  chats: ChatType[];
  subscribedChats: ChatType[];
  messages: MessageType[];
  activeChat: number | undefined;
  setActiveChat(chat: number): void;

  importChats(chats: ChatType[]): Promise<void>;
  actionGetMessages(chatId: number, page: number): Promise<boolean>;
  actionInitChat(chatId: number): Promise<void>;
  actionGetChats(idList: number[]): Promise<void>;
  actionSendMessage(message: Partial<MessageType>): Promise<void>;
  actionDeleteMessage(message: Partial<MessageType>): Promise<void>;
  actionUpdateMessage(message: MessageType): Promise<void>;
}

export const initChatStore = (initial: Partial<ChatState> = {}) => {
  return create<ChatState>()((set, get) => ({
    chats: [],
    messages: [],

    subscribedChats: [],

    activeChat: undefined,
    setActiveChat: (id) => {
      set(
        produce<ChatState>((state) => {
          state.activeChat = id;
        })
      );
    },

    async importChats(chatList) {
      const allChats = uniqBy(
        [...chatList.filter((x) => !isEmpty(x)), ...get().chats],
        "id"
      );

      const activeUserId = getStore("global").getState().activeUserId;
      const customers = getStore("customers").getState().customers;
      const userState = getStore("users").getState();

      await userState.actionGetUsers(
        chatList
          .filter((x) => !isEmpty(x))
          .map((x) => (activeUserId == x.personId ? x.ownerId : x.personId))
          .filter((x) => !isNull(x)) as number[]
      );

      const subscribedChats: ChatType[] = [];

      if (activeUserId) {
        for (let chat of allChats) {
          if (chat.organizationId) {
            const findedOrganization = customers.find(
              (customer) => customer.id === chat.organizationId
            );

            if (findedOrganization?.personIds?.includes(activeUserId)) {
              if (!chat.inactive) {
                subscribedChats.push(chat);
              }
            }
          } else if (
            chat.personId === activeUserId ||
            chat.ownerId === activeUserId
          ) {
            if (!chat.inactive) {
              subscribedChats.push(chat);
            }
          }
        }
      }

      set({
        chats: allChats,
        subscribedChats,
      });
    },

    async actionGetMessages(chatId: number, page?: number) {
      const { error, data } = await sdk.make<{
        data: MessageType[];
        relations: {
          files: FileType[];
        };
      }>({
        method: "POST",
        url: "/messages/paginate",
        data: {
          query: {
            chat: chatId,
          },
          page: page,
          limit: 100,
          desc: true,
        },
      });

      if (data) {
        set({
          messages: uniqBy([...data.data, ...get().messages], "id"),
        });

        if (data.relations) {
          const filesStore = getStore("files");

          if (data.relations.files) {
            await filesStore.getState().actionPaginateFiles(
              data.relations.files
                .filter((x) => !isEmpty(x))
                .filter((file: any) => file.type === "attachment")
                .map((file: any) => file.id),
              "attachment"
            );
          }
        }
      }

      const dataLength = data?.data.length || 0;

      if (page === 0) {
        return dataLength === 100;
      }

      return dataLength > 0;
    },

    async actionGetChats(idList) {
      const { error, data } = await sdk.make<{
        data: ChatType[];
        relations: {
          files: FileType[];
        };
      }>({
        method: "POST",
        url: "/chats/paginate",
        data: {
          id: idList,
        },
      });

      if (error) {
        throw error;
      }

      if (data) {
        const globalStore = getStore("global");
        const globalState = globalStore.getState();

        set({
          chats: reduce(
            uniqBy([...get().chats, ...data.data], "id"),
            function (acc: ChatType[], curr: ChatType) {
              if (curr.type === "person") {
                if (
                  [curr.ownerId, curr.personId].includes(
                    globalState.activeUserId as number
                  )
                ) {
                  const last = findLast(
                    acc,
                    (e) =>
                      e.type === "person" &&
                      ((e.ownerId === curr.ownerId &&
                        e.personId === curr.personId) ||
                        (e.ownerId === curr.personId &&
                          e.personId === curr.ownerId))
                  );
                  const lastIndex = acc.findIndex((e) => e.id === last?.id);

                  if (lastIndex === -1) {
                    acc.push(curr);
                  } else {
                    acc[lastIndex] = curr;
                  }
                }
              } else {
                acc.push(curr);
              }

              return acc;
            },
            []
          ),
          subscribedChats: reduce(
            uniqBy([...get().chats, ...data.data], "id"),
            function (acc: ChatType[], curr: ChatType) {
              if (curr.type === "person") {
                if (
                  [curr.ownerId, curr.personId].includes(
                    globalState.activeUserId as number
                  )
                ) {
                  const last = findLast(
                    acc,
                    (e) =>
                      e.type === "person" &&
                      ((e.ownerId === curr.ownerId &&
                        e.personId === curr.personId) ||
                        (e.ownerId === curr.personId &&
                          e.personId === curr.ownerId))
                  );
                  const lastIndex = acc.findIndex((e) => e.id === last?.id);

                  if (lastIndex === -1) {
                    acc.push(curr);
                  } else {
                    acc[lastIndex] = curr;
                  }
                }
              } else {
                acc.push(curr);
              }

              return acc;
            },
            []
          ),
        });
      }
    },

    async actionInitChat(chatId: number) {
      const { error, data } = await sdk.make<{
        data: MessageType[];
        relations: {
          files: FileType[];
        };
      }>({
        method: "POST",
        url: "/messages/init",
        data: {
          query: {
            chat: chatId,
          },
        },
      });

      if (data) {
        set({
          messages: uniqBy([...data.data, ...get().messages], "id"),
        });

        if (data.relations) {
          const filesStore = getStore("files");

          if (data.relations.files) {
            await filesStore.getState().actionPaginateFiles(
              data.relations.files
                .filter((x) => !isEmpty(x))
                .filter((file: any) => file.type === "attachment")
                .map((file: any) => file.id),
              "attachment"
            );
          }
        }
      }
    },

    async actionSendMessage(message) {
      const newMessageData: {
        data: MessageType;
      } = await new Promise((resolve) => {
        sdk.io.emit("message", message, (val: { data: MessageType }) =>
          resolve(val)
        );
      });

      if (!isObject(newMessageData) || isEmpty(newMessageData)) {
        throw new Error("Message cannot sent");
      }

      set({
        messages: uniqBy([...get().messages, newMessageData.data], "id"),
      });
    },

    async actionDeleteMessage(message) {
      await new Promise((resolve) => {
        sdk.io.emit("delete", message, (val: { data: MessageType }) =>
          resolve(val)
        );
      });

      set({
        messages: get().messages.filter((x) => x.id !== message.id),
      });
    },

    async actionUpdateMessage(message) {
      await new Promise((resolve) => {
        sdk.io.emit("message", message, (val: { data: MessageType }) => {
          resolve(val);
        });
      });

      if (!isObject(message) || isEmpty(message)) {
        throw new Error("Message cannot updated");
      }

      set({
        messages: uniqBy([...get().messages, message], "id"),
      });
    },

    ...initial,
  }));
};
