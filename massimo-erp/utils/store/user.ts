import create from "zustand";
import { persist } from "zustand/middleware";

import { persistOptions } from "~utils/persist";

export interface UserState {
  logged: boolean;
}

export const initUserStore = (initial: Partial<UserState> = {}) => {
  return create<UserState>()(
    persist<UserState>(
      (set, get) => ({
        logged: false,
        ...initial,
      }),
      {
        name: "user-state",
        getStorage: () => persistOptions,
        version: 3,
      }
    )
  );
};
