import produce from "immer";
import create from "zustand";
import { GetElementType } from "~types";

export interface AccessState {
  accesses: {
    id: number;
    name: string;
    path: string;
    defaultRoles: Record<string, number>;
    departmentRoles: Record<string, Record<string, number>>;
  }[];
  saveAccesses: (data: GetElementType<AccessState["accesses"]>[]) => void;
}

export const initAccessStore = (initial: Partial<AccessState> = {}) => {
  return create<AccessState>()((set, get) => ({
    accesses: [
      {
        id: 1,
        name: "Dashboard CRM",
        path: "/dashboard/crm",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 2,
        name: "Dashboard Analytics",
        path: "/dashboard/analytics",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 3,
        name: "CRM Invoices",
        path: "/apps/crm/invoices",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 4,
        name: "Requests",
        path: "/admin/requests",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 5,
        name: "Organization",
        path: "/admin/organization",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 6,
        name: "Customer",
        path: "/admin/customer",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 7,
        name: "Projects",
        path: "/admin/projects",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
    ],

    saveAccesses: (data) => {
      set(
        produce<AccessState>((state) => {
          state.accesses = data;
        })
      );
    },

    ...initial,
  }));
};
