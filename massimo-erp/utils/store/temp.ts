import produce from "immer";
import { merge, result } from "lodash";
import create from "zustand";
import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";
import { OfferType, RequestType } from "~utils/types/Project";
import { PostType } from "~utils/types/Post";
import { ProjectType } from "~utils/types/Project";
import { RoleType } from "~utils/types/Roles";

import { TaskStatusTypes, TaskTagType, TaskType } from "~utils/types/Task";
import { UserType } from "~utils/types/User";
import { DeepPartial, GetElementType } from "~types";
import { conflictMerge } from "~utils/tools";

export interface TempState {
  initialized: boolean;
  toggleInitialized(initialized?: boolean): void;

  isLoading: boolean;
  setIsLoading(value: boolean): void;

  loadingLevel: number;
  setLoadingLevel(value: number): void;

  updateInformation: {
    isOpen: boolean;
    type:
      | null
      | "connections"
      | "tasks"
      | "messages"
      | "projects"
      | "notifications";
  };
  setUpdateInformations(data: Partial<TempState["updateInformation"]>): void;

  mobileIsOpenNavbar: boolean;
  setMobileIsOpenNavbar(value: boolean): void;

  isOpenTaskViewModal: boolean;
  setIsOpenTaskViewModal(value: boolean): void;

  taskViewModalData: TaskType;
  setTaskViewModalData(data: Partial<TaskType>): void;

  taskChanges: Record<any, any>;
  setTaskChanges(data: TempState["taskChanges"]): void;

  //   New Task Modal
  isOpenNewTaskModal: boolean;
  setIsOpenNewTaskModal(value: boolean): void;
  newTaskModalData: TaskType;
  setNewTaskModalData(data: Partial<TaskType>): void;

  isOpenNewTagModal: boolean;
  setIsOpenNewTagModal(value: boolean): void;

  postModal: {
    modalType: "edit" | "new";
    isOpen: boolean;
    type: "story" | "post";
    data: Partial<PostType>;
  };
  setPostModal: (data: Partial<TempState["postModal"]>) => void;

  imageModal: string | undefined;
  setImageModal(value: string | undefined): void;

  newTag: TaskTagType;
  setNewTag(value: any): void;

  activeCalendarFilter: TaskStatusTypes[];
  setActiveCalendarFilter(value: TaskStatusTypes[]): void;

  // User Modal
  userModal: {
    isOpen: boolean;
    isOpenRoleValuesModal: boolean;
    type: "new" | "edit";
    data: Partial<UserType>;
  };
  setUserModal(data: Partial<TempState["userModal"]>): void;

  // Department Modal
  departmentModal: {
    isOpen: boolean;
    type: "new" | "edit";
    data: DepartmentType;
    changes: Record<string, any>;
  };
  setDepartmentModal(
    data: DeepPartial<TempState["departmentModal"]>,
    reset?: string[],
    target?: number[]
  ): void;

  // Role Selector In Department Modal
  roleSelectorModal: {
    isOpen: boolean;
    userId: number;
  };
  setRoleSelectorModal(value: Partial<TempState["roleSelectorModal"]>): void;

  userInformationsModal: {
    isOpen: boolean;
    data: any;
  };
  setUserInformationsModal(
    data: Partial<TempState["userInformationsModal"]>
  ): void;

  // Customer Modal
  customerModal: {
    isOpen: boolean;
    type: "new" | "edit";
    data: CustomerType;
    changes: Record<string, any>;
  };
  setCustomerModal(data: Partial<TempState["customerModal"]>): void;

  galleryPostViewModal: {
    isOpen: boolean;
    data: any;
  };
  setGalleryPostViewModal(
    data: Partial<{
      isOpen: boolean;
      data: any;
    }>
  ): void;

  // Project Modal
  projectModal: {
    isOpen: boolean;
    type: "new" | "edit";
    isOwner: boolean;
    data: ProjectType;
  };
  setProjectModal(value: Partial<TempState["projectModal"]>): void;

  isOpenSelectUserModal: boolean;
  setIsOpenSelectUserModal(value: boolean): void;

  lastOffer: OfferType;
  setLastOffer(data: Partial<OfferType>): void;

  sendRequestModal: {
    isOpen: boolean;
    isCustomer: string | undefined;
    data: RequestType;
  };
  setSendRequestModal(data: Partial<TempState["sendRequestModal"]>): void;

  requestViewModal: {
    isOpen: boolean;
    data: RequestType;
    changes: Record<string, Record<string, any>>;
  };
  setRequestViewModal(data: Partial<TempState["requestViewModal"]>): void;

  isOpenProjectSelectorModal: boolean;
  setIsOpenProjectSelectorModal(value: boolean): void;

  isOpenUnverifiedUsersModal: boolean;
  setIsOpenUnverifiedUsersModal(value: boolean): void;

  accesses: {
    id: number;
    name: string;
    path: string;
    defaultRoles: Record<string, number>;
    departmentRoles: Record<string, Record<string, number>>;
  }[];
  setAccesses(data: Partial<GetElementType<TempState["accesses"]>>): void;

  isOpenRegisterCompleteModal: boolean;
  setIsOpenRegisterCompleteModal(
    value: TempState["isOpenRegisterCompleteModal"]
  ): void;

  storyViewModal: {
    isOpen: boolean;
    storyId: number;
    group?: boolean;
  };
  setStoryViewModal(value: Partial<TempState["storyViewModal"]>): void;

  postViewModal: {
    isOpen: boolean;
    postId: number;
  };
  setPostViewModal(value: Partial<TempState["postViewModal"]>): void;

  viewLikesModal: {
    isOpen: boolean;
    postId: number;
  };
  setViewLikesModal: (value: Partial<TempState["viewLikesModal"]>) => void;

  storyViewsModal: {
    isOpen: boolean;
    storyId: number;
  };
  setStoryViewsModal: (value: Partial<TempState["storyViewsModal"]>) => void;

  profileSearchModal: {
    isOpen: boolean;
  };
  setProfileSearchModal: (
    value: Partial<TempState["profileSearchModal"]>
  ) => void;

  notification: {
    play: boolean;
  };
  setNotification: (value: Partial<TempState["notification"]>) => void;

  uploadProgress: null | number;
  setUploadProgress: (value: TempState["uploadProgress"]) => void;

  computed: {
    readonly blankTask: TaskType;
    readonly blankPost: PostType;
    readonly blankDepartment: DepartmentType;
    readonly blankUser: UserType;
    readonly blankCustomer: CustomerType;
    readonly blankProject: ProjectType;
    readonly blankRequest: RequestType;
    readonly blankOffer: OfferType;
  };
}

export const initTempStore = (initial: Partial<TempState> = {}) => {
  return create<TempState>()((set, get) => ({
    isOpenTaskModal: false,
    taskModalData: {},

    initialized: false,
    toggleInitialized: (initialized) => {
      initialized = initialized ?? !get().initialized;

      set(
        produce<TempState>((state) => {
          state.initialized = initialized!;
        })
      );
    },

    isLoading: false,
    setIsLoading: (value) => {
      set(
        produce<TempState>((state) => {
          state.isLoading = value;
        })
      );
    },

    loadingLevel: 0,
    setLoadingLevel: (value) => {
      set(
        produce<TempState>((state) => {
          state.loadingLevel = value;
        })
      );
    },

    updateInformation: {
      isOpen: false,
      type: null,
    },
    setUpdateInformations(data) {
      set(
        produce<TempState>((state) => {
          state.updateInformation = { ...get().updateInformation, ...data };
        })
      );
    },

    mobileIsOpenNavbar: false,
    setMobileIsOpenNavbar: (value) => {
      set(
        produce<TempState>((state) => {
          state.mobileIsOpenNavbar = value;
        })
      );
    },

    // Task View Modal
    isOpenTaskViewModal: false,
    setIsOpenTaskViewModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenTaskViewModal = value!;
        })
      );
    },

    taskViewModalData: {
      id: -1,
      title: "",
      description: "",
      private: false,
      customerPrivate: false,
      status: "Active",
      priority: "",
      projectId: -1,
      assigneeIds: [],
      contributorIds: [],
      contents: [],
      tags: [],
      start: new Date(),
      end: new Date(),
    },
    setTaskViewModalData: (data) => {
      set(
        produce<TempState>((state) => {
          state.taskViewModalData = { ...get().taskViewModalData, ...data };
        })
      );
    },

    taskChanges: {},
    setTaskChanges: (data) => {
      set(
        produce<TempState>((state) => {
          state.taskChanges = conflictMerge(get().taskChanges, data);
        })
      );
    },

    // New Task Modal
    isOpenNewTaskModal: false,
    setIsOpenNewTaskModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenNewTaskModal = value;
        })
      );
    },

    newTaskModalData: {
      id: -1,
      title: "",
      description: "",
      private: false,
      customerPrivate: false,
      status: "Active",
      priority: "",
      projectId: -1,
      assigneeIds: [],
      contributorIds: [],
      contents: [],
      tags: [],
      start: new Date(),
      end: new Date(),
    },
    setNewTaskModalData: (data) => {
      set(
        produce<TempState>((state) => {
          state.newTaskModalData = { ...state.newTaskModalData, ...data };
        })
      );
    },

    // New Tag Modal
    isOpenNewTagModal: false,
    setIsOpenNewTagModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenNewTagModal = value;
        })
      );
    },

    newTag: {
      value: "",
      label: "",
      color: "",
    },
    setNewTag: (value) => {
      set(
        produce<TempState>((state) => {
          state.newTag = { ...state.newTag, ...value };
        })
      );
    },

    postModal: {
      modalType: "new",
      isOpen: false,
      type: "post",
      data: {
        id: 0,
        type: "post",
        userId: 0,
        description: "",
        commentIds: [],
        likes: [],
        fileIds: [],
        updatedAt: new Date(),
      },
    },
    setPostModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.postModal = { ...get().postModal, ...data };
        })
      );
    },

    // Image Modal
    imageModal: undefined,
    setImageModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.imageModal = value;
        })
      );
    },

    // Calendar Filter
    activeCalendarFilter: [
      "To Do",
      "Active",
      "Completed",
      "Problems",
      "Archived",
    ],
    setActiveCalendarFilter: (value) => {
      set(
        produce<TempState>((state) => {
          state.activeCalendarFilter = value;
        })
      );
    },

    // User Modal
    userModal: {
      isOpen: false,
      isOpenRoleValuesModal: false,
      type: "new",
      data: {
        id: undefined,
        avatar: "",
        name: "",
        surname: "",
        phone: "",
        email: "",
        roleValues: {},
        defaultRoleId: [],
        plan: "",
        password: "",
        status: "pending",
        isValid: false,
        isBanned: false,
        isCustomer: false,
      },
    },
    setUserModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.userModal = { ...state.userModal, ...data };
        })
      );
    },

    // Department Modal
    departmentModal: {
      isOpen: false,
      type: "new",
      data: {
        id: -1,
        color: "#1864AB",
        name: "",
        label: "",
        description: "",
        roleIds: [],
        userIds: [],
        isVisible: true,
      },
      changes: {},
      order: ["$", "roles", "users"],
    },
    setDepartmentModal: (data, reset = [], target = []) => {
      set(
        produce<TempState>((state) => {
          state.departmentModal = conflictMerge(get().departmentModal, data);

          if (reset.includes("changes")) {
            state.departmentModal.changes = {};
          }

          if (reset.includes("data")) {
            state.departmentModal.data = get().computed.blankDepartment;
          }

          if (reset.includes("userIds") && target.length > 0) {
            state.departmentModal.data.userIds =
              get().departmentModal.data.userIds.filter(
                (e) => !target.includes(e)
              );
          }

          if (reset.includes("roleIds") && target.length > 0) {
            state.departmentModal.data.roleIds =
              get().departmentModal.data.roleIds.filter(
                (e) => !target.includes(e)
              );
            state.departmentModal.changes.roles = !!get().departmentModal
              .changes.roles
              ? get().departmentModal.changes.roles.filter(
                  (e: RoleType) => !target.includes(e.id as number)
                )
              : [];
          }
        })
      );
    },

    // Role Selector In Department Modal
    roleSelectorModal: {
      isOpen: false,
      userId: 0,
    },
    setRoleSelectorModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.roleSelectorModal = { ...state.roleSelectorModal, ...value };
        })
      );
    },

    // User Informations Modal
    userInformationsModal: {
      isOpen: false,
      data: {
        id: -1,
      },
    },
    setUserInformationsModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.userInformationsModal = {
            ...state.userInformationsModal,
            ...data,
          };
        })
      );
    },

    // Customer Modal
    customerModal: {
      isOpen: false,
      type: "new",
      data: {
        id: -1,
        avatar: "",
        location: "tr",
        shortName: "",
        fullName: "",
        phone: "",
        email: "",
        taxDepartment: "",
        taxNumber: "",
        requestIds: [],
        personIds: [],
        address: "",
        offers: [],
        tasks: [],
        recentActivities: [],
      },
      changes: {},
    },
    setCustomerModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.customerModal = { ...state.customerModal, ...data };
        })
      );
    },

    // Select User Modal
    isOpenSelectUserModal: false,
    setIsOpenSelectUserModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenSelectUserModal = value;
        })
      );
    },

    galleryPostViewModal: {
      isOpen: false,
      data: undefined,
    },
    setGalleryPostViewModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.galleryPostViewModal = {
            ...state.galleryPostViewModal,
            ...data,
          };
        })
      );
    },

    // Project Modal
    projectModal: {
      isOpen: false,
      type: "new",
      isOwner: false,
      data: {
        id: -1,
        offerIds: [],
        requestId: -1,
        departmentIds: [],
        roleIds: [],
        allowedUserIds: [],
        restrictedUserIds: [],
        priority: "Medium",
      },
    },

    setProjectModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.projectModal = { ...get().projectModal, ...value };
        })
      );
    },

    lastOffer: {
      id: -1,
      name: "",
      description: "",
      start: new Date(),
      end: new Date(),
      status: "Offer",
      priority: "Medium",
      budget: 0,
      projectId: -1,
      invoiceIds: [],
      contents: [],
    },
    setLastOffer: (data) => {
      set(
        produce<TempState>((state) => {
          state.lastOffer = { ...state.lastOffer, ...data };
        })
      );
    },

    // New Request Modal
    sendRequestModal: {
      isOpen: false,
      isCustomer: "customer-1",
      data: {
        id: -1,
        name: "",
        description: "",
        departmentIds: [],
        customerId: -1,
        budget: 0,
        start: new Date(),
        end: new Date(),
        projectId: -1,
        approverId: -1,
      },
    },
    setSendRequestModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.sendRequestModal = { ...state.sendRequestModal, ...data };
        })
      );
    },

    // Request View Moddal
    requestViewModal: {
      isOpen: false,
      data: {
        id: -1,
        name: "",
        description: "",
        departmentIds: [],
        customerId: -1,
        budget: 0,
        start: new Date(),
        end: new Date(),
        projectId: -1,
        approverId: -1,
      },
      changes: {},
    },
    setRequestViewModal: (data) => {
      set(
        produce<TempState>((state) => {
          state.requestViewModal = conflictMerge(get().requestViewModal, data);
        })
      );
    },

    // Project Selector Modal
    isOpenProjectSelectorModal: false,
    setIsOpenProjectSelectorModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenProjectSelectorModal = value;
        })
      );
    },

    // Customer Invites Modal
    isOpenUnverifiedUsersModal: false,
    setIsOpenUnverifiedUsersModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenUnverifiedUsersModal = value;
        })
      );
    },

    accesses: [
      {
        id: 1,
        name: "Dashboard CRM",
        path: "/dashboard/crm",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 2,
        name: "Dashboard Analytics",
        path: "/dashboard/analytics",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 3,
        name: "CRM Invoices",
        path: "/apps/crm/invoices",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 4,
        name: "Requests",
        path: "/admin/requests",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 5,
        name: "Organization",
        path: "/admin/organization",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 6,
        name: "Customer",
        path: "/admin/customer",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
      {
        id: 7,
        name: "Projects",
        path: "/admin/projects",
        defaultRoles: {
          admin: 4,
          super_admin: 4,
        },
        departmentRoles: {
          software: {
            frontend: 3,
            backend: 4,
          },
        },
      },
    ],
    setAccesses: (data) => {
      set(
        produce<TempState>((state) => {
          let index = state.accesses.findIndex((e) => e.id === data.id);
          state.accesses[index] = { ...state.accesses[index], ...data };
        })
      );
    },

    isOpenRegisterCompleteModal: false,
    setIsOpenRegisterCompleteModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.isOpenRegisterCompleteModal = value;
        })
      );
    },

    storyViewModal: {
      isOpen: false,
      storyId: -1,
      group: false,
    },
    setStoryViewModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.storyViewModal = { ...get().storyViewModal, ...value };
        })
      );
    },

    postViewModal: {
      isOpen: false,
      postId: -1,
    },
    setPostViewModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.postViewModal = { ...get().postViewModal, ...value };
        })
      );
    },

    viewLikesModal: {
      isOpen: false,
      postId: 0,
    },
    setViewLikesModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.viewLikesModal = { ...get().viewLikesModal, ...value };
        })
      );
    },

    storyViewsModal: {
      isOpen: false,
      storyId: 0,
    },
    setStoryViewsModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.storyViewsModal = { ...get().storyViewsModal, ...value };
        })
      );
    },

    profileSearchModal: {
      isOpen: false,
    },
    setProfileSearchModal: (value) => {
      set(
        produce<TempState>((state) => {
          state.profileSearchModal = { ...get().profileSearchModal, ...value };
        })
      );
    },

    notification: {
      play: false,
    },
    setNotification: (value) => {
      set(
        produce<TempState>((state) => {
          state.notification = { ...get().notification, ...value };
        })
      );
    },

    uploadProgress: 0,
    setUploadProgress: (value) => {
      set(
        produce<TempState>((state) => {
          state.uploadProgress = value;
        })
      );
    },

    computed: {
      get blankTask() {
        const result: TaskType = {
          id: -1,
          title: "",
          description: "",
          private: false,
          customerPrivate: false,
          status: "Active",
          priority: "",
          projectId: -1,
          assigneeIds: [],
          contributorIds: [],
          start: new Date(),
          end: new Date(),
        };

        return result;
      },

      get blankDepartment() {
        const result: DepartmentType = {
          id: -1,
          color: "#1864AB",
          name: "",
          description: "",
          roleIds: [],
          label: "",
          userIds: [],
          isVisible: true,
        };

        return result;
      },

      get blankUser() {
        const result: UserType = {
          id: 0,
          avatar: "",
          name: "",
          surname: "",
          phone: "",
          email: "",
          fileIds: [],
          roleValues: {},
          defaultRoleId: [],
          plan: "",
          password: "",
          departmentIds: [],
          allowedProjectIds: [],
          restrictedProjectIds: [],
          status: "pending",
          isValid: false,
          isBanned: false,
          isCustomer: false,
          organizationIds: [],
          chatIds: [],
          ownedChatIds: [],
        };

        return result;
      },

      get blankCustomer() {
        const result: CustomerType = {
          id: -1,
          avatar: "",
          location: "tr",
          shortName: "",
          fullName: "",
          phone: "",
          email: "",
          taxDepartment: "",
          taxNumber: "",
          address: "",
          chatId: undefined,
          personIds: [],
          offers: [],
          requestIds: [],
          tasks: [],
          recentActivities: [],
        };

        return result;
      },

      get blankPost() {
        const result: PostType = {
          id: 0,
          type: "post",
          userId: 0,
          description: "",
          isLiked: null,
          likeCount: 0,
          commentCount: 0,
          fileIds: [],
          updatedAt: new Date(),
        };

        return result;
      },

      get blankProject() {
        const result: ProjectType = {
          id: -1,
          priority: "Medium",
          offerIds: [],
          requestId: -1,
          departmentIds: [],
          roleIds: [],
          allowedUserIds: [],
          restrictedUserIds: [],
        };

        return result;
      },

      get blankOffer() {
        const result: OfferType = {
          id: -1,
          name: "",
          description: "",
          start: new Date(),
          end: new Date(),
          budget: 0,
          status: "Offer",
          projectId: -1,
          invoiceIds: [],
        };

        return result;
      },

      get blankRequest() {
        const result: RequestType = {
          id: -1,
          name: "",
          description: "",
          budget: 0,
          start: new Date(),
          end: new Date(),
          approverId: -1,
          customerId: -1,
          projectId: -1,
        };

        return result;
      },
    },

    ...initial,
  }));
};
