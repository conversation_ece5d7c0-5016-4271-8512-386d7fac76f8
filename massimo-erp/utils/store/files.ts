import produce from "immer";
import { uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { FileType } from "~utils/types/File";
import { getStore } from ".";

export interface FilesState {
  files: [number, string, string][];

  actionPaginateFiles(
    idList: number[],
    type?: "attachment" | "avatar"
  ): Promise<void>;
  actionUploadFiles(
    fileList: FileType[],
    type: "attachment" | "avatar" | "post",
    userId?: number,
    increaseUserPrev?: boolean
  ): Promise<number[]>;
}

export const initFilesStore = (initial: Partial<FilesState> = {}) => {
  return create<FilesState>()((set, get) => ({
    files: [],

    ...initial,

    async actionPaginateFiles(idList, type) {
      const { error, data } = await sdk.make<{
        data: string[];
      }>({
        method: "POST",
        url: "/files/paginate",
        data: {
          query: {
            id: idList,
            ...(type ? { type } : {}),
          },
          page: 0,
          limit: idList.length,
        },
      });

      if (error) {
        throw error;
      }

      if (!data || !data.data) {
        throw new Error("404");
      }

      const files = get().files;
      const newData = data.data.map((file, i) => {
        return [idList[i], type, file];
      }) as [number, string, string][];

      set({
        files: uniqBy([...newData, ...files], "0"),
      });
    },

    async actionUploadFiles(
      fileList,
      type = "attachment",
      userId,
      increaseUserPrev
    ) {
      const formData = new FormData();
      const updateData: Record<string, string | number> = {};

      fileList.forEach((data) => {
        if (data.id) {
          updateData[data.file.name] = data.id;
        }

        formData.append(type, data.file);
      });

      formData.set("update", JSON.stringify(updateData));

      if (!!userId) {
        formData.set("userId", `${userId}`);
      }

      const { error, data } = await sdk.make<number[]>(
        {
          method: "POST",
          url: "/files/upload",
          headers: {
            "Content-Type": "multipart/form-data",
          },
          data: formData,
        },
        true
      );

      if (error) {
        throw error;
      }

      if (!data) {
        throw new Error("404");
      }

      await get().actionPaginateFiles(data);

      if (increaseUserPrev) {
        const usersStore = getStore("users");

        usersStore.setState({
          userPrev: usersStore.getState().userPrev + 1,
        });
      }

      return data;
    },
  }));
};
