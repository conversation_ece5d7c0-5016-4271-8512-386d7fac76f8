import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { uniq, uniqBy } from "lodash";
import { SVGProps } from "react";
import create from "zustand";
import { persist } from "zustand/middleware";
import { persistOptions } from "~utils/persist";
import { sdk } from "~utils/sdk";
import { conflictMerge } from "~utils/tools";
import { TaskType } from "~utils/types/Task";
import { getStore } from ".";

export interface AnalyticsState {
  externalLink: {
    data: {
      amount: string;
      trendAmount: number;
      color: string;
      title: string;
      icon: (props: SVGProps<SVGSVGElement>) => JSX.Element;
    }[];
    series: {
      name: string;
      data: number[];
    }[];
  };
  meetings: {
    chipText: string;
    chipColor: string;
    title: string;
    avatar: string;
    subtitle: string;
  }[];
  organicSessions: {
    name: string;
    value: number;
  }[];
  paymentHistory: {
    id: string;
    card: {
      type: string;
      method: string;
      last4Digits: string;
    };
    date: Date;
    spendings: {
      spent: number;
      balance: number;
    };
  }[];
  projectTimeline: {
    labels: string[];
    series: {
      data: {
        x: string;
        y: number[];
      }[];
    }[];
    projectList: {
      title: string;
      task: string;
    }[];
  };
  socialNetworkVisits: {
    data: {
      value: number;
      growUp: number;
    };
    series: {
      amount: string;
      chipText: string;
      title: string;
      imgAlt: string;
      chipColor: string;
      subtitle: string;
      imgSrc: string;
    }[];
  };
  totalGrowth: {
    value: string;
    growth: string;
    series: number[];
  };
  totalImpressions: string;
  totalOrders: {
    value: string;
    growth: string;
  };
  totalProfit: {
    value: string;
    growth: string;
    series: {
      name: string;
      data: number[];
    }[];
  };
  totalRevenue: {
    value: string;
    growth: string;
    series: {
      name: string;
      data: number[];
    }[];
  };
  totalSales: {
    value: string;
    growth: string;
  };
  visitsByDay: {
    total: string;
    series: {
      data: number[];
    }[];
    mostVisitedDay: {
      name: string;
      value: string;
    };
  };
  weekslyOverview: {
    name: string;
    type: string;
    data: number[];
  }[];
  metrics: {
    users: number;
    roles: number;
    accesss: number;
    customers: number;
    departments: number;
    offers: number;
    projects: number;
    requests: number;
    tasks: number;
    files: number;
    chats: number;
  };
  profileTaskTimeline: {
    name: string;
    color: string;
    tasks: TaskType[];
  }[];
  highPriorityTaskIds: number[];
  tasksTimeline: {
    name: string;
    color: string;
    tasks: TaskType[];
  }[];

  actionGetGeneralReport(): Promise<void>;
  actionGetCrmReport(userId?: number): Promise<void>;
  actionGetUserReport(userId?: number): Promise<void>;
  actionGetReport(options: {
    target: keyof AnalyticsState["metrics"];
    query: any;
    limit?: number | string;
    page?: number | string;
  }): Promise<void>;
}

export const initAnalyticsStore = (initial: Partial<AnalyticsState> = {}) => {
  return create<AnalyticsState>()((set, get) => ({
    metrics: {
      users: 0,
      roles: 0,
      accesss: 0,
      customers: 0,
      departments: 0,
      offers: 0,
      projects: 0,
      requests: 0,
      tasks: 0,
      files: 0,
      messages: 0,
      chats: 0,
    },

    searchMetrics: {
      users: 0,
      roles: 0,
      accesss: 0,
      customers: 0,
      departments: 0,
      offers: 0,
      projects: 0,
      requests: 0,
      tasks: 0,
      files: 0,
      messages: 0,
      chats: 0,
    },

    externalLink: {
      data: [
        {
          amount: "₺0",
          trendAmount: 0,
          color: "gray",
          title: "Google Analytics",
          icon: ChevronUpIcon,
        },
        {
          amount: "₺0",
          trendAmount: 0,
          color: "gray",
          title: "Facebook Ads",
          icon: ChevronDownIcon,
        },
      ],
      series: [
        {
          name: "Google Analytics",
          data: [0, 0, 0, 0, 0, 0, 0],
        },
        {
          name: "Facebook Ads",
          data: [0, 0, 0, 0, 0, 0, 0],
        },
      ],
    },

    meetings: [
      // {
      //   chipText: "Business",
      //   chipColor: "blue",
      //   title: "Call with Woods",
      //   avatar: "/images/avatars/4.png",
      //   subtitle: "21 Jul | 08:20-10:30",
      // },
    ],

    paymentHistory: [
      // {
      //   id: "payment-1",
      //   card: {
      //     type: "visa",
      //     method: "Credit Card",
      //     last4Digits: "4399",
      //   },
      //   date: new Date(),
      //   spendings: {
      //     spent: 2820,
      //     balance: 10450,
      //   },
      // },
    ],

    projectTimeline: {
      labels: [
        "Development Apps",
        "UI Design",
        "IOS Application",
        "Web App Wireframing",
        "Prototyping",
      ],
      series: [
        {
          data: [
            // {
            //   x: "Catherine",
            //   y: [
            //     new Date(`${new Date().getFullYear()}-01-01`).getTime(),
            //     new Date(`${new Date().getFullYear()}-04-02`).getTime(),
            //   ],
            // },
          ],
        },
      ],
      projectList: [
        {
          title: "IOS Application",
          task: "0/0",
          color: "red",
        },
        {
          title: "Web Application",
          task: "0/0",
          color: "green",
        },
        {
          title: "Bank Dashboard",
          task: "0/0",
          color: "gray",
        },
        {
          title: "UI Kit Design",
          task: "0/0",
          color: "blue",
        },
      ],
    },

    socialNetworkVisits: {
      data: {
        value: 0,
        growUp: 0,
      },
      series: [
        {
          amount: "0",
          chipText: "0%",
          title: "Facebook",
          imgAlt: "facebook",
          chipColor: "gray",
          subtitle: "Social Media",
          imgSrc: "/images/social-facebook.png",
        },
        {
          amount: "0",
          chipText: "0%",
          title: "Dribbble",
          imgAlt: "dribbble",
          chipColor: "gray",
          subtitle: "Community",
          imgSrc: "/images/social-dribbble.png",
        },
        {
          amount: "0",
          chipText: "0%",
          title: "Twitter",
          imgAlt: "twitter",
          chipColor: "gray",
          subtitle: "Social Media",
          imgSrc: "/images/social-twitter.png",
        },
        {
          amount: "0",
          chipText: "0%",
          title: "Instagram",
          imgAlt: "instagram",
          chipColor: "gray",
          subtitle: "Social Media",
          imgSrc: "/images/social-instagram.png",
        },
      ],
    },

    totalGrowth: {
      value: "0",
      growth: "0",
      series: [0, 0, 0],
    },

    totalOrders: {
      value: "0",
      growth: "0",
    },

    totalProfit: {
      value: "0",
      growth: "0",
      series: [
        {
          name: "Earning",
          data: [0, 0, 0, 0, 0, 0, 0],
        },
        {
          name: "Expense",
          data: [0, 0, 0, 0, 0, 0, 0],
        },
      ],
    },

    totalRevenue: {
      value: "0",
      growth: "0",
      series: [
        {
          name: "Earning",
          data: [0, 0, 0, 0],
        },
        {
          name: "Expense",
          data: [0, 0, 0, 0],
        },
      ],
    },

    totalSales: {
      value: "0",
      growth: "0",
    },

    visitsByDay: {
      total: "0",
      series: [{ data: [0, 0, 0, 0, 0, 0, 0] }],
      mostVisitedDay: {
        name: "Thursday",
        value: "0",
      },
    },

    weekslyOverview: [
      {
        name: "Sales",
        type: "column",
        data: [0, 0, 0, 0, 0, 0, 0],
      },
      {
        name: "Sales",
        type: "line",
        data: [0, 0, 0, 0, 0, 0, 0],
      },
    ],

    profileTaskTimeline: [
      {
        name: "Todo",
        color: "blue",
        tasks: [],
      },
      {
        name: "Overdue",
        color: "red",
        tasks: [],
      },
    ],

    organicSessions: [
      {
        name: "Empty",
        value: 0,
      },
    ],

    totalImpressions: "",

    highPriorityTaskIds: [],

    tasksTimeline: [
      {
        name: "overdue",
        color: "red",
        tasks: [],
      },
      {
        name: "today",
        color: "orange",
        tasks: [],
      },
      {
        name: "nextWeek",
        color: "blue",
        tasks: [],
      },
      {
        name: "nextMonth",
        color: "green",
        tasks: [],
      },
    ],

    ...initial,

    async actionGetGeneralReport() {
      const { data, error } = await sdk.make<Record<string, number>>({
        method: "POST",
        url: "/analytics/general",
        data: {},
      });

      if (data) {
        set({
          metrics: conflictMerge(get().metrics, data),
        });
      }
    },

    async actionGetReport(options) {
      const { data, error } = await sdk.make<Record<string, number>>({
        method: "POST",
        url: "/analytics/target",
        data: options,
      });

      if (data) {
        set({
          metrics: conflictMerge(get().metrics, data),
        });
      }
    },

    async actionGetCrmReport(userId) {
      const selectedProjectId = getStore("global").getState().selectedProjectId;

      const tasksStore = getStore("tasks");

      const { data, error } = await sdk.make<Record<string, any>>({
        method: "POST",
        url: "/analytics/crm",
        data: {
          userId,
          projectId: selectedProjectId,
          date: new Date(),
        },
      });

      tasksStore.setState({
        filteredTasks: [],
      });

      if (data) {
        set({
          highPriorityTaskIds: data.highPriorityTasks.map((e: any) => e.id),
          organicSessions: data.taskCount,
          totalImpressions: data.totalCompletedTasks,
          weekslyOverview: data.weekslyOverview,
          tasksTimeline: data.tasksTimeline,
        });

        tasksStore.setState({
          filteredTasks: uniqBy(
            [...tasksStore.getState().filteredTasks, ...data.highPriorityTasks],
            "id"
          ),
          tasks: uniqBy(
            [...tasksStore.getState().tasks, ...data.highPriorityTasks],
            "id"
          ),
        });

        // set(data);
      }
    },

    async actionGetUserReport(userId) {
      const selectedProjectId = getStore("global").getState().selectedProjectId;

      const { data, error } = await sdk.make<Record<string, any>>({
        method: "POST",
        url: "/analytics/user",
        data: {
          userId,
          projectId: selectedProjectId,
          date: new Date(),
        },
      });

      if (data) {
        const usersStore = getStore("users");

        const timeline = [
          {
            name: "Todo",
            color: "blue",
            tasks: data.activeTasks,
          },
          {
            name: "Overdue",
            color: "red",
            tasks: data.overdueTasks,
          },
        ];
        set({
          profileTaskTimeline: timeline,
        });

        usersStore.setState({
          users: uniqBy(
            [...usersStore.getState().users, ...data.relations.assignees],
            "id"
          ),
        });
      }
    },
  }));
};
