import produce from "immer";
import create from "zustand";
import { GetElementType } from "~types";

import { PostType } from "~utils/types/Post";
import {
  TaskType,
  TaskStatusTypes,
  TaskPriorityType,
  TaskTagType,
} from "~utils/types/Task";

export interface DataState {
  navigations: (
    | {
        color: string;
        title: string;
        path?: string;
        childrens?: {
          path: string;
          title: string;
        }[];
      }
    | {
        label: string;
      }
  )[];

  userStatusColors: Record<string, string>;

  tags: TaskTagType[];
  addTag(tag: TaskTagType): void;
  removeTag(i: number): void;
}

export const initDataStore = (initial: Partial<DataState> = {}) => {
  return create<DataState>()((set, get) => ({
    navigations: [
      {
        color: "red",
        title: "Dashboard",
        childrens: [
          {
            path: "/dashboard/crm",
            title: "CRM",
          },
          // {
          //   path: "/dashboard/analytics",
          //   title: "Analytics",
          // },
        ],
      },
      {
        color: "pink",
        title: "Profile",
        path: "/profile",
      },
      {
        color: "grape",
        title: "Explore",
        path: "/explore",
      },
      {
        label: "Apps",
      },
      {
        color: "violet",
        title: "Chat",
        path: "/apps/chat",
      },
      {
        color: "indigo",
        title: "Calendar",
        path: "/apps/calendar",
      },
      {
        color: "blue",
        title: "Kanban",
        path: "/apps/kanban",
      },
      // {
      //   color: "blue",
      //   title: "CRM",
      //   childrens: [
      //     {
      //       path: "/apps/crm/invoices",
      //       title: "Invoices",
      //     },
      //   ],
      // },
      {
        color: "cyan",
        title: "Support",
        childrens: [
          {
            title: "FAQ",
            path: "/faq",
          },
          {
            title: "Privacy",
            path: "/privacy",
          },
          // {
          //   title: "Contact Us",
          //   path: "/contact",
          // },
        ],
      },
      {
        label: "Admin",
      },
      {
        color: "teal",
        title: "Requests",
        path: "/admin/requests",
      },
      {
        color: "green",
        title: "Departments",
        path: "/admin/departments",
      },
      {
        color: "lime",
        title: "Users",
        path: "/admin/users",
      },
      {
        color: "yellow",
        title: "Customers",
        path: "/admin/customers",
      },
      {
        color: "orange",
        title: "Projects",
        path: "/admin/projects",
      },
      // {
      //   color: "orange",
      //   title: "Permissions",
      //   path: "/admin/permissions",
      // },
      // {
      //   color: "gray",
      //   title: "Settings",
      //   path: "/admin/settings",
      // },
    ],

    userStatusColors: {
      online: "green",
      busy: "orange",
      offline: "gray",
    },

    tags: [
      {
        value: "tag1",
        label: "Important",
        color: "red",
      },
      {
        value: "tag2",
        label: "Tag",
        color: "blue",
      },
    ],
    addTag: (tag: TaskTagType) => {
      set(
        produce<DataState>((state) => {
          let c = { ...tag };
          c.value = get().tags.length + "";
          state.tags.push(c);
        })
      );
    },
    removeTag: (i: number) => {
      set(
        produce<DataState>((state) => {
          state.tags.splice(i, 1);
        })
      );
    },

    ...initial,
  }));
};
