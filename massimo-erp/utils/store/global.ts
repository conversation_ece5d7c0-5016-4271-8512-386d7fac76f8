import produce from "immer";
import create from "zustand";
import { setCookie } from "cookies-next";
import { ColorScheme } from "@mantine/core";
import { persist } from "zustand/middleware";

import { persistOptions } from "~utils/persist";
import { getStore } from ".";
import { UserType } from "~utils/types/User";
import { sdk } from "~utils/sdk";

export interface GlobalState {
  token: string | null;

  colorScheme: ColorScheme;
  toggleColorScheme(colorScheme?: ColorScheme): void;
  version: string;
  isLogin: boolean;
  setIsLogin(value: boolean): void;
  isFocus: boolean;
  setIsFocus(value: boolean): void;
  activeUserId: number | null;
  setActiveUserId(user: number): void;
  isNavbarMinimized: boolean;
  setNavbarMinimize(value: boolean): void;
  isNavbarHover: boolean;
  setNavbarHover(value: boolean): void;
  selectedProjectId?: number;
  setSelectedProject(id?: number): void;
  isAcceptCookies: boolean;
  setCookies(value: boolean): void;

  setToken(): Promise<void>;
  actionSignIn(user: Partial<UserType>): Promise<void>;
  actionLogout(): Promise<void>;
  deleteData(target: string, id: number): Promise<void>;
}

export const initGlobalStore = (initial: Partial<GlobalState> = {}) => {
  const store = create<GlobalState>()(
    persist<GlobalState>(
      (set, get) => ({
        token: null,
        version: "beta",
        isLogin: false,
        colorScheme: "dark",
        isNavbarMinimized: true,
        activeUserId: null,
        selectedProjectId: undefined,
        isAcceptCookies: false,
        isNavbarHover: true,
        isFocus: false,

        ...initial,

        toggleColorScheme: (colorScheme) => {
          if (!colorScheme) {
            colorScheme = get().colorScheme === "dark" ? "light" : "dark";
          }

          const user = getStore("user");

          user.setState((state) => ({
            logged: !state.logged,
          }));

          setCookie("mantine-color-scheme", colorScheme, {
            maxAge: 60 * 60 * 24 * 30,
          });

          set(
            produce((state) => {
              state.colorScheme = colorScheme;
            })
          );
        },

        setIsLogin: (value) => {
          set(
            produce<GlobalState>((state) => {
              if (!value) {
                state.token = null;
              }

              state.isLogin = value;
            })
          );
        },

        setActiveUserId: (user) => {
          set(
            produce<GlobalState>((state) => {
              state.activeUserId = user;
            })
          );
        },

        setNavbarMinimize: (value) => {
          set(
            produce<GlobalState>((state) => {
              state.isNavbarMinimized = value;
            })
          );
        },

        setNavbarHover: (value) => {
          set(
            produce<GlobalState>((state) => {
              state.isNavbarHover = value;
            })
          );
        },

        setSelectedProject: (id) => {
          set(
            produce<GlobalState>((state) => {
              state.selectedProjectId = id;
            })
          );
        },

        setIsFocus: (value) => {
          set(
            produce<GlobalState>((state) => {
              state.isFocus = value;
            })
          );
        },

        setCookies: (value) => {
          set(
            produce<GlobalState>((state) => {
              state.isAcceptCookies = value;
            })
          );
        },

        setToken: async () => {
          let token = get().token;

          if (token) {
            await sdk.setToken(token);
          }
        },

        async actionSignIn(user) {
          const { error, data } = await sdk.make<UserType>(
            {
              method: "POST",
              url: "/auth/signin",
              data: { email: user.email, password: user.password },
            },
            true
          );

          if (error) {
            throw error;
          }

          set({
            token: (data as any).token,
            activeUserId: data!.id,
          });

          await get().setToken();

          const usersStore = getStore("users");

          try {
            await usersStore.getState().actionGetUsers([data!.id]);
          } finally {
            set({
              isLogin: true,
            });
          }
        },

        async actionLogout() {
          const tempStore = getStore("temp");

          tempStore.setState({
            initialized: false,
          });

          sdk.clear();

          set({
            isLogin: false,
            activeUserId: null,
            token: null,
          });
        },

        async deleteData(target, id) {
          const response = sdk.make({
            method: "DELETE",
            url: "/access/delete",
            data: {
              data: {
                target,
                id,
              },
            },
          });

          if (target === "users") {
            const usersStore = getStore("users");
            const usersState = usersStore.getState();

            usersStore.setState({
              users: usersState.users.filter((e) => e.id !== id),
              filteredUsers: usersState.filteredUsers.filter(
                (e) => e.id !== id
              ),
              userPrev: usersStore.getState().userPrev + 1,
            });
          } else if (target === "projects") {
            const projectsStore = getStore("projects");
            const projectState = projectsStore.getState();

            projectsStore.setState({
              projects: projectState.projects.filter((e) => e.id !== id),
              filteredProjects: projectState.filteredProjects.filter(
                (e) => e.id !== id
              ),
              projectPrev: projectState.projectPrev + 1,
            });
          } else if (target === "tasks") {
            const tasksStore = getStore("tasks");
            const taskstate = tasksStore.getState();

            tasksStore.setState({
              tasks: taskstate.tasks.filter((e) => e.id !== id),
              filteredTasks: taskstate.filteredTasks.filter((e) => e.id !== id),
              taskPrev: taskstate.taskPrev + 1,
            });
          } else if (target === "customers") {
            const customerStore = getStore("customers");
            const customersState = customerStore.getState();

            customerStore.setState({
              customers: customersState.customers.filter((e) => e.id !== id),
              filteredCustomers: customersState.filteredCustomers.filter(
                (e) => e.id !== id
              ),
            });
          }
        },
      }),
      {
        name: "global-state",
        getStorage: () => persistOptions,
        version: 5,
      }
    )
  );

  sdk.on("security", (data: keyof typeof sdk["errors"]) => {
    if (data === 401) {
      console.debug("security issue");

      store.getState().actionLogout();
    }
  });

  return store;
};
