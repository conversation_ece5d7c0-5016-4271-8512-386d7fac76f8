import produce from "immer";
import { isEmpty, isObject, uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { ChatType } from "~utils/types/Chat";
import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";

import { OfferType, ProjectType, RequestType } from "~utils/types/Project";
import { UserType } from "~utils/types/User";
import { getStore } from ".";

export interface ProjectsState {
  projectPrev: number;
  projects: ProjectType[];
  filteredProjects: ProjectType[];
  addProject(data: ProjectType): void;
  setProject(data: ProjectType): void;

  actionGetProjects(
    limit: number,
    page: number,
    search?: string
  ): Promise<void>;
  actionGetProjects(query: Partial<ProjectType>): Promise<void>;
  actionGetProjects(
    query: Partial<ProjectType>,
    page?: null,
    search?: null,
    keepOld?: boolean
  ): Promise<void>;
  actionUpdateMultipleProjects(
    list: Partial<ProjectType>[],
    changes?: Record<string, Record<string, any>>
  ): Promise<ProjectType[]>;
  actionResetProjects(): Promise<void>;
}

export const initProjectsStore = (initial: Partial<ProjectsState> = {}) => {
  return create<ProjectsState>()((set, get) => ({
    projects: [],
    filteredProjects: [],
    projectPrev: 0,

    addProject: (data) => {
      set(
        produce<ProjectsState>((state) => {
          state.projects.unshift(data);
        })
      );
    },

    setProject: (data) => {
      set(
        produce<ProjectsState>((state) => {
          let i = state.projects.findIndex((e) => e.id === data.id);

          state.projects[i] = data;
        })
      );
    },

    ...initial,

    async actionGetProjects(limit, page?, search?, keepOld = false) {
      console.debug("get projects", limit, page);

      const baseData = {
        query: isObject(limit) ? limit : {},
        offers: {
          search: `%${search}%`,
        },
        page: isObject(limit) ? 0 : (page as number),
        limit: isObject(limit) ? 100 : limit,
      };

      const { error, data } = await sdk.make<{
        data: ProjectType[];
        relations: {
          users: UserType[];
          customers: CustomerType[];
          departments: DepartmentType[];
          offers: OfferType[];
          requests: RequestType[];
          chat: ChatType[];
        };
      }>({
        method: "POST",
        url: "/crm/projects/paginate",
        data: {
          ...baseData,
          options: {
            relations: {
              users: [
                "projects.allowedUsers",
                "projects.restrictedUsers",
                "projects.requests.approver",
                "customers.persons",
              ],
              departments: ["projects.departments"],
              customers: ["projects.requests.customer"],
              requests: ["projects.request:requests"],
              offers: ["projects.offers"],
              chat: ["projects.offers.chat"],
            },
          },
        },
      });

      await getStore("analytics")
        .getState()
        .actionGetReport({
          target: "projects",
          ...baseData,
        });

      if (data) {
        set({
          filteredProjects: uniqBy(
            [
              ...data.data.filter((x) => !isEmpty(x)),
              ...(keepOld ? get().filteredProjects : []),
            ],
            "id"
          ),
          projects: uniqBy(
            [...data.data.filter((x) => !isEmpty(x)), ...get().projects],
            "id"
          ),
        });

        const usersStore = getStore("users");
        const customersStore = getStore("customers");
        const deparmentsStore = getStore("departments");
        const requestsStore = getStore("requests");
        const offersStore = getStore("offers");
        const chatStore = getStore("chat");

        usersStore.setState({
          users: uniqBy(
            [
              ...data.relations.users.filter((x) => !isEmpty(x)),
              ...usersStore.getState().users,
            ],
            "id"
          ),
        });

        customersStore.setState({
          customers: uniqBy(
            [
              ...data.relations.customers.filter((x) => !isEmpty(x)),
              ...customersStore.getState().customers,
            ],
            "id"
          ),
        });

        deparmentsStore.setState({
          departments: uniqBy(
            [
              ...data.relations.departments.filter((x) => !isEmpty(x)),
              ...deparmentsStore.getState().departments,
            ],
            "id"
          ),
        });

        requestsStore.setState({
          requests: uniqBy(
            [
              ...data.relations.requests.filter((x) => !isEmpty(x)),
              ...requestsStore.getState().requests,
            ],
            "id"
          ),
        });

        offersStore.setState({
          offers: uniqBy(
            [
              ...data.relations.offers.filter((x) => !isEmpty(x)),
              ...offersStore.getState().offers,
            ],
            "id"
          ),
        });

        if (data.relations.chat) {
          console.debug("initializing chats");

          await chatStore
            .getState()
            .importChats(data.relations.chat.filter((x) => !isEmpty(x)));
        }
      }
    },

    async actionResetProjects() {
      set({
        projects: [],
      });
    },

    async actionUpdateMultipleProjects(data, changes = {}) {
      console.debug("update multiple projects", data, changes);

      let response = await sdk.make({
        method: "POST",
        url: "/crm/projects/update",
        data: {
          data,
          changes,
          order: ["offers", "$", "chats"],
        },
      });

      set({
        projectPrev: get().projectPrev + 1,
      });

      return response.data! as ProjectType[];
    },
  }));
};
