import produce from "immer";
import { isEmpty, uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";
import { RequestType } from "~utils/types/Project";
import { UserType } from "~utils/types/User";
import { getStore } from ".";

export interface RequestsState {
  requests: RequestType[];
  filteredRequests: RequestType[];
  addRequest(data: RequestType): void;

  actionGetRequests(
    limit: number,
    page: number,
    search?: string
  ): Promise<void>;
  actionUpdateMultipleRequests(
    list: Partial<RequestType>[],
    changes?: Record<string, Record<string, any>>
  ): Promise<RequestType[]>;
}

export const initRequestsStore = (initial: Partial<RequestsState> = {}) => {
  return create<RequestsState>()((set, get) => ({
    requests: [],
    filteredRequests: [],

    addRequest: (data) => {
      set(
        produce<RequestsState>((state) => {
          state.requests.push(data);
        })
      );
    },

    ...initial,

    async actionGetRequests(limit, page, search) {
      console.debug("get requests", limit, page, search);

      const baseData = {
        query: !!search ? { search: `%${search}%` } : {},
        page,
        limit,
      };

      const { error, data } = await sdk.make<{
        data: RequestType[];
        relations: {
          users: UserType[];
          customers: CustomerType[];
          departments: DepartmentType[];
          files: string[];
        };
      }>({
        method: "POST",
        url: "/crm/requests/paginate",
        data: {
          ...baseData,
          options: {
            relations: {
              users: ["requests.approver", "customers.persons"],
              customers: ["requests.customer"],
              files: ["customers.persons.files"],
            },
          },
        },
      });

      await getStore("analytics")
        .getState()
        .actionGetReport({
          target: "requests",
          ...baseData,
        });

      if (data) {
        set({
          filteredRequests: uniqBy(
            data.data.filter((x) => !isEmpty(x)),
            "id"
          ),
          requests: uniqBy(
            [...data.data.filter((x) => !isEmpty(x)), ...get().requests],
            "id"
          ),
        });

        const usersStore = getStore("users");
        const filesStore = getStore("files");
        const customersStore = getStore("customers");

        usersStore.setState({
          users: uniqBy(
            [
              ...data.relations.users.filter((x) => !isEmpty(x)),
              ...usersStore.getState().users,
            ],
            "id"
          ),
        });

        customersStore.setState({
          customers: uniqBy(
            [
              ...data.relations.customers.filter((x) => !isEmpty(x)),
              ...customersStore.getState().customers,
            ],
            "id"
          ),
        });

        await filesStore.getState().actionPaginateFiles(
          data.relations.files
            .filter((x) => !isEmpty(x))
            .filter((file: any) => file.type === "avatar")
            .map((file: any) => file.id),
          "avatar"
        );
      }
    },

    async actionUpdateMultipleRequests(dataList, changes = {}) {
      console.debug("update multiple requests", dataList, changes);

      let response = await sdk.make<RequestType[]>({
        method: "POST",
        url: "/crm/requests/update",
        data: {
          data: dataList,
          changes,
          order: ["offers", "projects", "$", "chats"],
        },
      });

      return response.data!;
    },
  }));
};
