import produce from "immer";
import {
  cloneDeep,
  every,
  isArray,
  isEmpty,
  isNumber,
  isObject,
  map,
  omit,
  reduce,
  uniqBy,
} from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { ChatType } from "~utils/types/Chat";

import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";
import { OfferType, ProjectType, RequestType } from "~utils/types/Project";
import { RoleType } from "~utils/types/Roles";
import { TaskPriorityType, TaskStatusTypes, TaskType } from "~utils/types/Task";
import { UserType } from "~utils/types/User";
import { getStore } from ".";

export interface TaskActionOptions {
  id: number;
  start: Date;
  end: Date;
  projectId?: number;
  userId?: number;
}

export interface TasksState {
  taskPrev: number;
  tasks: TaskType[];
  filteredTasks: TaskType[];
  updateTask(task: TaskType): void;
  taskStatusList: TaskStatusTypes[];
  priorityColors: Record<TaskPriorityType, string>;
  statusColors: Record<string, string>;
  addTask(task: TaskType): void;

  computed: {
    readonly groupByStatus: [[string, string], TaskType[]][];
  };

  actionGetTasks(
    options: Partial<TaskActionOptions>,
    search?: string
  ): Promise<void>;
  actionUpdateMultipleTask(
    list: Partial<TaskType>[],
    changes?: Record<string, any>
  ): Promise<TaskType[]>;
  actionResetTasks(): Promise<void>;
}

export const initTasksStore = (initial: Partial<TasksState> = {}) => {
  return create<TasksState>()((set, get) => ({
    // tasks: seedTaskData(),
    tasks: [],
    taskPrev: 0,
    filteredTasks: [],

    updateTask: (task: TaskType) => {
      set(
        produce<TasksState>((state) => {
          let index: number = 0;

          get().tasks.map((e, i) => {
            if (e.id === task.id) {
              index = i;
            }
          });

          state.tasks[index!] = task;
        })
      );
    },
    addTask: (task: TaskType) => {
      set(
        produce<TasksState>((state) => {
          const additiveTask = { ...task };
          additiveTask.id = get().tasks.length + 1;
          state.tasks = [...get().tasks, additiveTask];
        })
      );
    },

    taskStatusList: ["To Do", "Active", "Completed", "Problems", "Archived"],
    statusColors: {
      "To Do": "orange",
      Active: "blue",
      Completed: "green",
      Problems: "red",
      Archived: "gray",
    },
    priorityColors: {
      "": "gray",
      "Very Low": "cyan",
      Low: "blue",
      Medium: "yellow",
      High: "orange",
      "Very High": "red",
    },

    ...initial,

    computed: {
      get groupByStatus() {
        const result = new Map();
        const sym = Symbol("-").toString();

        const colors = get().statusColors;

        get().taskStatusList.forEach((taskStatusName) => {
          result.set(`${taskStatusName}${sym}${colors[taskStatusName]}`, []);
        });

        get().filteredTasks.forEach((task) => {
          const status = task.status || "Archived";
          const groupName = `${status}${sym}${colors[status]}`;

          let newData = result.get(groupName);

          newData.push(task);

          result.set(groupName, newData);
        });

        return [...result.entries()].map(([columData, tasks]) => [
          columData.split(sym),
          tasks,
        ]) as any;
      },
    },

    async actionGetTasks(query, search) {
      console.debug("get tasks", query);

      const departmentsStore = getStore("departments");
      const usersStore = getStore("users");
      const projectsStore = getStore("projects");
      const requestsStore = getStore("requests");
      const offersStore = getStore("offers");
      const chatStore = getStore("chat");
      const rolesStore = getStore("roles");

      const usersState = usersStore.getState();

      const activeUser = usersState.users.find((x) => x.id === query.userId);

      let defaultRole: RoleType | undefined;

      if (activeUser) {
        defaultRole = rolesStore
          .getState()
          .roles.find((x) => x.id === activeUser.defaultRoleId[0]);

        if (!query.projectId) {
          const projectState = projectsStore.getState();

          await Promise.all([
            projectState.actionGetProjects({
              departmentIds: activeUser.departmentIds,
            }),
            projectState.actionGetProjects({
              allowedUserIds: [activeUser.id],
            }),
          ]);

          return;
        }
      }

      const { error, data } = await sdk.make<{
        data: TaskType[];
        relations: {
          roles: RoleType[];
          departments: DepartmentType[];
          files: string[];
          customers: CustomerType[];
          users: UserType[];
          offers: OfferType[];
          projects: ProjectType[];
          requests: RequestType[];
          chat: ChatType[];
        };
      }>({
        method: "POST",
        url: "/crm/tasks/paginate",
        data: {
          query,
          options: {
            relations: {
              users: [
                "tasks.assignees",
                "tasks.contributors",
                "projects.allowedUsers",
                "projects.restrictedUsers",
              ],
              projects: ["tasks.project"],
              departments: ["projects.departments"],
              requests: ["projects.request"],
              offers: ["projects.offers"],
              chat: ["tasks.chat"],
            },
          },
        },
      });

      if (data && data.data) {
        set({
          filteredTasks: uniqBy(
            data.data.filter((x) => {
              if (x.private) {
                if (
                  activeUser &&
                  (x.assigneeIds.includes(query.userId!) ||
                    (defaultRole && defaultRole.weight! < 0))
                ) {
                  return true;
                } else {
                  return false;
                }
              } else {
                return true;
              }
            }),
            "id"
          ),
          tasks: uniqBy([...data.data, ...get().tasks], "id"),
        });

        // const filesStore = getStore("files");

        offersStore.setState({
          offers: uniqBy(
            [
              ...data.relations.offers.filter((x) => !isEmpty(x)),
              ...offersStore.getState().offers,
            ],
            "id"
          ),
        });

        requestsStore.setState({
          requests: uniqBy(
            [
              ...data.relations.requests.filter((x) => !isEmpty(x)),
              ...requestsStore.getState().requests,
            ],
            "id"
          ),
        });

        departmentsStore.setState({
          departments: uniqBy(
            [
              ...data.relations.departments.filter((x) => !isEmpty(x)),
              ...departmentsStore.getState().departments,
            ],
            "id"
          ),
        });

        usersStore.setState({
          users: uniqBy(
            [
              ...data.relations.users.filter((x) => !isEmpty(x)),
              ...usersStore.getState().users,
            ],
            "id"
          ),
        });

        projectsStore.setState({
          projects: uniqBy(
            [
              ...data.relations.projects.filter((x) => !isEmpty(x)),
              ...projectsStore.getState().projects,
            ],
            "id"
          ),
        });

        chatStore.setState({
          chats: uniqBy(
            [
              ...data.relations.chat.filter((x) => !isEmpty(x)),
              ...chatStore.getState().chats,
            ],
            "id"
          ),
        });

        // await filesStore.getState().actionPaginateFiles(
        //   data.relations.files
        //     .filter((x) => !isEmpty(x))
        //     .filter((file: any) => file.type === "avatar")
        //     .map((file: any) => file.id),
        //   "avatar"
        // );
      }
    },

    async actionResetTasks() {
      set({
        tasks: [],
      });
    },

    async actionUpdateMultipleTask(
      data: TaskType[],
      changes: Record<string, any> = {}
    ) {
      console.debug("update multiple tasks", data, changes);

      let response = await sdk.make<{
        data: TaskType[];
      }>({
        method: "POST",
        url: "/crm/tasks/update",
        data: {
          data: map(data, (item) => {
            const omits: string[] = [];

            return omit(item, omits);
          }),
          changes,
          order: ["$", "chats", "messages"],
        },
      });

      if (response.data) {
        set({
          tasks: uniqBy([...get().tasks, ...response.data?.data], "id"),
          taskPrev: get().taskPrev + 1,
        });
      }

      return response.data?.data as TaskType[];
    },
  }));
};
