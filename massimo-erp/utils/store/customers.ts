import produce from "immer";
import {
  cloneDeep,
  every,
  has,
  isArray,
  isEmpty,
  isNumber,
  isObject,
  map,
  omit,
  reduce,
  uniqBy,
} from "lodash";
import create from "zustand";
import { DeepPartial } from "~types";
import { sdk } from "~utils/sdk";
import { ChatType } from "~utils/types/Chat";

import { CustomerType } from "~utils/types/Customer";
import { RoleType } from "~utils/types/Roles";
import { UserType } from "~utils/types/User";
import { getStore } from ".";
import { TempState } from "./temp";

export interface CustomersState {
  customers: CustomerType[];
  filteredCustomers: CustomerType[];
  addCustomer(customers: CustomerType): void;
  setCustomer(customer: CustomerType): void;

  invites: CustomerType[];
  setInvites(data: CustomerType): void;
  actionGetCustomers(
    limit: number,
    page: number,
    search?: string
  ): Promise<void>;
  actionUpdateCustomer(
    options: DeepPartial<Omit<TempState["customerModal"], "isOpen" | "type">>,
    order?: string[]
  ): Promise<void>;
  actionResetCustomers(): Promise<void>;
}

export const initCustomersStore = (initial: Partial<CustomersState> = {}) => {
  return create<CustomersState>()((set, get) => ({
    customers: [],
    filteredCustomers: [],

    addCustomer: (customers) => {
      set(
        produce<CustomersState>((state) => {
          state.customers.push(customers);
        })
      );
    },

    setCustomer: (customer) => {
      set(
        produce<CustomersState>((state) => {
          let activeIndex = get().customers.findIndex(
            (e) => e.id === customer.id
          );
          state.customers[activeIndex] = customer;
        })
      );
    },

    invites: [],

    ...initial,

    setInvites: (data) => {},

    async actionGetCustomers(limit, page, search) {
      console.debug("get customers", limit, page);
      const baseData = {
        query: !!search
          ? {
              search: `%${search}%`,
            }
          : {},
        page,
        limit,
      };

      const { error, data } = await sdk.make<{
        data: CustomerType[];
        relations: {
          users: UserType[];
          files: string[];
          chats: ChatType[];
          roles: RoleType[];
        };
      }>({
        method: "POST",
        url: "/customers/paginate",
        data: {
          ...baseData,
          options: {
            relations: {
              users: ["customers.persons"],
              files: ["persons.files"],
              chats: ["customers.chat"],
              roles: ["customers.persons.defaultRole"],
            },
          },
        },
      });

      await getStore("analytics")
        .getState()
        .actionGetReport({
          target: "customers",
          ...baseData,
        });

      if (data) {
        set({
          filteredCustomers: uniqBy(
            data.data.filter((x) => !isEmpty(x)),
            "id"
          ),
          customers: uniqBy(
            [...data.data.filter((x) => !isEmpty(x)), ...get().customers],
            "id"
          ),
        });

        const filesStore = getStore("files");
        const usersStore = getStore("users");
        const chatStore = getStore("chat");
        const rolesStore = getStore("roles");

        usersStore.setState({
          users: uniqBy(
            [
              ...data.relations.users.filter((x) => !isEmpty(x)),
              ...usersStore.getState().users,
            ],
            "id"
          ),
        });

        chatStore.setState({
          chats: uniqBy(
            [
              ...data.relations.chats.filter((x) => !isEmpty(x)),
              ...chatStore.getState().chats,
            ],
            "id"
          ),
        });

        rolesStore.setState({
          roles: uniqBy(
            [
              ...data.relations.roles.filter((x) => !isEmpty(x)),
              ...rolesStore.getState().roles,
            ],
            "id"
          ),
        });

        await filesStore.getState().actionPaginateFiles(
          data.relations.files
            .filter((x) => !isEmpty(x))
            .filter((file: any) => file.type === "avatar")
            .map((file: any) => file.id),
          "avatar"
        );
      }
    },

    async actionResetCustomers() {
      set({
        filteredCustomers: [],
      });
    },

    async actionUpdateCustomer(options, order = ["$", "chats"]) {
      console.debug("update multiple customers", options);

      const { data: rawData, changes: rawChanges } = options;
      const data = cloneDeep(rawData);

      const changes = reduce(
        rawChanges,
        (acc: Record<string, any[]>, value, key) => {
          if (isArray(value)) {
            if (every(value, (v) => isNumber(v))) {
              acc[key] = value.filter((x) => x > -1);
            } else if (every(value, (v) => isObject(v))) {
              acc[key] = value.map((x) => {
                const res = cloneDeep(x);

                if (!res.name && res.label) {
                  res.name = res.label.toLowerCase().split(" ").join("-");
                }

                if (res.id < 0) {
                  return omit(res, ["id"]);
                }

                return res;
              });
            } else {
              acc[key] = value;
            }
          } else {
            acc[key] = value;
          }

          return acc;
        },
        {}
      );

      await sdk.make({
        method: "POST",
        url: "/customers/update",
        data: {
          data: [data],
          changes,
          order,
        },
      });
    },
  }));
};
