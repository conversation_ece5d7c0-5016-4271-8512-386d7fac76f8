import produce from "immer";
import {
  cloneDeep,
  has,
  isArray,
  isEmpty,
  map,
  omit,
  uniq,
  uniqBy,
} from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { ChatType } from "~utils/types/Chat";
import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";
import { NotificationType } from "~utils/types/Notification";
import { OfferType, ProjectType, RequestType } from "~utils/types/Project";
import { RoleType } from "~utils/types/Roles";
import { ConnectionType, UserType } from "~utils/types/User";
import { getStore } from ".";

export interface UsersState {
  userPrev: number;
  plans: string[];
  status: string[];

  connections: ConnectionType[];
  connectionRequests: ConnectionType[];
  lastUserId: number;

  roleColors: {
    [key: string]: string;
  };
  planColors: {
    [key: string]: string;
  };
  statusColors: {
    [key: string]: string;
  };

  filter: {
    roles: string[];
    plans: string[];
    status: string[];
  };
  setFilter(key: "roles" | "plans" | "status", value: string[]): void;

  users: UserType[];
  filteredUsers: UserType[];
  filteredUnverifiedUsers: UserType[];
  addUser(user: UserType): void;
  setUser(user: UserType): void;
  removeUser(id: number): void;

  actionGetUsers(
    limit: number,
    page: number,
    search?: string,
    unverified?: boolean
  ): Promise<void>;
  actionGetUsers(dataList: number[], search?: string): Promise<void>;
  actionUpdateMultipleUser(
    list: Partial<UserType>[],
    ignorePrev?: boolean
  ): Promise<UserType[]>;
  actionGetConnections(id: number, prev?: boolean): Promise<void>;
  actionGetConnectionRequests(): Promise<void>;
  actionRejectUser(userId: number): Promise<void>;
  actionConnectUser(userId: number): Promise<void>;
  actionResetUsers(): Promise<void>;
}

export const initUsersStore = (initial: Partial<UsersState> = {}) => {
  return create<UsersState>()((set, get) => ({
    userPrev: 0,

    plans: ["basic", "company", "enterprise", "team"],
    status: ["offline", "pending", "active"],

    roleColors: {
      "super-admin": "red",
      admin: "red",
      empty: "gray",
      staff: "yellow",
    },
    planColors: {
      basic: "blue",
      company: "yellow",
      enterprise: "green",
      team: "orange",
    },
    statusColors: {
      offline: "gray",
      pending: "orange",
      active: "green",
    },

    filter: {
      roles: ["admin", "editor", "author", "maintainer", "subscriber"],
      plans: ["basic", "company", "enterprise", "team"],
      status: ["offline", "pending", "active"],
    },
    setFilter: (key, value) => {
      set(
        produce<UsersState>((state) => {
          state.filter[key] = value;
        })
      );
    },

    users: [],
    filteredUsers: [],

    filteredUnverifiedUsers: [],

    connections: [],
    connectionRequests: [],

    lastUserId: 0,

    addUser: (user) => {
      set(
        produce<UsersState>((state) => {
          state.users.push(user);
        })
      );
    },

    setUser: (user) => {
      set(
        produce<UsersState>((state) => {
          let i = state.users.findIndex((e) => e.id === user.id);
          state.users[i] = user;
        })
      );
    },

    removeUser: (id) => {
      set(
        produce<UsersState>((state) => {
          state.users = state.users.filter((e) => e.id !== id);
        })
      );
    },

    ...initial,

    async actionGetUsers(limit, page, search?, unverified = false) {
      console.debug("get users", limit, page, search);

      const activeUserId = getStore("global").getState().activeUserId;
      const relations: any = {
        roles: [
          "users.defaultRole",
          "users.roles",
          "users.departments.roles:roles2",
        ],
        users: [],
        departments: ["users.departments"],
        files: ["users.files"],
        customers: ["users.organizations"],
      };

      if (activeUserId && isArray(limit) && limit.includes(activeUserId)) {
        relations.chats = [
          "users.chats",
          "users.organizations.chat",
          "users.ownedChats",
        ];
        relations.projects = [
          "users.departments.projects",
          "users.allowedProjects",
        ];
        relations.offers = [
          "users.departments.projects.offers",
          "users.allowedProjects.offers:offers2",
        ];
        relations.requests = [
          "users.departments.projects.request",
          "users.allowedProjects.request:request2",
        ];
      }

      const baseData = {
        query: isArray(limit)
          ? {
              id: limit,
            }
          : !!search
          ? {
              search: `%${search}%`,
              ...(unverified
                ? [
                    {
                      isValid: false,
                    },
                  ]
                : []),
            }
          : unverified
          ? {
              isValid: false,
            }
          : undefined,
        page: isArray(limit) ? 0 : page,
        limit: isArray(limit) ? limit.length : limit,
      };

      const { error, data } = await sdk.make<{
        data: UserType[];
        relations: {
          roles: RoleType[];
          departments: DepartmentType[];
          files: string[];
          customers: CustomerType[];
          chats: ChatType[];
          users: UserType[];
          projects: ProjectType[];
          offers: OfferType[];
          requests: RequestType[];
        };
      }>({
        method: "POST",
        url: "/users/paginate",
        data: {
          ...baseData,
          options: {
            relations,
          },
        },
      });

      if (unverified) {
        set({
          filteredUnverifiedUsers: data?.data,
        });

        return;
      }

      await getStore("analytics")
        .getState()
        .actionGetReport({
          target: "users",
          ...baseData,
        });

      if (activeUserId && isArray(limit) && limit.includes(activeUserId)) {
        await getStore("roles").getState().actionGetRoles();
        await getStore("projects").getState().actionGetProjects(1000, 0);
      }

      if (!isArray(limit)) {
        set({ filteredUsers: [] });
      }

      if (data) {
        set({
          filteredUsers: uniqBy(data.data, "id"),
          users: uniqBy(
            [
              ...data.data,
              ...(data.relations.users ? data.relations.users : []),
              ...get().users,
            ],
            "id"
          ),
        });

        const rolesStore = getStore("roles");
        const departmentsStore = getStore("departments");
        const filesStore = getStore("files");
        const customersStore = getStore("customers");
        const projectsStore = getStore("projects");
        const offersStore = getStore("offers");
        const requestsStore = getStore("requests");
        const chatStore = getStore("chat");

        if (data.relations.customers)
          customersStore.setState({
            customers: uniqBy(
              [
                ...data.relations.customers.filter((x) => !isEmpty(x)),
                ...customersStore.getState().customers,
              ],
              "id"
            ),
          });

        if (data.relations.projects)
          projectsStore.setState({
            projects: uniqBy(
              [
                ...data.relations.projects.filter((x) => !isEmpty(x)),
                ...projectsStore.getState().projects,
              ],
              "id"
            ),
          });

        if (data.relations.requests)
          requestsStore.setState({
            requests: uniqBy(
              [
                ...data.relations.requests.filter((x) => !isEmpty(x)),
                ...requestsStore.getState().requests,
              ],
              "id"
            ),
          });

        if (data.relations.offers)
          offersStore.setState({
            offers: uniqBy(
              [
                ...data.relations.offers.filter((x) => !isEmpty(x)),
                ...offersStore.getState().offers,
              ],
              "id"
            ),
          });

        if (data.relations.roles)
          rolesStore.setState({
            roles: uniqBy(
              [
                ...data.relations.roles.filter((x) => !isEmpty(x)),
                ...rolesStore.getState().roles,
              ],
              "id"
            ),
          });

        if (data.relations.departments)
          departmentsStore.setState({
            departments: uniqBy(
              [
                ...data.relations.departments.filter((x) => !isEmpty(x)),
                ...departmentsStore.getState().departments,
              ],
              "id"
            ),
          });

        if (data.relations.files)
          await filesStore.getState().actionPaginateFiles(
            data.relations.files
              .filter((x) => !isEmpty(x))
              .filter((file: any) => file.type === "avatar")
              .map((file: any) => file.id),
            "avatar"
          );

        if (data.relations.chats) {
          console.debug("initializing chats");

          await chatStore
            .getState()
            .importChats(data.relations.chats.filter((x) => !isEmpty(x)));
        }
      }

      return;
    },

    async actionUpdateMultipleUser(data: UserType[], ignorePrev) {
      console.debug("update multiple users", data);

      let response = await sdk.make({
        method: "POST",
        url: "/users/update",
        data: {
          data: map(data, (item) => {
            const omits = [];

            if (!item.password) {
              omits.push("password");
            }

            if (!item.phone) {
              omits.push("phone");
            }

            return omit(item, omits);
          }),

          order: ["$"],
        },
      });

      if (!ignorePrev) {
        set({
          userPrev: get().userPrev + 1,
        });
      }

      return response.data! as UserType[];
    },

    async actionResetUsers() {
      set({
        filteredUsers: [],
      });
    },

    async actionGetConnections(id, prev = false) {
      const { data, error } = await sdk.make<{
        data: ConnectionType[];
        relations: {
          users: UserType[];
        };
      }>({
        method: "POST",
        url: `/connections/connected/${prev ? get().lastUserId : id}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        const globalState = getStore("global").getState();

        set({
          users: uniqBy([...get().users, ...data.relations.users], "id"),
          connections: data.data,
        });

        if (id !== 0) {
          set({
            lastUserId: id,
          });
        }
      }
    },

    async actionGetConnectionRequests() {
      const { data, error } = await sdk.make<{
        data: ConnectionType[];
        relations: {
          users: UserType[];
        };
      }>({
        method: "POST",
        url: `/connections/disconnected`,
      });

      if (error) {
        throw error;
      }

      const { activeUserId } = getStore("global").getState();

      if (data) {
        set({
          users: uniqBy([...get().users, ...data.relations.users], "id"),
          connectionRequests: data.data.filter((e) =>
            [e.target1Id, e.target2Id].includes(activeUserId as number)
          ),
        });
      }
    },

    async actionRejectUser(userId) {
      await sdk.make({
        method: "POST",
        url: `/connections/reject/${userId}`,
      });
    },

    async actionConnectUser(userId) {
      await sdk.make({
        method: "POST",
        url: `/connections/connect/${userId}`,
      });
    },
  }));
};
