import produce from "immer";
import {
  cloneDeep,
  every,
  isArray,
  isNumber,
  isObject,
  omit,
  reduce,
  uniqBy,
} from "lodash";
import create from "zustand";
import { DeepPartial } from "~types";
import { sdk } from "~utils/sdk";
import { DepartmentType } from "~utils/types/Department";
import { RoleType } from "~utils/types/Roles";
import { UserType } from "~utils/types/User";
import { getStore } from ".";
import { TempState } from "./temp";

export interface DepartmentsState {
  departments: DepartmentType[];
  filteredDepartments: DepartmentType[];
  setDepartment(data: any): void;
  addDepartment(data: any): void;

  actionGetDepartments(
    limit: number,
    page: number,
    search?: string
  ): Promise<void>;
  actionResetDepartments(): void;
  actionUpdateDepartment(
    options: DeepPartial<Omit<TempState["departmentModal"], "isOpen" | "type">>,
    order?: string[]
  ): Promise<void>;
}

export const initDepartmentsStore = (
  initial: Partial<DepartmentsState> = {}
) => {
  return create<DepartmentsState>()((set, get) => ({
    departments: [],
    filteredDepartments: [],

    ...initial,

    setDepartment: (data) => {
      set(
        produce<DepartmentsState>((state) => {
          let i = state.departments.findIndex((e) => e.id === data.id);
          state.departments[i] = data;
        })
      );
    },
    addDepartment: (data) => {
      set(
        produce<DepartmentsState>((state) => {
          state.departments.push(data);
        })
      );
    },

    async actionGetDepartments(limit, page, search?) {
      const baseData = {
        query: !!search
          ? {
              search: `%${search}%`,
            }
          : undefined,
        page: page,
        limit: limit,
      };

      const { error, data } = await sdk.make<{
        data: DepartmentType[];
        relations: {
          roles: RoleType[];
          users: UserType[];
        };
      }>({
        method: "POST",
        url: "/departments/paginate",
        data: {
          ...baseData,
          options: {
            relations: {
              users: ["departments.users"],
              roles: ["departments.roles"],
            },
          },
        },
      });

      await getStore("analytics")
        .getState()
        .actionGetReport({
          target: "departments",
          ...baseData,
        });

      if (data) {
        set({
          filteredDepartments: data.data,
        });

        const usersStore = getStore("users");
        const rolesStore = getStore("roles");

        const rolesState = rolesStore.getState();
        const usersState = usersStore.getState();

        usersStore.setState({
          users: uniqBy([...data.relations.users, ...usersState.users], "id"),
        });

        rolesStore.setState({
          roles: uniqBy([...data.relations.roles, ...rolesState.roles], "id"),
        });
      }
    },

    async actionResetDepartments() {
      set({
        departments: [],
      });
    },

    async actionUpdateDepartment(options, order = ["$", "roles", "users"]) {
      const { data: rawData, changes: rawChanges } = options;
      const data = cloneDeep(rawData);

      if (data && !data.name && data.label) {
        data.name = data.label.toLowerCase().split(" ").join("-");
      }

      const changes = reduce(
        rawChanges,
        (acc: Record<string, any[]>, value, key) => {
          if (isArray(value)) {
            if (every(value, (v) => isNumber(v))) {
              acc[key] = value.filter((x) => x > -1);
            } else if (every(value, (v) => isObject(v))) {
              acc[key] = value.map((x) => {
                const res = cloneDeep(x);

                if (!res.name && res.label) {
                  res.name = res.label.toLowerCase().split(" ").join("-");
                }

                if (res.id < 0) {
                  return omit(res, ["id"]);
                }

                return res;
              });
            } else {
              acc[key] = value;
            }
          } else {
            acc[key] = value;
          }

          return acc;
        },
        {}
      );

      await sdk.make({
        method: "POST",
        url: "/departments/update",
        data: {
          data: [data],
          changes,
          order,
        },
      });

      // if (!response.data) {
      //   throw new Error("Cannot Update");
      // }
    },
  }));
};
