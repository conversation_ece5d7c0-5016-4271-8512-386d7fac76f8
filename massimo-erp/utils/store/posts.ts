import produce from "immer";
import { cloneDeep, isNumber, orderBy, uniq, uniqBy } from "lodash";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { CommentType, PostType } from "~utils/types/Post";
import { getStore } from ".";

export interface PostsState {
  stories: PostType[];
  posts: PostType[];
  comments: CommentType[];

  likes: number[];
  views: number[];

  setPost: (post: Partial<PostType>) => void;

  actionGetFeed: (page: number, onlyStory?: boolean) => Promise<boolean>;
  actionGetStories: (userId: number) => Promise<void>;
  actionGetPosts: (
    userId: number,
    page: number,
    isOrganization?: boolean
  ) => Promise<boolean>;
  actionUpdatePosts: (
    rawData: {
      id?: number;
      type?: "story" | "post";
      description?: string;
      files?: number[];
      user?: number;
    }[],
    isDelete?: boolean,
    addStore?: boolean
  ) => Promise<void>;
  actionUpdatePostMeta: (
    rawData: {
      isLike?: boolean;
      isView?: boolean;
      user?: number;
      post?: number;
    }[],
    isDelete?: boolean
  ) => Promise<void>;
  actionGetComments: (postId: number, page: number) => Promise<boolean>;
  actionUpdateComments: (
    rawData: {
      id?: number;
      user?: number;
      post?: number;
      description?: string;
    }[],
    isDelete?: boolean
  ) => Promise<void>;
  actionGetLikes: (postId: number, page: number) => Promise<boolean>;
  actionGetViews: (storyId: number, page: number) => Promise<boolean>;
  resetPosts(): Promise<void>;
  resetStories(): Promise<void>;
  resetComments(): Promise<void>;
  resetLikes(): Promise<void>;
}

export const initPostsStore = (initial: Partial<PostsState> = {}) => {
  return create<PostsState>()((set, get) => ({
    stories: [],
    posts: [],
    comments: [],

    likes: [],
    views: [],

    setPost: (post) => {
      set(
        produce<PostsState>((state) => {
          let index = get().posts.findIndex((e) => e.id === post.id);

          if (index === -1) {
            index = get().stories.findIndex((e) => e.id === post.id);

            state.stories[index] = {
              ...get().stories[index],
              ...post,
            } as PostType;
          } else {
            state.posts[index] = { ...get().posts[index], ...post } as PostType;
          }
        })
      );
    },

    ...initial,

    async actionGetFeed(page, onlyStory = false) {
      const { data, error } = await sdk.make<{
        data: [PostType[], PostType[]];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/feed/${page}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        if (page === 0) {
          let fileIds: number[] = [];

          await data.data[1].map((e) => {
            e.fileIds.map((id) => {
              fileIds.push(id);
            });
          });

          const filesStore = getStore("files");

          filesStore.getState().actionPaginateFiles(uniq(fileIds));

          set({
            stories: data.data[1],
          });
        }

        set({
          posts: uniqBy([...get().posts, ...data.data[0]], "id"),
        });
      }

      const dataLength = data?.data[0].length || 0;

      if (page === 0) {
        return dataLength === 20;
      }

      return dataLength > 0;
    },

    async actionGetStories(userId) {
      const { data, error } = await sdk.make<{
        data: [PostType[], PostType[]];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/profile/${userId}/0`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          stories: uniqBy(
            [
              ...data.data[1],
              ...get().stories.filter((e) => e.userId === userId),
            ],
            "id"
          ),
        });

        let fileIds: number[] = [];

        await data.data[1].map((e) => {
          e.fileIds.map((id) => {
            fileIds.push(id);
          });
        });

        const filesStore = getStore("files");

        filesStore.getState().actionPaginateFiles(uniq(fileIds));
      }
    },

    async actionGetPosts(userId, page, isOrganization = false) {
      const { data, error } = await sdk.make<{
        data: PostType[] | [PostType[], PostType[]];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/${
          isOrganization ? "organization" : "profile"
        }/${userId}/${page}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          posts: uniqBy(
            [
              ...get().posts,
              ...(isOrganization
                ? (data.data as PostType[])
                : (data.data[0] as PostType[])),
            ],
            "id"
          ),
        });
      }

      const dataLength =
        ((isOrganization ? data?.data : data?.data[0]) as PostType[]).length ||
        0;

      if (page === 0) {
        return dataLength === 20;
      }

      return dataLength > 0;
    },

    async actionUpdatePosts(rawData, isDelete = false, addStore = true) {
      const { data, error } = await sdk.make<{
        data: Partial<PostType>[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/update`,
        data: {
          data: rawData,
          isDelete,
        },
      });

      if (error) {
        throw error;
      }

      if (isDelete) {
        rawData.map((d) => {
          if (d.type === "story") {
            set({
              stories: get().stories.filter(
                (e) => !rawData.some((x) => x.id === e.id)
              ),
            });
          } else {
            set({
              posts: get().posts.filter(
                (e) => !rawData.some((x) => x.id === e.id)
              ),
            });
          }
        });
      } else if (addStore) {
        data?.data.map((d, i) => {
          if (!isNumber(rawData[i].id)) {
            if (d.type === "story") {
              set({
                stories: uniqBy([d as PostType, ...get().stories], "id"),
              });
            } else {
              set({
                posts: uniqBy([d as PostType, ...get().posts], "id"),
              });
            }
          } else {
            get().setPost(d);
          }
        });
      }
    },

    async actionUpdatePostMeta(rawData, isDelete = false) {
      const { data, error } = await sdk.make<{
        data: {
          id?: number;
          postId?: number;
          isLike?: boolean;
          isView?: boolean;
          type?: "post" | "story";
        }[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/update-postmeta`,
        data: {
          data: rawData,
          isDelete,
        },
      });

      if (error) {
        throw error;
      }

      rawData?.map((d, i) => {
        let post = get().posts.find((e) => e.id === d.post);

        if (!post) {
          post = get().stories.find((e) => e.id === d.post);
        }

        get().setPost({
          id: post?.id,
          isViewed: !d.isView ? post?.isViewed : data?.data[i].id,
          viewCount: post?.viewCount! + (!d.isLike ? (!d.isView ? -1 : 1) : 0),
          isLiked: !d.isView
            ? !d.isLike || isDelete
              ? null
              : data?.data[i].id
            : post?.isLiked,
          likeCount: post?.likeCount! + (!d.isView ? (!d.isLike ? -1 : 1) : 0),
        });
      });
    },

    async actionGetComments(postId, page) {
      const { data, error } = await sdk.make<{
        data: CommentType[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/comments/${postId}/${page}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          comments: uniqBy([...get().comments, ...data.data], "id"),
        });
      }

      const dataLength = data?.data.length || 0;

      if (page === 0) {
        return dataLength === 20;
      }

      return dataLength > 0;
    },

    async actionUpdateComments(rawData, isDelete = false) {
      const { data, error } = await sdk.make<{
        data: CommentType[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/update-comments`,
        data: {
          data: rawData,
          isDelete,
        },
      });

      if (error) {
        throw error;
      }

      rawData.map((d, i) => {
        const post = get().posts.find((e) => e.id === d.post);

        get().setPost({
          id: d.post,
          commentCount: post?.commentCount! + (isDelete ? -1 : 1),
        });
      });

      if (data) {
        set({
          comments: isDelete
            ? get().comments.filter((e) => !rawData.some((x) => x.id === e.id))
            : uniqBy([...data.data, ...get().comments], "id"),
        });
      }
    },

    async actionGetLikes(postId, page) {
      const { data, error } = await sdk.make<{
        data: {
          id: number;
          userId: number;
        }[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/postmeta-like/${postId}/${page}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          likes: uniq([...data.data.map((e) => e.userId), ...get().likes]),
        });
      }

      const dataLength = data?.data.length || 0;

      if (page === 0) {
        return dataLength === 20;
      }

      return dataLength > 0;
    },

    async actionGetViews(storyId, page) {
      const { data, error } = await sdk.make<{
        data: {
          id: number;
          userId: number;
        }[];
        relations: {};
      }>({
        method: "POST",
        url: `/posts/postmeta-view/${storyId}/${page}`,
      });

      if (error) {
        throw error;
      }

      if (data) {
        set({
          views: uniq([...data.data.map((e) => e.userId), ...get().views]),
        });
      }

      const dataLength = data?.data.length || 0;

      if (page === 0) {
        return dataLength === 20;
      }

      return dataLength > 0;
    },

    async resetPosts() {
      set({
        posts: [],
      });
    },

    async resetStories() {
      set({
        stories: [],
      });
    },

    async resetComments() {
      set({
        comments: [],
      });
    },

    async resetLikes() {
      set({
        likes: [],
      });
    },
  }));
};
