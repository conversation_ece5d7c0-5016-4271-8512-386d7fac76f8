import produce from "immer";
import create from "zustand";
import { sdk } from "~utils/sdk";
import { DepartmentType } from "~utils/types/Department";
import { RoleType } from "~utils/types/Roles";

export interface RolesState {
  roles: RoleType[];
  setRole(role: RoleType): void;
  addRole(role: RoleType): void;

  actionGetRoles(): Promise<void>;
  actionUpdateRoles(roles: RoleType[]): Promise<number[]>;
  actionRemoveRoles(roles: number[]): Promise<number[]>;
}

export const initRolesStore = (initial: Partial<RolesState> = {}) => {
  return create<RolesState>()((set, get) => ({
    roles: [
      {
        id: 3,
        name: "staff",
        label: "Staff",
        isDefaultRole: true,
        isDepartmentRole: false,
        weight: 0,
      },
    ],

    setRole: (role) => {
      set(
        produce<RolesState>((state) => {
          const activeIndex = state.roles.findIndex((e) => e.id === role.id);
          state.roles[activeIndex] = role;
        })
      );
    },

    addRole: (role) => {
      set(
        produce<RolesState>((state) => {
          state.roles.push(role);
        })
      );
    },

    ...initial,

    async actionGetRoles() {
      // const { error, data } = await sdk.make<{
      //   data: RoleType[];
      // }>({
      //   method: "POST",
      //   url: "/roles/paginate",
      //   data: {
      //     query: {
      //       isDefaultRole: true,
      //     },
      //     limit: 1000,
      //     page: 0,
      //   },
      // });
      // console.log("get roles", data, error);
      // let response = await pull({
      //   method: "GET",
      //   url: "/roles",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      // });
      // set({
      //   roles: response.data,
      // });
      // console.debug("All Roles", response.data);
    },

    async actionUpdateRoles(roles) {
      console.debug("update roles", roles);

      return [];

      // const res = await pull({
      //   method: "PATCH",
      //   url: "/roles/update",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   data: {
      //     data: roles,
      //   },
      // });

      // await get().actionGetRoles();

      // return res.data;
    },

    async actionRemoveRoles(roles) {
      console.debug("remove roles", roles);

      return [];

      // const res = await pull({
      //   method: "DELETE",
      //   url: "/roles",
      //   headers: {
      //     "Content-Type": "application/json",
      //   },
      //   data: {
      //     data: roles,
      //   },
      // });

      // await get().actionGetRoles();

      // return res.data;
    },
  }));
};
