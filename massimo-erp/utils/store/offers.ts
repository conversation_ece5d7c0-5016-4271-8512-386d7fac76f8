import produce from "immer";
import create from "zustand";
import { OfferStatusType, OfferType } from "~utils/types/Project";

export interface OffersState {
  offerStatusList: OfferStatusType[];
  offerStatusColors: Record<OfferStatusType, string>;

  offers: OfferType[];
  addOffer(offer: OfferType): void;
  setOffer(offer: OfferType): void;
}

export const initOffersStore = (initial: Partial<OffersState> = {}) => {
  return create<OffersState>()((set, get) => ({
    offerStatusList: ["Offer", "Canceled", "Revised", "Completed"],
    offerStatusColors: {
      Request: "orange",
      Offer: "blue",
      Canceled: "red",
      Revised: "lime",
      Completed: "green",
    },

    offers: [],

    addOffer: (offer) => {
      set(
        produce<OffersState>((state) => {
          state.offers.unshift(offer);
        })
      );
    },

    setOffer: (offer) => {
      set(
        produce<OffersState>((state) => {
          const index = state.offers.findIndex((e) => e.id === offer.id);
          state.offers[index] = offer;
        })
      );
    },

    ...initial,
  }));
};
