import { useLayoutEffect } from "react";
import createContext from "zustand/context";
import { devtools } from "zustand/middleware";
import create, { StoreA<PERSON>, useStore as useBaseStore } from "zustand";

import { initUserStore, UserState } from "./user";
import { GlobalState, initGlobalStore } from "./global";
import { initTempStore, TempState } from "./temp";
import { initDataStore, DataState } from "./data";
import { initKanbanStore, KanbanState } from "./kanban";
import { initChatStore, ChatState } from "./chat";
import { initCustomersStore, CustomersState } from "./customers";
import { initUsersStore, UsersState } from "./users";
import { initDepartmentsStore, DepartmentsState } from "./departments";
import { initProjectsStore, ProjectsState } from "./projects";
import { initRolesStore, RolesState } from "./roles";
import { initOffersStore, OffersState } from "./offers";
import { initRequestsStore, RequestsState } from "./requests";
import { initAccessStore, AccessState } from "./access";
import { initAnalyticsStore, AnalyticsState } from "./analytics";
import { initSocketsStore, SocketsState } from "./sockets";
import { initTasksStore, TasksState } from "./tasks";
import { initFilesStore, FilesState } from "./files";
import { initNotificationsStore, NotificationsState } from "./notifications";
import { initPostsStore, PostsState } from "./posts";

/**
 * Types
 */

export interface TotalState {
  global: Partial<GlobalState>;
  user: Partial<UserState>;
  temp: Partial<TempState>;
  data: Partial<DataState>;
  kanban: Partial<KanbanState>;
  chat: Partial<ChatState>;
  customers: Partial<CustomersState>;
  users: Partial<UsersState>;
  departments: Partial<DepartmentsState>;
  projects: Partial<ProjectsState>;
  roles: Partial<RolesState>;
  offers: Partial<OffersState>;
  requests: Partial<RequestsState>;
  access: Partial<AccessState>;
  analytics: Partial<AnalyticsState>;
  sockets: Partial<SocketsState>;
  tasks: Partial<TasksState>;
  files: Partial<FilesState>;
  notifications: Partial<NotificationsState>;
  posts: Partial<PostsState>;
}

export interface TotalStoreWithoutApi {
  global: ReturnType<typeof initGlobalStore>;
  user: ReturnType<typeof initUserStore>;
  temp: ReturnType<typeof initTempStore>;
  data: ReturnType<typeof initDataStore>;
  kanban: ReturnType<typeof initKanbanStore>;
  chat: ReturnType<typeof initChatStore>;
  customers: ReturnType<typeof initCustomersStore>;
  users: ReturnType<typeof initUsersStore>;
  departments: ReturnType<typeof initDepartmentsStore>;
  projects: ReturnType<typeof initProjectsStore>;
  roles: ReturnType<typeof initRolesStore>;
  offers: ReturnType<typeof initOffersStore>;
  requests: ReturnType<typeof initRequestsStore>;
  access: ReturnType<typeof initAccessStore>;
  analytics: ReturnType<typeof initAnalyticsStore>;
  sockets: ReturnType<typeof initSocketsStore>;
  tasks: ReturnType<typeof initTasksStore>;
  files: ReturnType<typeof initFilesStore>;
  notifications: ReturnType<typeof initNotificationsStore>;
  posts: ReturnType<typeof initPostsStore>;
}

export type TotalStore = StoreApi<TotalStoreWithoutApi>;

let store: TotalStore;
let api: TotalStoreWithoutApi;

/**
 * Context
 */

const zustandContext = createContext<TotalStore>();
const initializeStore = (initialData: Partial<TotalState> = {}): TotalStore => {
  api = {
    ...api,

    global: initGlobalStore(initialData.global),
    user: initUserStore(initialData.user),
    temp: initTempStore(initialData.temp),
    data: initDataStore(initialData.data),
    kanban: initKanbanStore(initialData.kanban),
    chat: initChatStore(initialData.chat),
    customers: initCustomersStore(initialData.customers),
    users: initUsersStore(initialData.users),
    departments: initDepartmentsStore(initialData.departments),
    projects: initProjectsStore(initialData.projects),
    roles: initRolesStore(initialData.roles),
    offers: initOffersStore(initialData.offers),
    requests: initRequestsStore(initialData.requests),
    access: initAccessStore(initialData.access),
    analytics: initAnalyticsStore(initialData.analytics),
    sockets: initSocketsStore(initialData.sockets),
    tasks: initTasksStore(initialData.tasks),
    files: initFilesStore(initialData.files),
    notifications: initNotificationsStore(initialData.notifications),
    posts: initPostsStore(initialData.posts),
  };

  return create(devtools(() => api));
};

export const Provider = zustandContext.Provider;

/**
 * Hooks
 */

export const getStore = <T extends keyof TotalState>(storeName: T) => {
  return api[storeName];
};

export const useStore = <T extends keyof TotalState, U>(
  storeName: T,
  selector: (state: TotalState[T]) => U,
  equalityFn?: (a: U, b: U) => boolean
) => {
  const targetStore = zustandContext.useStore((state) => state[storeName]);

  return useBaseStore(targetStore, selector as any, equalityFn);
};

export function useCreateStore(
  serverInitialState: Partial<TotalState> = {}
): () => TotalStore {
  // Server side code: For SSR & SSG, always use a new store.
  if (typeof window === "undefined") {
    return () => initializeStore(serverInitialState);
  }
  // End of server side code

  // Customer side code:
  // Next.js always re-uses same store regardless of whether page is a SSR or SSG or CSR type.
  const isReusingStore = Boolean(store);
  store = store ?? initializeStore(serverInitialState);
  // When next.js re-renders _app while re-using an older store, then replace current state with
  // the new state (in the next render cycle).
  // (Why next render cycle? Because react cannot re-render while a render is already in progress.
  // i.e. we cannot do a setState() as that will initiate a re-render)
  //
  // eslint complaining "React Hooks must be called in the exact same order in every component render"
  // is ignorable as this code runs in same order in a given environment (i.e. customer or server)
  // eslint-disable-next-line react-hooks/rules-of-hooks
  useLayoutEffect(() => {
    // serverInitialState is undefined for CSR pages. It is up to you if you want to reset
    // states on CSR page navigation or not. I have chosen not to, but if you choose to,
    // then add `serverInitialState = getDefaultInitialState()` here.
    if (serverInitialState && isReusingStore) {
      const avail = store.getState();

      for (let data of Object.entries(serverInitialState)) {
        let [storeName, storeData] = data as [keyof TotalStoreWithoutApi, any];

        (avail[storeName] as any).setState({
          ...(avail[storeName] as any).getState(),
          ...storeData,
        });
      }
    }
  });

  return () => store;
}
