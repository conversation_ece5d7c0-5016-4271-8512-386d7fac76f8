import deepmerge from "deepmerge";
import _, {
  assign,
  extend,
  isArray,
  isDate,
  isObject,
  keyBy,
  merge,
  mergeWith,
  values,
} from "lodash";
import { TaskContentType } from "./types/Task";

export const removeFromList = (list: unknown[], index: number) => {
  const result = Array.from(list);
  const [removed] = result.splice(index, 1);
  return [removed, result];
};

export const addToList = (list: unknown[], index: number, element: unknown) => {
  const result = Array.from(list);
  result.splice(index, 0, element);
  return result;
};

export function padTo2Digits(num: any) {
  return num.toString().padStart(2, "0");
}

export function formatDate(rawDate: Date | string) {
  let date: Date;

  if (isDate(rawDate)) {
    date = rawDate;
  } else {
    date = new Date(rawDate);
  }

  return !!date
    ? [
        padTo2Digits(date.getDate()),
        padTo2Digits(date.getMonth() + 1),
        date.getFullYear(),
      ].join("/")
    : "";
}

export function formatTime(rawDate: Date) {
  let date: Date;

  if (isDate(rawDate)) {
    date = rawDate;
  } else {
    date = new Date(rawDate);
  }

  return !!date
    ? [padTo2Digits(date.getHours()), padTo2Digits(date.getMinutes())].join(":")
    : "";
}

export const hexToRGBA = (hexCode: string, opacity: number) => {
  let hex = hexCode.replace("#", "");

  if (hex.length === 3) {
    hex = `${hex[0]}${hex[0]}${hex[1]}${hex[1]}${hex[2]}${hex[2]}`;
  }

  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

export const calculateProgress = function (content: TaskContentType[]) {
  let totalSubtask = 0;
  let completedSubtask = 0;
  content
    .filter((e) => e.type === "sub-tasks")
    .map((c: TaskContentType) => {
      c.data.map((subtask: any) => {
        totalSubtask++;
        if (subtask.completed) {
          completedSubtask++;
        }
      });
    });

  return completedSubtask > 0 ? (completedSubtask / totalSubtask) * 100 : 0;
};

export const formatBytes = function (bytes: number, decimals = 2) {
  if (!+bytes) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

export const urlify = function (text: string) {
  var urlRegex = /(https?:\/\/[^\s]+)/g;
  return text.replace(urlRegex, function (url) {
    return '<a target="_blank" href="' + url + '">' + url + "</a>";
  });
  // or alternatively
  // return text.replace(urlRegex, '<a href="$1">$1</a>')
};

export const randomColor = () =>
  `#${Math.floor(Math.random() * 16777215).toString(16)}`;

export const numberFormatter = (value: number | string) =>
  !Number.isNaN(parseFloat(value! as string))
    ? `₺ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
    : "₺ ";

export const conflictMerge = <T extends {}, U extends {}>(
  source: T,
  data: U
): U & T => {
  const res: Record<any, any> = _.cloneDeep(source);

  _.forEach(data, (value, key) => {
    if (
      _.isArray(value) &&
      _.isArray(source[key as keyof T]) &&
      _.every(value, (x) => !!x.id)
    ) {
      res[key] = _.values(
        conflictMerge(
          keyBy(source[key as keyof T] as T, "id"),
          keyBy(value, "id")
        )
      );
    } else if (_.isArray(value) && _.isArray(source[key as keyof T])) {
      res[key] = [...(source[key as keyof T] as T[]), ...value];
    } else if (_.isObject(value) && _.isObject(source[key as keyof T])) {
      res[key] = conflictMerge(source[key as keyof T] as T, value);
    } else {
      res[key] = value;
    }
  });

  return res as U & T;
};

export const wait = (ms: number) => {
  return new Promise((res) => {
    setTimeout(() => {
      res(true);
    }, ms);
  });
};
