export type MessageType = {
  id: number;
  type: "text" | "image" | "file" | "task" | "price" | "subtasks";
  content: Record<string, any>;
  ownerId: number;
  chatId: number | string;
  createdAt: Date;
};

export type ChatType = {
  id: number;
  type: "offer" | "organization" | "person" | "task";

  offerId?: number;
  inactive?: boolean;
  organizationId?: number;
  personId?: number;
  taskId?: number;
  ownerId?: number;

  options: Record<string, any>;
};
