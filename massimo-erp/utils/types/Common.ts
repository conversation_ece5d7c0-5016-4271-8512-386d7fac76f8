import { GetServerSidePropsContext, GetStaticPropsContext } from "next";

export type RenderVariant = "SSR" | "SSG" | "ISR" | "Static";

export enum Langs {
  tr = "Türkçe",
  en = "English",
}

Langs.tr

export interface Renderers<U = {}> {
  getInitialProps?: (props: {
    ctx: GetServerSidePropsContext;
    locale: Langs;
  }) => Promise<Partial<U>> | Partial<U>;
  getStaticProps?: (props: {
    ctx: GetStaticPropsContext;
    locale: Langs;
  }) => Promise<Partial<U>> | Partial<U>;
  getServerSideProps?: (props: {
    ctx: GetServerSidePropsContext;
    locale: Langs;
  }) => Promise<Partial<U>> | Partial<U>;
}
