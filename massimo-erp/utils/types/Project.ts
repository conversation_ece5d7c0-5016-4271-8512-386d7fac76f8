export type OfferStatusType = "Offer" | "Canceled" | "Revised" | "Completed";

export type OfferPriorityType =
  | "Very Low"
  | "Low"
  | "Medium"
  | "High"
  | "Very High";

export type RequestType = {
  id: number;
  name: string;
  description: string;
  budget: number;
  start: Date;
  end: Date;
  customerId: number;
  approverId: number;
  projectId: number;

  departmentIds?: number[];
  allowedUserIds?: number[];
  restrictedUserIds?: number[];
};

export type OfferType = {
  id: number;
  name: string;
  description: string;
  budget: number;
  start: Date;
  end: Date;
  projectId: number;
  status: OfferStatusType;
  cancellationReason?: string;
  invoiceIds: number[];
  chatId?: number;
};

export type ProjectType = {
  id: number;
  priority: OfferPriorityType;
  offerIds: number[];
  requestId: number | string;
  departmentIds?: number[];
  roleIds: number[];
  allowedUserIds: number[];
  restrictedUserIds: number[];
};
