export type CommentType = {
  id: number;
  description: string;
  userId?: number;
  postId: number;
  updatedAt: Date;
};

export type PostType = {
  id: number;
  type: "post" | "story";
  description: string;
  fileIds: number[];
  userId: number;
  organizationId?: number;
  commentCount?: number;
  likeCount: number;
  isLiked: number | null;
  viewCount?: number;
  isViewed?: number | null;
  updatedAt: Date;
};
