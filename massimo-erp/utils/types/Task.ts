import { CustomerType } from "./Customer";
import { ProjectType } from "./Project";

export type TaskStatusTypes =
  | "To Do"
  | "Active"
  | "Completed"
  | "Problems"
  | "Archived";

export type TaskPriorityType =
  | "Very Low"
  | "Low"
  | "Medium"
  | "High"
  | "Very High"
  | "";

export type TaskContentType = {
  id: string;
  type: "sub-tasks" | "comment" | "attachment";
  data: any;
  contributors?: number[];
  dateList?: Date[];
};

export type TaskSubtaskType = {
  id: number;
  completed: boolean;
  label: string;
};

export type TaskTagType = {
  value: string;
  label: string;
  color: string;
};

export type TaskType = {
  id: number;
  title: string;
  description: string;
  status: TaskStatusTypes;
  priority: TaskPriorityType;

  private: boolean;
  start: Date;
  end: Date;
  customerPrivate: boolean;

  assigneeIds: number[];
  contributorIds: number[];
  projectId: number;
  recurrence?: {
    cron: string;
    waitForDeadline: boolean;
  };

  // tags: TaskTagType[];

  chatId?: number;
};

/*
# Yapılacaklar
 - [ ] Veri Düzeni
 - [ ]  Store 
 - [ ] Componentler
 - [ ] Eksik özellikler

 ## TasksType 
#### basic 
- **id** - string
- **title** - string 
- **description** - string 
- **private** - boolean 
- **status** - string
- **priority** - string 

#### objects
- project 
  - id 
  - name 
- customer ? 
  - id 
  - name 
- recurrence ?
  - cron - string 
  - waitForDeadline - boolean 

#### arrays
- assignees 
- contributers 
- contents
  - type ("sub-tasks", "comment", "attachment") 
  - data - any
  - contributers 
- dateList - date[]
- tags 
 - label 
 - color 

#### dates
- start - 
- end 

### SubTaskType 
- progress - computed number 
- contents - []
-  id - string 
- completed - boolean 
- label - string
*/
