import { TaskType } from "./Task";
import { UserType } from "./User";

export type CustomerType = {
  id: number;
  avatar: string;
  location: string;
  shortName: string;
  fullName: string;
  phone: string;
  email: string;
  taxDepartment: string;
  taxNumber: string;
  address: string;
  requestIds: number[];
  personIds: number[];
  offers: number[];
  tasks: number[];
  chatId?: number;
  recentActivities: {
    owner: Partial<UserType>;
    date: Date;
    text: string;
  }[];
};
