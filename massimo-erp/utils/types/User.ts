export type UserType = {
  id: number;
  avatar?: string;
  name: string;
  surname: string;
  email: string;
  phone: string;
  password: string;
  roleValues: Record<string, Record<string, string>>;
  defaultRoleId: number[];
  departmentIds: number[];
  allowedProjectIds: number[];
  restrictedProjectIds: number[];
  fileIds: number[];
  ownedChatIds: number[];
  chatIds: number[];
  plan: string;
  status: string;
  isValid: boolean;
  isBanned: boolean;
  isCustomer: boolean;
  organizationIds: number[];
};

export type ConnectionType = {
  id: number;
  isAccepted1: boolean;
  isAccepted2: boolean;
  version: number;
  createdAt: Date;
  target1Id: number;
  target2Id: number;
};
