import { AxiosInstance, AxiosRequestConfig, default as $axios } from "axios";
import { EventEmitter } from "eventemitter3";
import {
  isArray,
  isError,
  isNull,
  isNumber,
  isObject,
  isUndefined,
  uniqBy,
} from "lodash";
import { UserType } from "~utils/types/User";

import { io as SocketIO, Socket } from "socket.io-client";
import { ChatResponseType } from "./types/ChatResponseType";
import { getStore, useStore } from "~utils/store";
import { NotificationType } from "~utils/types/Notification";
import { showNotification } from "@mantine/notifications";
import { i18n, useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { TempState } from "~utils/store/temp";
import { StoreApi, UseBoundStore } from "zustand";

interface ResultData<T, U extends keyof SDK["errors"]> {
  error: U;
  message?: SDK["errors"][U];
  data: T | null;
}

class SDK extends EventEmitter {
  pull: AxiosInstance;
  io!: Socket;
  prevConnection = true;
  user?: UserType;
  lastTime: null | Date = null;
  status: null | UserType = null;

  tempStore!: UseBoundStore<StoreApi<TempState>>;

  baseURL =
    process.env.NEXT_PUBLIC_SDK_MODE === "development"
      ? process.env.NEXT_PUBLIC_SDK_LOCAL
      : process.env.NEXT_PUBLIC_SDK_PUBLIC;

  constructor() {
    super();

    const self = this;

    this.pull = $axios.create({
      baseURL: this.baseURL,

      headers: {
        common: {
          "Content-Type": "application/json",
        },
      },

      onUploadProgress: ({ progress }) => {
        if (self.tempStore) {
          self.tempStore.setState({
            uploadProgress: progress,
          });
        }
      },
    });
  }

  errors = {
    0: "NO_ERR",
    1: "UNKNOWN",
    401: "Unauthorized",
  };

  result<T, U extends keyof SDK["errors"]>(
    data: T | null,
    error?: U | Error
  ): ResultData<T, U> {
    if (isNumber(error)) {
      return {
        error,
        message: this.errors[error],
        data,
      };
    } else if (isError(error)) {
      return {
        error: 1 as any,
        message: error.message,
        data,
      };
    } else {
      return {
        error: 0 as any,
        data,
      };
    }
  }

  tokenized = false;

  async setToken(token: string) {
    this.tempStore = getStore("temp");

    this.pull.defaults.headers.common["Authorization"] = `Bearer ${token}`;

    if (typeof window !== "undefined") {
      this.io = SocketIO(this.baseURL!.split("/api").join(""), {
        auth: (cb) => {
          cb({
            token,
          });
        },
      });
    }

    if (!this.tokenized) {
      await this.setSockets();

      setInterval(async () => {
        if (!this.prevConnection) {
          await this.setSockets();

          this.prevConnection = true;
        }

        if (!this.io.connected) {
          this.io.offAny();

          this.prevConnection = false;
        }
      }, 1000);
    }

    this.tokenized = true;

    return true;
  }

  async verify() {
    if (
      this.lastTime &&
      (Date.now() - +this.lastTime) / 1000 < 60 &&
      this.status
    ) {
      return this.result<UserType, 0>(this.status);
    }

    try {
      console.debug("verify");

      this.lastTime = new Date();
      const res = await this.pull({
        method: "GET",
        url: "/auth/verify",
      });

      this.user = res.data;

      this.status = res.data;

      return this.result<UserType, 0>(res.data);
    } catch (err: any) {
      console.debug(err, "here");

      this.status = null;

      if (err.code === "ERR_NETWORK" || err?.message?.includes("401")) {
        this.emit("security", 401);
        return this.result(null, 401);
      } else {
        return this.result(null, err);
      }
    }
  }

  async make<T>(options: AxiosRequestConfig<any>, skipVerify = false) {
    if (!skipVerify) {
      await this.verify();
    }

    try {
      const res = await this.pull(options);

      if (this.tempStore) {
        this.tempStore.setState({
          uploadProgress: 0,
        });
      }

      return this.result<T, 0>(res.data);
    } catch (err: any) {
      console.debug(err, "here2");

      return this.result<T, 1>(null, err);
    }
  }

  async setSockets() {
    if (!this.user) {
      this.io.offAny();
      this.prevConnection = false;

      return;
    }

    this.io.offAny();

    console.debug("initializing sockets");

    const notificationsStore = getStore("notifications");

    const globalStore = getStore("global");
    const tempStore = getStore("temp");
    const chatStore = getStore("chat");
    const tasksStore = getStore("tasks");
    const usersStore = getStore("users");
    const projectsStore = getStore("projects");
    const offersStore = getStore("offers");

    this.io.on("new-message", (event: ChatResponseType) => {
      const targetEl = document.querySelector("#messageArea > div");

      const chatState = chatStore.getState();

      chatStore.setState({
        messages: uniqBy(
          [
            ...chatState.messages.filter(
              (e) => !isNull(e) && e.id !== event.data.id
            ),
            event.data,
          ],
          "id"
        ),
      });

      targetEl?.scrollTo({
        top: targetEl.scrollHeight + 100,
      });
    });

    this.io.on("delete-message", (event: ChatResponseType) => {
      const chatState = chatStore.getState();

      chatStore.setState({
        messages: chatState.messages.filter((x) => x.id !== event.data.id),
      });
    });

    this.io.onAny(async (event: string, data: any) => {
      // console.log(event, data);

      const usersState = usersStore.getState();

      let tasksState = tasksStore.getState();
      let globalState = globalStore.getState();
      let projectsState = projectsStore.getState();
      let chatState = chatStore.getState();
      let offersState = offersStore.getState();
      let notificationsState = notificationsStore.getState();

      const eventType = event.split("-")[0] as "update" | "delete";

      const type = event.split("-")[1] as
        | "connections"
        | "tasks"
        | "messages"
        | "projects"
        | "notifications"
        | "chats";

      let targetId: number;

      // const updatedMessage = i18n?.isInitialized && i18n?.t(`updated.${type}`);

      // const newNotification: NotificationType = {
      //   id: notificationsState.notifications.length,
      //   type,
      //   target: data.data.id[0],
      //   date: new Date(),
      //   isReaded: false,
      // };

      // notificationsStore.setState({
      //   notifications: [...notificationsState.notifications, newNotification],
      // });

      tempStore.setState({
        updateInformation: {
          isOpen: true,
          type: type as
            | "connections"
            | "tasks"
            | "messages"
            | "projects"
            | "notifications",
        },
      });

      try {
        if (eventType === "update") {
          if (type !== "notifications") {
            targetId = data?.data?.id[0];

            if (type === "connections") {
              await usersState.actionGetConnections(0, true);
              await usersState.actionGetConnectionRequests();
            }

            if (
              type === "tasks" &&
              tasksState.tasks.some((e) => e.id === +targetId)
            ) {
              await tasksState.actionGetTasks({
                id: +targetId,
                projectId: globalStore.getState().selectedProjectId,
              });
            }

            if (
              type === "projects" &&
              projectsState.projects.some((e) => e.id === targetId)
            ) {
              await projectsState.actionGetProjects(
                { id: targetId },
                null,
                null,
                true
              );

              offersState = offersStore.getState();

              const project = projectsState.projects.find(
                (e) => e.id === targetId
              );

              const offer = offersState.offers.find(
                (e) => e.id === project?.offerIds.at(-1)
              );
              const tempOffer = tempStore.getState().lastOffer;

              await tempStore.setState({
                lastOffer: {
                  ...tempOffer,
                  budget: offer?.budget || tempOffer.budget,
                },
              });
            }

            if (type === "chats") {
              setTimeout(async () => {
                await chatState.actionGetChats([targetId]);
              }, 100);
            }
          } else {
            if (isArray(data.data)) {
              const activeUserId = globalState.activeUserId;

              let newNotifications: NotificationType[] = [];

              await Promise.all(
                data.data.map(async (notification: NotificationType) => {
                  if (notification.userId === activeUserId) {
                    const activeChat = chatState.chats.find(
                      (e) => e.id === +notification.value
                    );

                    const tempState = tempStore.getState();

                    if (
                      notification.target === "messages" &&
                      (activeChat?.type === "person" ||
                      activeChat?.type === "organization"
                        ? window.location.pathname
                            .split("/apps/chat/")
                            .includes(`${notification.value}`)
                        : activeChat?.type === "task"
                        ? tempState.isOpenTaskViewModal &&
                          tempStore.getState().taskViewModalData.id ===
                            activeChat.taskId
                        : tempState.projectModal.isOpen &&
                          tempState.projectModal.data.id ===
                            activeChat?.offerId)
                    ) {
                      notificationsState.actionViewNotification([
                        { id: notification.id },
                      ]);
                    } else {
                      tempStore.setState({
                        notification: {
                          play: true,
                        },
                      });

                      newNotifications.push(notification);

                      if (notification.target === "messages") {
                        await chatState.actionGetChats([+notification.value]);
                        await chatState.actionGetMessages(
                          +notification.value,
                          0
                        );

                        const activeChat = chatState.chats.find(
                          (e) => e.id === +notification.value
                        );

                        if (activeChat?.type === "task") {
                          await tasksState.actionGetTasks({
                            id: activeChat.taskId,
                          });
                        } else if (activeChat?.type === "offer") {
                          // await projectsState.actionGetProjects({});
                        }
                      } else if (notification.target === "tasks") {
                        await tasksState.actionGetTasks({
                          id: +notification.value,
                        });
                      }
                    }
                  }

                  // if (notification.target === "tasks") {
                  //   try {
                  //     await tasksState.actionGetTasks({
                  //       id: +notification.value as number,
                  //     });
                  //   } finally {
                  //     const activeTask = tasksStore
                  //       .getState()
                  //       .tasks.find((e) => e.id === +notification.value);

                  //     if (
                  //       activeTask?.assigneeIds.includes(
                  //         activeUserId as number
                  //       ) ||
                  //       notification.userId === activeUserId
                  //     ) {
                  //       newNotifications.push(notification);
                  //     }
                  //   }
                  // } else if (notification.userId === activeUserId) {
                  //   newNotifications.push(notification);
                  // }
                })
              );

              await notificationsStore.setState({
                notifications: uniqBy(
                  [...notificationsState.notifications, ...newNotifications],
                  "id"
                ),
              });
            }
          }
        } else if (eventType === "delete") {
          targetId = data?.data?.data[0];

          if (type === "chats") {
            await chatStore.setState({
              chats: chatState.chats.filter((e) => e.id !== targetId),
              subscribedChats: chatState.subscribedChats.filter(
                (e) => e.id !== targetId
              ),
            });

            notificationsStore.setState({
              notifications: notificationsStore
                .getState()
                .notifications.filter(
                  (e) => e.target === "messages" && +e.value !== targetId
                ),
            });
          }
        }
      } finally {
        tempStore.setState({
          updateInformation: {
            isOpen: false,
            type: null,
          },
        });
      }
    });

    return;
  }

  clear() {
    this.user = undefined;
    this.pull.defaults.headers.common["Authorization"] = undefined;
  }
}

export const sdk = new SDK();
