export const accessMap = {
  customer: {
    "/faq": true,
    "/profile": true,
    "/privacy": true,
    "/contact": true,
    "/apps/chat": true,
    "/explore": true,

    "/apps/kanban": false,
    "/apps/calendar": false,
    "/dashboard/crm": false,
    "/apps/crm/invoices": false,
    "/dashboard/analytics": false,

    "/admin/users": false,
    "/admin/projects": false,
    "/admin/requests": false,
    "/admin/settings": false,
    "/admin/customers": false,
    "/admin/permissions": false,
    "/admin/departments": false,
  },

  employee: {
    "/faq": true,
    "/profile": true,
    "/privacy": true,
    "/contact": true,
    "/apps/chat": true,
    "/apps/kanban": true,
    "/apps/calendar": true,
    "/explore": true,

    "/dashboard/crm": true,
    "/apps/crm/invoices": false,
    "/dashboard/analytics": false,

    "/admin/users": false,
    "/admin/requests": false,
    "/admin/projects": false,
    "/admin/settings": false,
    "/admin/customers": false,
    "/admin/departments": false,
    "/admin/permissions": false,
  },

  admin: {
    "/faq": true,
    "/profile": true,
    "/privacy": true,
    "/contact": true,
    "/apps/chat": true,
    "/apps/kanban": true,
    "/apps/calendar": true,
    "/explore": true,

    "/dashboard/crm": true,
    "/apps/crm/invoices": true,
    "/dashboard/analytics": true,

    "/admin/users": true,
    "/admin/projects": true,
    "/admin/requests": true,
    "/admin/settings": true,
    "/admin/customers": true,
    "/admin/departments": true,
    "/admin/permissions": true,
  },
};

export const checkAccess = (
  navigations: any[],
  type: keyof typeof accessMap
): any[] => {
  const responsible = accessMap[type];

  const loop = (dataList: any[], res: any[] = []): any[] => {
    dataList.forEach((navigation, i) => {
      if (navigation.childrens) {
        navigation.childrens = loop(navigation.childrens);

        if (navigation.childrens.length > 0) {
          res.push(navigation);
        }
      } else if (navigation.path) {
        if (responsible[navigation.path as keyof typeof responsible]) {
          res.push(navigation);
        }
      } else if (navigation.label) {
        res.push(navigation);
      }
    });

    return res.filter((x, i) => {
      if (!x.label) {
        return x;
      } else {
        if (res[i + 1] && !res[i + 1].label) {
          return x;
        }
      }
    });
  };

  return loop(navigations, []);
};
