import Head from "next/head";
import { AppProps } from "next/app";
import shallow from "zustand/shallow";

import { MantineProvider, ColorSchemeProvider } from "@mantine/core";
import { NotificationsProvider } from "@mantine/notifications";
import { SpotlightProvider } from "@mantine/spotlight";
import { ModalsProvider } from "@mantine/modals";
import type { SpotlightAction } from "@mantine/spotlight";

import { Page } from "~types";
import { useStore } from "~utils/store";
import { fontWeights } from "~utils/defaults";

import {
  MagnifyingGlassIcon,
  ChatBubbleLeftIcon,
  CalendarDaysIcon,
  CodeBracketIcon,
  ChartPieIcon,
  ChartBarIcon,
  UserIcon,
  DocumentTextIcon,
  ClipboardDocumentListIcon,
  BuildingOffice2Icon,
  UsersIcon,
  BuildingStorefrontIcon,
  CommandLineIcon,
  LockClosedIcon,
  QuestionMarkCircleIcon,
  ExclamationCircleIcon,
  PhoneIcon,
} from "@heroicons/react/24/outline";
import { useEffect } from "react";
import { useRouter } from "next/router";
import Cookies from "~components/Cookies";
import RouterTransition from "~components/RouterTransition";
import { hexToRGBA } from "./tools";
import { useTranslation } from "next-i18next";

export const CommonProvider: Page<{
  pageProps: AppProps["pageProps"];
}> = (props) => {
  const { children } = props;
  const router = useRouter();
  const { t } = useTranslation();

  const actions: SpotlightAction[] = [
    {
      title: t("spotlight.items.0.title"),
      group: t("spotlight.groups.dashboard"),
      description: t("spotlight.items.0.description"),
      onTrigger: () => router.push("/dashboard/crm"),
      icon: <ChartPieIcon style={{ width: 20, height: 20 }} />,
    },
    // {
    //   title: t("spotlight.items.1.title"),
    //   group: t("spotlight.groups.dashboard"),
    //   description: t("spotlight.items.1.description"),
    //   onTrigger: () => router.push("/dashboard/analytics"),
    //   icon: <ChartBarIcon style={{ width: 20, height: 20 }} />,
    // },
    {
      title: t("spotlight.items.2.title"),
      description: t("spotlight.items.2.description"),
      onTrigger: () => router.push("/profile"),
      icon: <UserIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.3.title"),
      group: t("spotlight.groups.apps"),
      description: t("spotlight.items.3.description"),
      onTrigger: () => router.push("/apps/chat"),
      icon: <ChatBubbleLeftIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.4.title"),
      group: t("spotlight.groups.apps"),
      description: t("spotlight.items.4.description"),
      onTrigger: () => router.push("/apps/calendar"),
      icon: <CalendarDaysIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.5.title"),
      group: t("spotlight.groups.apps"),
      description: t("spotlight.items5.description"),
      onTrigger: () => router.push("/apps/kanban"),
      icon: <CodeBracketIcon style={{ width: 20, height: 20 }} />,
    },
    // {
    //   title: t("spotlight.items.6.title"),
    //   group: t("spotlight.groups.apps"),
    //   description: t("spotlight.items.6.description"),
    //   onTrigger: () => router.push("/apps/crm/invoices"),
    //   icon: <DocumentTextIcon style={{ width: 20, height: 20 }} />,
    // },
    {
      title: t("spotlight.items.7.title"),
      group: t("spotlight.groups.support"),
      description: t("spotlight.items.7.description"),
      onTrigger: () => router.push("/faq"),
      icon: <QuestionMarkCircleIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.8.title"),
      group: t("spotlight.groups.support"),
      description: t("spotlight.items.8.description"),
      onTrigger: () => router.push("/privacy"),
      icon: <ExclamationCircleIcon style={{ width: 20, height: 20 }} />,
    },
    // {
    //   title: t("spotlight.items.9.title"),
    //   group: t("spotlight.groups.support"),
    //   description: t("spotlight.items.9.description"),
    //   onTrigger: () => router.push("/contact"),
    //   icon: <PhoneIcon style={{ width: 20, height: 20 }} />,
    // },
    // {
    //   title: t("spotlight.items.10.title"),
    //   group: t("spotlight.groups.admin"),
    //   description: t("spotlight.items.10.description"),
    //   onTrigger: () => router.push("/admin/invoices"),
    //   icon: <ClipboardDocumentListIcon style={{ width: 20, height: 20 }} />,
    // },
    {
      title: t("spotlight.items.11.title"),
      group: t("spotlight.groups.admin"),
      description: t("spotlight.items.11.description"),
      onTrigger: () => router.push("/admin/departments"),
      icon: <BuildingOffice2Icon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.12.title"),
      group: t("spotlight.groups.admin"),
      description: t("spotlight.items.12.description"),
      onTrigger: () => router.push("/admin/users"),
      icon: <UsersIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.13.title"),
      group: t("spotlight.groups.admin"),
      description: t("spotlight.items.13.description"),
      onTrigger: () => router.push("/admin/customers"),
      icon: <BuildingStorefrontIcon style={{ width: 20, height: 20 }} />,
    },
    {
      title: t("spotlight.items.14.title"),
      group: t("spotlight.groups.admin"),
      description: t("spotlight.items.14.description"),
      onTrigger: () => router.push("/admin/projects"),
      icon: <CommandLineIcon style={{ width: 20, height: 20 }} />,
    },
    // {
    //   title: t("spotlight.items.15.title"),
    //   group: t("spotlight.groups.admin"),
    //   description: t("spotlight.items.15.description"),
    //   onTrigger: () => router.push("/admin/roles"),
    //   icon: <LockClosedIcon style={{ width: 20, height: 20 }} />,
    // },
  ];

  const { colorScheme, toggleColorScheme } = useStore(
    "global",
    (state) => ({
      colorScheme: state.colorScheme!,
      toggleColorScheme: state.toggleColorScheme!,
    }),
    shallow
  );

  const { setMobileIsOpenNavbar } = useStore(
    "temp",
    (state) => ({
      setMobileIsOpenNavbar: state.setMobileIsOpenNavbar!,
    }),
    shallow
  );

  const { actionGetGeneralReport } = useStore(
    "analytics",
    (state) => ({
      actionGetGeneralReport: state.actionGetGeneralReport!,
    }),
    shallow
  );

  useEffect(() => {
    const handleRouteChange = async () => {
      setMobileIsOpenNavbar(false);

      await actionGetGeneralReport();
    };

    router.events.on("routeChangeStart", handleRouteChange);

    // If the component is unmounted, unsubscribe
    // from the event with the `off` method:
    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [router, setMobileIsOpenNavbar]);

  return (
    <>
      <Head>
        <title>Massimo ERP</title>
        <meta
          name="viewport"
          content="minimum-scale=1, initial-scale=1, width=device-width"
        />
        <link rel="icon" href="/favicon.png" />
      </Head>

      <ColorSchemeProvider
        colorScheme={colorScheme}
        toggleColorScheme={toggleColorScheme}
      >
        <MantineProvider
          theme={{
            colorScheme,
            other: {
              fontWeights,
            },
            white: "#fdfdfd",
            defaultRadius: "md",
            globalStyles: (theme) => ({
              "button:disabled": {
                color: "inherit !important",
              },
              ".fc-orange": {
                backgroundColor: theme.colors.orange[7],
                color: theme.colors.gray[1],
                "&:hover": {
                  backgroundColor: theme.colors.orange[8],
                },
                ".fc-daygrid-event-dot": {
                  borderColor: theme.colors.orange[4],
                },
              },
              ".fc-blue": {
                backgroundColor: theme.colors.blue[7],
                color: theme.colors.gray[1],
                "&:hover": {
                  backgroundColor: theme.colors.blue[8],
                },
                ".fc-daygrid-event-dot": {
                  borderColor: theme.colors.blue[4],
                },
              },
              ".fc-green": {
                backgroundColor: theme.colors.green[7],
                color: theme.colors.gray[1],
                "&:hover": {
                  backgroundColor: theme.colors.green[8],
                },
                ".fc-daygrid-event-dot": {
                  borderColor: theme.colors.green[4],
                },
              },
              ".fc-red": {
                backgroundColor: theme.colors.red[8],
                color: theme.colors.gray[1],
                "&:hover": {
                  backgroundColor: theme.colors.red[9],
                },
                ".fc-daygrid-event-dot": {
                  borderColor: theme.colors.red[4],
                },
              },
              ".fc-gray": {
                backgroundColor: theme.colors.gray[8],
                color: theme.colors.gray[6],
                "&:hover": {
                  backgroundColor: theme.colors.gray[9],
                },
                ".fc-daygrid-event-dot": {
                  borderColor: theme.colors.dark[6],
                },
              },

              ".mantine-Notification-root": {
                zIndex: 999999999,
                boxShadow:
                  theme.colorScheme === "dark"
                    ? `0px 0px 15px 3px ${theme.colors.dark[8]}`
                    : `0px 0px 15px 3px ${hexToRGBA(
                        theme.colors.dark[5],
                        0.2
                      )}`,
              },

              "::-webkit-scrollbar": {
                width: 7,
              },

              "::-webkit-scrollbar-track": {
                background: "transparent",
              },

              "::-webkit-scrollbar-thumb": {
                background:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[3]
                    : theme.colors.gray[5],
                borderRadius: 8,
              },

              "::-webkit-scrollbar-thumb:hover": {
                background:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[4]
                    : theme.colors.gray[4],
              },
            }),
          }}
          withGlobalStyles
          withNormalizeCSS
        >
          <RouterTransition />
          <ModalsProvider>
            <NotificationsProvider position="bottom-right" zIndex={1000}>
              <SpotlightProvider
                radius="md"
                actions={actions}
                searchIcon={
                  <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
                }
                searchPlaceholder={`${t("search")}...`}
                nothingFoundMessage={`${t("nothingFound")}...`}
                transition="fade"
                limit={5}
              >
                <Cookies />
                {children}
              </SpotlightProvider>
            </NotificationsProvider>
          </ModalsProvider>
        </MantineProvider>
      </ColorSchemeProvider>
    </>
  );
};

export default CommonProvider;
