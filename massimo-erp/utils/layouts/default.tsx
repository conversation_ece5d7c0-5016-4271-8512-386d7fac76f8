import { AppProps } from "next/app";
import shallow from "zustand/shallow";

import {
  ScrollArea,
  Box,
  MediaQuery,
  LoadingOverlay,
  Affix,
  Transition,
  Button,
  ActionIcon,
} from "@mantine/core";

import { Page } from "~types";
import { useStore } from "~utils/store";

import AppNavbar from "~components/SharedComponents/Navbar";
import AppHeader from "~components/SharedComponents/Header";
import TaskViewModal from "~components/Modals/TaskViewModal";
import NewTaskModal from "~components/Modals/NewTaskModal";
import NewTagModal from "~components/Modals/NewTagModal";
import Footer from "~components/SharedComponents/Footer";
import SharePostModal from "~components/Modals/SharePostModal";
import ImageModal from "~components/Modals/ImageModal";
import { useFullscreen, useMediaQuery } from "@mantine/hooks";
import ApexChartWrapper from "~components/ApexChartWrapper";
import OffersModal from "~components/Modals/OffersModal";
import SendRequestModal from "~components/Modals/SendRequestModal";
import ProjectSelectorModal from "~components/Modals/ProjectSelector";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";
import { useEffect, useRef, useState } from "react";
import UserModal from "~components/Admin/Users/<USER>";
import StoryViewModal from "~components/Stories/StoryViewModal";
import PostViewModal from "~components/Post/PostViewModal";
import UpdateInformation from "~components/UpdateInformation";
import { ChevronUpIcon } from "@heroicons/react/24/outline";
import ViewLikesModal from "~components/Post/ViewLikesModal";
import ViewsModal from "~components/Stories/ViewsModal";
import ProfileSearchModal from "~components/Modals/ProfileSearchModal";
import NotificationSound from "~components/NotificationSound";

const DefaultLayout: Page<{
  pageProps: AppProps["pageProps"];
}> = (props) => {
  const { children } = props;

  const { colorScheme, toggleColorScheme } = useStore(
    "global",
    (state) => ({
      colorScheme: state.colorScheme!,
      toggleColorScheme: state.toggleColorScheme!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    updateInformation,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      updateInformation: state.updateInformation!,
    }),
    shallow
  );

  const { pathname } = useRouter();
  const matches = useMediaQuery("(max-width: 992px)");
  const viewport = useRef<HTMLDivElement>(null);

  const [scrollPosition, onScrollPositionChange] = useState({ x: 0, y: 0 });

  const scrollToTop = () =>
    viewport.current?.scrollTo({ top: 0, behavior: "smooth" });

  return (
    <>
      <div
        style={{
          position: "relative",
          display: "flex",
          height: "100vh",
        }}
      >
        <AppNavbar
          colorScheme={colorScheme}
          toggleColorScheme={() => toggleColorScheme()}
        />
        <div
          style={{
            width: "100%",
            minHeight: "100vh",
            display: "flex",
            flexDirection: "column",
          }}
        >
          <AppHeader
            colorScheme={colorScheme}
            toggleColorScheme={() => toggleColorScheme()}
          ></AppHeader>
          <UserModal />
          <TaskViewModal />
          <NewTaskModal />
          <NewTagModal />
          <SharePostModal />
          <ImageModal />
          <OffersModal />
          <SendRequestModal />
          <ProjectSelectorModal />
          <StoryViewModal />
          <PostViewModal />
          <ViewLikesModal />
          <ViewsModal />
          <ProfileSearchModal />

          <NotificationSound />

          <ScrollArea
            viewportRef={viewport}
            onScrollPositionChange={onScrollPositionChange}
            sx={{
              width: "100%",
              height: "100vh",
            }}
          >
            <main
              style={{
                display: "flex",
                flexDirection: "column",
                justifyContent: "space-between",
                position: "relative",
                minHeight: "calc(100vh - 70px)",
                flexGrow: 1,
              }}
            >
              <ApexChartWrapper>
                <Box
                  p={
                    pathname
                      .split("/")
                      .some((e) => ["explore", "profile", "kanban"].includes(e))
                      ? 0
                      : matches
                      ? pathname.split("/").some((e) => e === "chat")
                        ? 0
                        : 16
                      : 32
                  }
                >
                  {children}
                </Box>
              </ApexChartWrapper>

              {/* {pathname.split("/").findIndex((e) => e === "chat") === -1 && (
                <Footer
                  links={[
                    {
                      link: "/faq",
                      label: t("footer.faq"),
                    },
                    {
                      link: "/privacy",
                      label: t("footer.privacy"),
                    },
                    {
                      link: "/contact",
                      label: t("footer.contact"),
                    },
                  ]}
                />
              )} */}
            </main>
            <LoadingOverlay
              visible={isLoading && loadingLevel === 0}
              loaderProps={{ size: "lg", variant: "bars" }}
              overlayOpacity={0.45}
            />
          </ScrollArea>

          <Affix position={{ bottom: 20, right: 20 }}>
            <Transition transition="slide-up" mounted={scrollPosition.y > 0}>
              {(transitionStyles) => (
                <ActionIcon
                  size="xl"
                  variant="light"
                  color="blue"
                  style={transitionStyles}
                  onClick={scrollToTop}
                >
                  <ChevronUpIcon width={20} height={20} />
                </ActionIcon>
              )}
            </Transition>
          </Affix>
        </div>
        {updateInformation.isOpen && <UpdateInformation />}
      </div>
    </>
  );
};

export default DefaultLayout;
