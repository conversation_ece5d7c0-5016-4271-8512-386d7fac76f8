import { AppProps } from "next/app";

import { ScrollArea, Group, Box } from "@mantine/core";

import { Page } from "~types";
import ColorSchemeToggler from "~components/ColorSchemeToggler";
import LanguageSelector from "~components/LanguageSelector";
import { useMediaQuery } from "@mantine/hooks";

const Auth: Page<{
  pageProps: AppProps["pageProps"];
}> = (props) => {
  const { children } = props;

  const matches = useMediaQuery("(max-width: 500px)");

  return (
    <ScrollArea
      sx={{
        height: "100vh",
      }}
    >
      <Box>
        <Group
          sx={{
            position: "absolute",
            top: 16,
            right: 16,
            zIndex: 1000,
          }}
        >
          <LanguageSelector />
          <ColorSchemeToggler />
        </Group>
        <main
          style={{
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
            position: "relative",
            minHeight: "100vh",
            flexGrow: 1,
            padding: matches ? 12 : 32,
          }}
        >
          {children}
        </main>
      </Box>
    </ScrollArea>
  );
};

export default Auth;
