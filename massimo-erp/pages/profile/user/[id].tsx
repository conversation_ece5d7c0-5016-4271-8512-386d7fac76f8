import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import EmployeeProfile from "~components/Profile/EmployeeProfile";
import { useRouter } from "next/router";
import { useCallback, useEffect, useState } from "react";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { LoadingOverlay } from "@mantine/core";

const Profile = function () {
  const { actionGetUsers } = useStore(
    "users",
    (state) => ({
      actionGetUsers: state.actionGetUsers!,
    }),
    shallow
  );

  const { query } = useRouter();
  const [isFetching, setIsFetching] = useState(true);

  const getUser = useCallback(async () => {
    if (query.id) {
      setIsFetching(true);

      try {
        await actionGetUsers([+query.id]);
      } finally {
        setIsFetching(false);
      }
    }
  }, [actionGetUsers, query.id]);

  useEffect(() => {
    getUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return isFetching ? (
    <LoadingOverlay
      visible={true}
      loaderProps={{ size: "lg", variant: "bars" }}
    />
  ) : (
    <EmployeeProfile id={+(query.id || -1)} />
  );
};

export const getServerSideProps: Renderers["getServerSideProps"] = async (
  context
) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Profile;
