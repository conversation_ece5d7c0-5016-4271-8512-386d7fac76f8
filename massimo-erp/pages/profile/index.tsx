import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import EmployeeProfile from "~components/Profile/EmployeeProfile";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

const Profile = function () {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  return (
    <>
      <EmployeeProfile id={activeUserId} />
    </>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Profile;
