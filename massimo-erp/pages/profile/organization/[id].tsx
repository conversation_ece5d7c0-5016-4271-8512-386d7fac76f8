import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import EmployeeProfile from "~components/Profile/EmployeeProfile";
import { useRouter } from "next/router";
import CustomerProfile from "~components/Profile/CustomerProfile";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useCallback, useEffect, useState } from "react";

const Profile = function () {
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );

  const { query, push } = useRouter();
  const [hasCustomer, setHasCustomer] = useState(false);

  useEffect(() => {
    if (query.id && !customers.find((e) => e.id === +(query.id as string))) {
      push("/search");
    } else {
      setHasCustomer(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return hasCustomer ? <CustomerProfile id={+(query.id || -1)} /> : <></>;
};

export const getServerSideProps: Renderers["getServerSideProps"] = async (
  context
) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Profile;
