import "~styles/globals.css";
import "@fullcalendar/common/main.css";
import "@fullcalendar/daygrid/main.css";
import "@fullcalendar/timegrid/main.css";
import "@fullcalendar/list/main.css";

import { appWithTranslation } from "next-i18next";

import { App } from "~types";
import { Provider, TotalState, useCreateStore } from "~utils/store";

import * as layoutList from "~utils/layouts";
import { GuardMaster } from "~guards";
import CommonProvider from "~utils/CommonProvider";
import { getTranslations } from "~utils/getTranslations";
import ErrorBoundary from "~components/ErrorBoundary";

export interface AppPageProps {
  initialZustandState?: Partial<TotalState>;
}

const App: App<AppPageProps> = (props) => {
  const { Component, pageProps, initialZustandState } = props;

  const Layout = layoutList[Component.layout ?? "default"];
  const guards = Component.guards ?? [];

  const createStore = useCreateStore(initialZustandState);

  return (
    <Provider createStore={createStore}>
      <CommonProvider pageProps={pageProps}>
        <GuardMaster guards={guards}>
          <Layout pageProps={pageProps}>
            <ErrorBoundary>
              <Component {...pageProps} />
            </ErrorBoundary>
          </Layout>
        </GuardMaster>
      </CommonProvider>
    </Provider>
  );
};

// App.getInitialProps = ({ ctx }) => ({
//   initialZustandState: {
//     global: {
//       colorScheme:
//         (getCookie("mantine-color-scheme", ctx) as any) || "light",
//     },
//   },
// });

export default appWithTranslation(App as any);
