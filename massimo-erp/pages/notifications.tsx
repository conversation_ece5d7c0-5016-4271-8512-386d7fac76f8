import React, { useCallback, useState } from "react";
import shallow from "zustand/shallow";

import { CC, Page, Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";

import {
  ActionIcon,
  Anchor,
  Button,
  Container,
  Divider,
  Group,
  Loader,
  Paper,
  Stack,
  Text,
  ThemeIcon,
} from "@mantine/core";
import { NotificationType } from "~utils/types/Notification";
import ActiveAvatar from "~components/ActiveAvatar";
import {
  BellIcon,
  DocumentTextIcon,
  TrashIcon,
  XMarkIcon,
  CheckIcon,
} from "@heroicons/react/24/outline";
import { findLast } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { ConnectionType } from "~utils/types/User";
import Notification from "~components/SharedComponents/NotificationMenu/Notification";

const NotificationCenter: Page = () => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { notifications, actionViewNotification } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
      actionViewNotification: state.actionViewNotification!,
    }),
    shallow
  );
  const {
    users,
    connectionRequests,
    actionGetConnections,
    actionGetConnectionRequests,
    actionConnectUser,
    actionRejectUser,
  } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      connectionRequests: state.connectionRequests!,
      actionGetConnections: state.actionGetConnections!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
      actionConnectUser: state.actionConnectUser!,
      actionRejectUser: state.actionRejectUser!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { push } = useRouter();

  const [viewAll, setViewAll] = useState(false);
  const [fetchingUsers, setFetchingUsers] = useState<number[]>([]);
  const [deletedNotifications, setDeletedNotifications] = useState<number[]>(
    []
  );

  const connectionRequestNotifications = connectionRequests.filter(
    (e) =>
      (e.target1Id === activeUserId && e.isAccepted2) ||
      (e.target2Id === activeUserId && e.isAccepted1)
  );

  const markAllAsRead = useCallback(async () => {
    setViewAll(true);

    try {
      if (notifications.length > 0) {
        await actionViewNotification(
          notifications.map((e: NotificationType) => ({ id: e.id }))
        );
      }
    } finally {
      setViewAll(false);
    }
  }, [actionViewNotification, notifications]);

  const handleDelete = useCallback(
    async (id: number) => {
      setDeletedNotifications((e) => [...e, id]);

      try {
        await actionViewNotification([{ id }]);
      } finally {
        setDeletedNotifications((e) => e.filter((x) => x !== id));
      }
    },
    [actionViewNotification]
  );

  const getConnections = useCallback(async () => {
    await actionGetConnections(0);
    await actionGetConnectionRequests();
  }, [actionGetConnectionRequests, actionGetConnections]);

  const handleConnect = useCallback(
    async (targetId: number) => {
      setFetchingUsers((v) => [...v, targetId]);

      try {
        await actionConnectUser(targetId);
        await getConnections();
      } finally {
        setFetchingUsers((v) => v.filter((e) => e !== targetId));
      }
    },
    [actionConnectUser, getConnections]
  );

  const handleReject = useCallback(
    async (targetId: number) => {
      setFetchingUsers((v) => [...v, targetId]);

      try {
        await actionRejectUser(targetId);
        await getConnections();
      } finally {
        setFetchingUsers((v) => v.filter((e) => e !== targetId));
      }
    },
    [actionRejectUser, getConnections]
  );

  return (
    <Container>
      <Stack>
        <Group position="apart">
          <Group spacing="xs">
            <BellIcon style={{ width: 22, height: 22 }} strokeWidth={2} />

            <Text size="md" weight={600}>
              {t("notifications.center")}
            </Text>
          </Group>

          <Button
            onClick={markAllAsRead}
            disabled={notifications.length === 0}
            loading={viewAll}
            loaderProps={{ variant: "bars" }}
            variant="light"
            size="xs"
          >
            {t("notifications.markAllAsRead")}
          </Button>
        </Group>

        <Divider />

        {notifications.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" align="center">
            {t("profile.empty")}
          </Text>
        )}

        {connectionRequestNotifications.length > 0 && (
          <Stack spacing={8}>
            <Text size="xs" weight={600}>
              Connections
            </Text>

            {connectionRequestNotifications.map(
              (connection: ConnectionType, i: number) => {
                let user = users.find(
                  (e) =>
                    e.id ===
                    [connection.target1Id, connection.target2Id].filter(
                      (e) => e !== activeUserId
                    )[0]
                );
                let notificationMessage = `${user?.name} ${user?.surname}`;
                let notificationRoute = "/";

                return (
                  <Paper
                    key={"notification-" + i}
                    sx={(theme) => ({
                      background:
                        theme.colorScheme === "dark"
                          ? theme.colors.dark[8]
                          : theme.colors.gray[0],
                      ":hover": {
                        background:
                          theme.colorScheme === "dark"
                            ? theme.colors.dark[9]
                            : theme.colors.gray[2],
                      },
                    })}
                    p="md"
                  >
                    <Group
                      position="apart"
                      align="center"
                      noWrap
                      spacing={10}
                      sx={{ position: "relative" }}
                    >
                      <Group
                        align="center"
                        spacing={10}
                        noWrap
                        sx={{ width: "100%" }}
                      >
                        {!!user ? (
                          <ActiveAvatar userId={user?.id} size={32} />
                        ) : (
                          <ThemeIcon size={32} variant="light">
                            <DocumentTextIcon width={20} height={20} />
                          </ThemeIcon>
                        )}
                        <Stack
                          spacing={0}
                          sx={{ width: "100%" }}
                          align="flex-start"
                        >
                          <Text size="xs" weight={700}>
                            {t(`notificationTitles.connections`)}
                          </Text>
                          <Anchor
                            size="xs"
                            weight={600}
                            color="dimmed"
                            onClick={() => {
                              push(notificationRoute);
                            }}
                          >
                            {notificationMessage}
                          </Anchor>
                        </Stack>
                      </Group>

                      <Group spacing={4} noWrap sx={{ height: 32 }}>
                        {fetchingUsers.includes(user?.id as number) ? (
                          <Loader size="xs" variant="bars" mr={8} />
                        ) : (
                          <>
                            <ActionIcon
                              size="sm"
                              color="red"
                              onClick={() => handleReject(user?.id as number)}
                            >
                              <XMarkIcon width={16} height={16} />
                            </ActionIcon>
                            <ActionIcon
                              size="sm"
                              color="green"
                              onClick={() => handleConnect(user?.id as number)}
                            >
                              <CheckIcon width={16} height={16} />
                            </ActionIcon>
                          </>
                        )}
                      </Group>

                      {/* <ActionIcon
                                size={32}
                                color="gray"
                                onClick={() => {
                                  handleDelete(notification.id);
                                }}
                                loading={deletedNotifications.includes(
                                  notification.id
                                )}
                                loaderProps={{ size: 14 }}
                              >
                                <TrashIcon width={14} height={14} />
                              </ActionIcon> */}
                    </Group>
                  </Paper>
                );
              }
            )}
          </Stack>
        )}

        {notifications.filter((e) => e.target === "messages").length > 0 && (
          <Stack spacing={8}>
            <Text size="xs" weight={600}>
              Messages
            </Text>

            {notifications
              .filter((e) => e.target === "messages")
              .map((notification: NotificationType, i: number) => {
                return (
                  <Notification
                    dark
                    spacing="md"
                    key={`notification-${notification.id}`}
                    notification={notification}
                  />
                );
              })}
          </Stack>
        )}

        {notifications.filter((e) => e.target === "tasks").length > 0 && (
          <Stack spacing={8}>
            <Text size="xs" weight={600}>
              Tasks
            </Text>

            {notifications
              .filter((e) => e.target === "tasks")
              .map((notification: NotificationType, i: number) => {
                return (
                  <Notification
                    dark
                    spacing="md"
                    key={`notification-${notification.id}`}
                    notification={notification}
                  />
                );
              })}
          </Stack>
        )}
      </Stack>
    </Container>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default NotificationCenter;
