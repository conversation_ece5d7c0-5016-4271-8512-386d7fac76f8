import { getTranslations } from "~utils/getTranslations";
import { Renderers } from "~types";
import Masonry from "react-masonry-css";

import { Box, SimpleGrid } from "@mantine/core";
import { useElementSize, useForceUpdate } from "@mantine/hooks";

import TasksTable from "~components/Dashboard/CRM/TasksTable";
import TasksTimeline from "~components/Dashboard/CRM/TasksTimeline";
import ProjectTimeline from "~components/Dashboard/Analytics/ProjectTimeline";
import PaymentHistory from "~components/Dashboard/Analytics/PaymentHistory";
import MeetingSchedule from "~components/Dashboard/Analytics/MeetingSchedule";
import { useEffect } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import WeekslyOverview from "~components/Dashboard/Analytics/WeekslyOverview";
import VisitsByDay from "~components/Dashboard/Analytics/VisitsByDay";
import TotalImpressions from "~components/Dashboard/Analytics/TotalImpressions";
import { showNotification } from "@mantine/notifications";
import { useTranslation } from "next-i18next";
import OrganicSessions from "~components/Dashboard/Analytics/OrganicSessions";

const CRM = function () {
  const { isNavbarMinimized, isNavbarHover, selectedProjectId } = useStore(
    "global",
    (state) => ({
      isNavbarMinimized: state.isNavbarMinimized!,
      isNavbarHover: state.isNavbarHover!,
      selectedProjectId: state.selectedProjectId,
    }),
    shallow
  );

  const { setIsLoading, setLoadingLevel, loadingLevel, isLoading } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
    }),
    shallow
  );

  const { actionGetCrmReport } = useStore(
    "analytics",
    (state) => ({
      actionGetCrmReport: state.actionGetCrmReport!,
    }),
    shallow
  );

  const { width, ref } = useElementSize();
  const forceUpdate = useForceUpdate();
  const { t } = useTranslation();

  useEffect(() => {
    forceUpdate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNavbarHover, isNavbarMinimized]);

  useEffect(() => {
    let aborted = false;

    (async () => {
      if (!aborted) {
        if (!selectedProjectId) {
          showNotification({
            color: "red",
            title: t("task.project.fail.title"),
            message: t("task.project.fail.message"),
            autoClose: 3000,
          });
        } else {
          setLoadingLevel(0);
          setIsLoading(true);

          await actionGetCrmReport();
          setIsLoading(false);
        }
      }
    })();

    return () => {
      aborted = true;
    };

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProjectId]);

  return (
    <Box
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <Masonry
        breakpointCols={{
          default: 2,
          1100: 1,
        }}
        className="my-masonry-grid"
        columnClassName="my-masonry-grid_column"
      >
        <TasksTimeline />
        <TasksTable />

        {/* <ProjectTimeline seperate={width <= 1280} /> */}

        <SimpleGrid cols={width > 1280 ? 2 : 1} spacing={30}>
          <WeekslyOverview />
          {width > 1280 && <OrganicSessions />}
        </SimpleGrid>

        {width <= 1280 && <OrganicSessions />}

        <SimpleGrid cols={width > 1280 ? 3 : width > 400 ? 2 : 1}>
          <TotalImpressions />
        </SimpleGrid>

        {/* <SimpleGrid cols={width > 1500 ? 2 : 1} spacing={30}>
          <MeetingSchedule />
          {width > 1500 && <PaymentHistory />}
        </SimpleGrid>
        {width <= 1500 && <PaymentHistory />} */}
      </Masonry>
    </Box>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default CRM;
