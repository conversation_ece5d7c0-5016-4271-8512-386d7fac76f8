import { getTranslations } from "~utils/getTranslations";
import { Renderers } from "~types";
import Masonry from "react-masonry-css";

import { Box, SimpleGrid } from "@mantine/core";

import ExternalLinks from "~components/Dashboard/Analytics/ExternalLinks";
import MeetingSchedule from "~components/Dashboard/Analytics/MeetingSchedule";
import OrganicSessions from "~components/Dashboard/Analytics/OrganicSessions";
import PaymentHistory from "~components/Dashboard/Analytics/PaymentHistory";
import ProjectTimeline from "~components/Dashboard/Analytics/ProjectTimeline";
import SocialNetworkVisits from "~components/Dashboard/Analytics/SocialNetworkVisits";
import TotalGrowth from "~components/Dashboard/Analytics/TotalGrowth";
import TotalImpressions from "~components/Dashboard/Analytics/TotalImpressions";
import TotalOrders from "~components/Dashboard/Analytics/TotalOrders";
import TotalProfit from "~components/Dashboard/Analytics/TotalProfit";
import TotalRevenue from "~components/Dashboard/Analytics/TotalRevenue";
import TotalSales from "~components/Dashboard/Analytics/TotalSales";
import VisitsByDay from "~components/Dashboard/Analytics/VisitsByDay";
import WeekslyOverview from "~components/Dashboard/Analytics/WeekslyOverview";

import { useElementSize, useForceUpdate } from "@mantine/hooks";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useEffect } from "react";

const Analytcis = function () {
  const { isNavbarMinimized, isNavbarHover } = useStore(
    "global",
    (state) => ({
      isNavbarMinimized: state.isNavbarMinimized!,
      isNavbarHover: state.isNavbarHover!,
    }),
    shallow
  );

  const { width, ref } = useElementSize();
  const forceUpdate = useForceUpdate();

  useEffect(() => {
    forceUpdate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNavbarHover, isNavbarMinimized]);

  return (
    <Box
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <Masonry
        breakpointCols={{
          default: 2,
          1100: 1,
        }}
        className="my-masonry-grid"
        columnClassName="my-masonry-grid_column"
      >
        <SimpleGrid cols={width > 1280 ? 3 : width > 400 ? 2 : 1} spacing={30}>
          <TotalOrders />
          <TotalSales />
          <TotalImpressions />
          {width <= 1280 && (
            <>
              <TotalProfit />
              <TotalGrowth />
              <TotalRevenue />
            </>
          )}
        </SimpleGrid>

        {width > 1280 && (
          <SimpleGrid cols={3} spacing={30}>
            <TotalProfit />
            <TotalGrowth />
            <TotalRevenue />
          </SimpleGrid>
        )}

        <SimpleGrid cols={width > 1280 ? 2 : 1} spacing={30}>
          <SocialNetworkVisits />
          <ExternalLinks />
        </SimpleGrid>

        <SimpleGrid cols={width > 1280 ? 2 : 1} spacing={30}>
          <OrganicSessions />
          <Box />
        </SimpleGrid>
      </Masonry>
    </Box>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Analytcis;
