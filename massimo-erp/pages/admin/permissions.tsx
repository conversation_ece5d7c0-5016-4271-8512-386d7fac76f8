import React, { useCallback } from "react";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import { Stack, Accordion, Box, Group, Text, Button } from "@mantine/core";
import { useElementSize } from "@mantine/hooks";

import "react-awesome-query-builder/lib/css/styles.css";
import "react-awesome-query-builder/lib/css/compact_styles.css";

import QueryBuilder from "~components/Admin/Permissions/QueryBuilder";
import AccessContent from "~components/Admin/Permissions/AccessContent";
import { useTranslation } from "next-i18next";
import { isEqual } from "lodash";

export default function Permissions() {
  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  const { tempAccesses, setAccesses } = useStore(
    "temp",
    (state) => ({
      tempAccesses: state.accesses!,
      setAccesses: state.setAccesses!,
    }),
    shallow
  );

  const { accesses, saveAccesses } = useStore(
    "access",
    (state) => ({
      accesses: state.accesses!,
      saveAccesses: state.saveAccesses!,
    }),
    shallow
  );

  const save = useCallback(() => {
    saveAccesses(tempAccesses);
  }, [tempAccesses, saveAccesses]);

  return (
    <Stack
      ref={ref}
      sx={{
        width: "100%",
        opacity: width === 0 ? 0 : 1,
        transition: "opacity .2s ease",
      }}
    >
      <Accordion variant="separated" defaultValue={`${accesses[0].name}`}>
        {tempAccesses.map((item: any, i: number) => (
          <Accordion.Item key={"access-" + i} value={`${item.name}`}>
            <Accordion.Control>
              <Group>
                <Text>{item.name}</Text>
                <Text size="xs" weight={600} color="dimmed">
                  {item.path}
                </Text>
                <Text
                  size="sm"
                  weight={600}
                  transform="uppercase"
                  color={item.type === "edit" ? "blue" : "lime"}
                >
                  {item.type}
                </Text>
              </Group>
            </Accordion.Control>
            <Accordion.Panel>
              <AccessContent data={item} />
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Accordion>

      {!isEqual(tempAccesses, accesses) && (
        <Box sx={{ position: "fixed", bottom: 0, right: 0 }} p="xl">
          <Button size="md" onClick={() => save()}>
            {t("save")}
          </Button>
        </Box>
      )}

      {/* <Space /> */}
      {/* <div className="query-builder-result">
        <div>
          Query string:{" "}
          <pre>
            {JSON.stringify(QbUtils.queryString(state.tree, state.config))}
          </pre>
        </div>
        <div>
          MongoDb query:{" "}
          <pre>
            {JSON.stringify(QbUtils.mongodbFormat(state.tree, state.config))}
          </pre>
        </div>
        <div>
          SQL where:{" "}
          <pre>
            {JSON.stringify(QbUtils.sqlFormat(state.tree, state.config))}
          </pre>
        </div>
        <div>
          JsonLogic:{" "}
          <pre>
            {JSON.stringify(QbUtils.jsonLogicFormat(state.tree, state.config))}
          </pre>
        </div>
      </div> */}
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
