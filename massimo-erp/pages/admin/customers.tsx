import {
  Cog6ToothIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  PencilSquareIcon,
  PlusSmallIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";
import {
  Table,
  Paper,
  Stack,
  Anchor,
  Group,
  Button,
  ActionIcon,
  Grid,
  Title,
  Text,
  List,
  ThemeIcon,
  Avatar,
  Tooltip,
  Input,
  Pagination,
  Select,
  ScrollArea,
  Indicator,
} from "@mantine/core";
import {
  getHotkeyHandler,
  useElementSize,
  useMediaQuery,
} from "@mantine/hooks";
import { Fragment, useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";
import { formatDate, formatTime } from "~utils/tools";
import CustomerModal from "~components/Admin/Customer/CustomerModal";
import { useTranslation } from "next-i18next";
import ActiveAvatar from "~components/ActiveAvatar";
import { orderBy } from "lodash";

export default function CustomerManagement() {
  const { customers, filteredCustomers, invites, actionGetCustomers } =
    useStore(
      "customers",
      (state) => ({
        customers: state.customers!,
        filteredCustomers: state.filteredCustomers!,
        invites: state.invites!,
        actionGetCustomers: state.actionGetCustomers!,
      }),
      shallow
    );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );

  const {
    setCustomerModal,
    setProjectModal,
    blankCustomer,
    setIsLoading,
    setLoadingLevel,
    setIsOpenUnverifiedUsersModal,
  } = useStore(
    "temp",
    (state) => ({
      setCustomerModal: state.setCustomerModal!,
      setProjectModal: state.setProjectModal!,
      blankCustomer: state.computed!.blankCustomer!,
      setIsOpenUnverifiedUsersModal: state.setIsOpenUnverifiedUsersModal!,
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 1300px)");
  const { ref, width } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);
  const [isOpenSearchBar, setIsOpenSearchBar] = useState(false);

  const [selectedID, setSelectedID] = useState<number>((customers[0] || {}).id);
  const [activeCustomer, setActiveCustomer] = useState(
    customers.filter((e) => e.id === selectedID)[0]
  );

  useEffect(() => {
    if (!selectedID && customers[0]) {
      setSelectedID(customers[0].id);
    }
    setActiveCustomer(customers.filter((e) => e.id === selectedID)[0]);
  }, [customers, selectedID]);

  const addCustomer = useCallback(() => {
    setCustomerModal({
      isOpen: true,
      type: "new",
      data: blankCustomer,
    });
  }, [setCustomerModal, blankCustomer]);

  const openEditCustomer = useCallback(() => {
    setCustomerModal({
      isOpen: true,
      type: "edit",
      data: activeCustomer,
    });
  }, [activeCustomer, setCustomerModal]);

  const rows = orderBy(filteredCustomers, "id").map((customer) => (
    <tr
      key={`customer-${customer.id}`}
      className={selectedID === customer.id ? "active" : ""}
      onClick={() => setSelectedID(customer.id)}
    >
      <td>
        <Group>
          <Tooltip label={`${customer.fullName}`}>
            <Anchor
              onClick={() =>
                setCustomerModal({
                  isOpen: true,
                  type: "edit",
                  data: customer,
                })
              }
              sx={{
                maxWidth: 200,
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {customer.fullName}
            </Anchor>
          </Tooltip>
        </Group>
      </td>
      <td>{customer.phone}</td>
      <td align="right">
        <Anchor href={`mailto:${customer.email}`}>{customer.email}</Anchor>
      </td>
      <td align="right">
        <Group position="right" spacing={8}>
          {customer.personIds?.length === 0 ? (
            <Text size="xs" weight={600} color="dimmed">
              {t("noOne")}
            </Text>
          ) : (
            <Avatar.Group>
              {customer.personIds
                ?.slice(0, 8)
                .map((personId: number, i: number) => {
                  return (
                    <ActiveAvatar
                      key={"personId-" + i}
                      userId={personId}
                      size="sm"
                      radius="xl"
                    />
                  );
                })}
              {customer.personIds.length > 8 && (
                <Avatar size="sm" radius="xl">
                  +{customer.personIds.length - 8}
                </Avatar>
              )}
            </Avatar.Group>
          )}
        </Group>
      </td>
    </tr>
  ));

  const [search, setSearch] = useState("");

  useEffect(() => {
    updateCustomers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  const updateCustomers = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetCustomers(+rowsPerPage, +activePage - 1, search);
    } finally {
      setSelectedID((filteredCustomers[0] || customers[0] || {}).id);
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    actionGetCustomers,
    rowsPerPage,
    activePage,
    search,
    filteredCustomers,
    customers,
  ]);

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <CustomerModal updateCustomers={updateCustomers} />
      <Group position={width < 500 ? "center" : "apart"}>
        <Group>
          <Button
            fullWidth={width < 500}
            variant="light"
            size="sm"
            leftIcon={<PlusSmallIcon style={{ width: 20, height: 20 }} />}
            onClick={() => addCustomer()}
          >
            {t("addCustomer")}
          </Button>
          <Group noWrap>
            <ActionIcon
              size="lg"
              variant="light"
              color="gray"
              sx={{ width: 36, height: 36 }}
              onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
            >
              <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
            <Input
              placeholder={t("search")}
              size={"sm"}
              sx={{
                width: width < 500 ? "100%" : isOpenSearchBar ? 200 : 0,
                opacity:
                  (isOpenSearchBar || width < 500) && width !== 0 ? 1 : 0,
                transition: "width .2s ease, opacity .25s ease",
              }}
              value={search}
              onChange={(e: any) => {
                setSearch(e.target.value);
              }}
              onKeyDown={getHotkeyHandler([["Enter", updateCustomers]])}
            />
          </Group>
        </Group>
      </Group>
      <Grid>
        <Grid.Col span={matches ? 12 : 7}>
          <Paper
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[8]
                  : theme.colors.gray[1],
              padding: theme.spacing.md,
              flexShrink: 0,
            })}
          >
            <Group
              position={width < 520 && width !== 0 ? "center" : "apart"}
              align="center"
              mb="md"
            >
              <Group position="center">
                <Text size="xs" weight={600} color="dimmed">
                  {t("rowPerPage")}
                </Text>
                <Select
                  size="xs"
                  value={rowsPerPage}
                  onChange={(value: string) => {
                    setRowsPerPage(value);
                    setActivePage(1);
                  }}
                  data={[
                    { value: "2", label: "2" },
                    { value: "10", label: "10" },
                    { value: "20", label: "20" },
                    { value: "50", label: "50" },
                  ]}
                  sx={{
                    width: 100,
                  }}
                  transition="fade"
                  transitionDuration={200}
                />
              </Group>
              <Pagination
                page={activePage}
                onChange={setActivePage}
                total={Math.ceil(metrics.customers / +rowsPerPage) || 1}
                size="sm"
              />
            </Group>
            <ScrollArea
              offsetScrollbars
              sx={{
                maxWidth: width > 700 ? "auto" : "calc(100vw - 64px)",
              }}
            >
              <Table
                verticalSpacing="xs"
                sx={(theme) => ({
                  minWidth: 800,
                  tr: {
                    ":hover:not(#table-header), &[class~='active']": {
                      background:
                        theme.colorScheme === "dark"
                          ? theme.colors.dark[7]
                          : theme.colors.gray[2],
                      cursor: "pointer",
                      transition: "background-color .1s ease",
                      marginTop: 8,
                    },
                  },
                  "td:first-of-type, th:first-of-type": {
                    borderRadius: `${theme.spacing.sm}px 0 0 ${theme.spacing.sm}px`,
                  },
                  "td:last-child, th:last-child": {
                    borderRadius: `0 ${theme.spacing.sm}px ${theme.spacing.sm}px 0`,
                  },
                })}
              >
                <thead>
                  <tr id="table-header">
                    <th>{t("customerTable.Company")}</th>
                    <th>{t("customerTable.Phone")}</th>
                    <th style={{ textAlign: "right" }}>
                      {t("customerTable.EMail")}
                    </th>
                    <th style={{ textAlign: "right" }}>
                      {t("customerTable.Authorized")}
                    </th>
                  </tr>
                </thead>
                <tbody>{rows}</tbody>
              </Table>
            </ScrollArea>
          </Paper>
        </Grid.Col>
        <Grid.Col span={matches ? 12 : 5}>
          <Paper
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[8]
                  : theme.colors.gray[1],
              padding: theme.spacing.md,
            })}
          >
            <Stack>
              <Group noWrap position="apart">
                <Stack
                  spacing={0}
                  sx={{
                    width: "calc(100% - 180px)",
                    position: "relative",
                  }}
                >
                  <Text
                    size="md"
                    weight={600}
                    sx={{
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                  >
                    {activeCustomer?.fullName}
                  </Text>
                  <Text
                    size="xs"
                    weight={600}
                    color="dimmed"
                    sx={{
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                  >
                    {activeCustomer?.shortName}
                  </Text>
                </Stack>

                <Button
                  variant="light"
                  size="xs"
                  onClick={() => openEditCustomer()}
                  rightIcon={
                    <PencilSquareIcon style={{ width: 16, height: 16 }} />
                  }
                >
                  {t("customerModal.title.edit")}
                </Button>
              </Group>

              {(activeCustomer?.offers || []).length > 0 && (
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    {t("Offers")} ({(activeCustomer?.offers || []).length})
                  </Text>
                  <List withPadding>
                    {(activeCustomer?.offers || [])
                      .slice(0, 4)
                      .map((offer: number, i: number) => {
                        const activeOffer = offers.filter(
                          (e) => e.id === offer
                        )[0];

                        return (
                          <List.Item key={"offer-" + i}>
                            <Anchor
                              size="sm"
                              onClick={
                                () => {}
                                // setProjectModal({
                                //   isOpen: true,
                                //   type: "edit",
                                //   data: activeOffer,
                                // })
                              }
                            >
                              {activeOffer.name}
                            </Anchor>
                          </List.Item>
                        );
                      })}
                  </List>
                </Stack>
              )}

              {(activeCustomer?.tasks || []).length > 0 && (
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    {t("Tasks")} ({(activeCustomer?.tasks || []).length})
                  </Text>
                  <List withPadding>
                    {(activeCustomer?.tasks || []).map(
                      (task: any, i: number) => (
                        <List.Item key={"task-" + i}>
                          <Anchor size="sm">{task.title}</Anchor>
                        </List.Item>
                      )
                    )}
                  </List>
                </Stack>
              )}

              <Stack spacing="sm">
                <Text size="sm" weight={600}>
                  {t("RecentActivities")}
                </Text>
                {(activeCustomer?.recentActivities || []).length > 0 && (
                  <List>
                    {(activeCustomer?.recentActivities || []).map(
                      (activity: any, i: number) => (
                        <List.Item
                          key={"activity-" + i}
                          mb={
                            i <
                            (activeCustomer?.recentActivities || []).length - 1
                              ? "md"
                              : 0
                          }
                          icon={
                            <ThemeIcon color="blue" variant="light" size="lg">
                              <DocumentTextIcon
                                style={{ width: 20, height: 20 }}
                              />
                            </ThemeIcon>
                          }
                        >
                          <Stack spacing={4}>
                            <Group spacing={8}>
                              <Text size="xs" weight={600}>
                                {activity.owner.name} {activity.owner.surname}
                              </Text>
                              <Text size="xs" weight={600} color="dimmed">
                                {formatDate(activity.date)}
                                {", "}
                                {formatTime(activity.date)}
                              </Text>
                            </Group>
                            <Text size="xs" color="dimmed">
                              {activity.text}
                              {/* Changed the status of task{" "}
                          <Anchor>Example Task</Anchor> to archived */}
                            </Text>
                          </Stack>
                        </List.Item>
                      )
                    )}
                  </List>
                )}
                {(activeCustomer?.recentActivities || []).length === 0 && (
                  <Text size="xs" weight={600} color="dimmed">
                    {t("noActivityYet")}
                  </Text>
                )}
              </Stack>
            </Stack>
          </Paper>
        </Grid.Col>
      </Grid>
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
