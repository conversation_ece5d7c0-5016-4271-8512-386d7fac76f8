import {
  MagnifyingGlassIcon,
  PlusSmallIcon,
} from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Group,
  Input,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Select,
  Pagination,
  Button,
} from "@mantine/core";
import { getHotkeyHandler, useElementSize } from "@mantine/hooks";
import { orderBy, some } from "lodash";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useState } from "react";
import Masonry from "react-masonry-css";
import shallow from "zustand/shallow";
import RequestCard from "~components/Admin/Requests/RequestCard";
import RequestViewModal from "~components/Modals/RequestViewModal";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";
import { CustomerType } from "~utils/types/Customer";
import { OfferType, RequestType } from "~utils/types/Project";

const breakpointColumnsObj = {
  default: 2,
  1600: 2,
  1200: 1,
};

export default function Requests() {
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { filteredRequests, actionGetRequests } = useStore(
    "requests",
    (state) => ({
      filteredRequests: state.filteredRequests!,
      actionGetRequests: state.actionGetRequests!,
    }),
    shallow
  );
  const { setSendRequestModal } = useStore(
    "temp",
    (state) => ({
      setSendRequestModal: state.setSendRequestModal!,
    }),
    shallow
  );
  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );

  const { setIsLoading, setLoadingLevel } = useStore("temp", (state) => ({
    setIsLoading: state.setIsLoading!,
    setLoadingLevel: state.setLoadingLevel!,
  }));

  const { ref, width } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);
  const [isOpenSearchBar, setIsOpenSearchBar] = useState(false);
  const { t } = useTranslation();
  const [search, setSearch] = useState("");

  useEffect(() => {
    updateRequests();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  const addProject = useCallback(() => {
    setSendRequestModal({
      isOpen: true,
      isCustomer: undefined,
    });
  }, [setSendRequestModal]);

  const updateRequests = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetRequests(+rowsPerPage, +activePage - 1, search);
    } finally {
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    actionGetRequests,
    rowsPerPage,
    activePage,
    search,
  ]);

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <RequestViewModal updateRequests={updateRequests} />

      <Group position={width <= 525 && width > 0 ? "center" : "apart"}>
        <Group position="center" sx={{ width: width <= 525 ? "100%" : "auto" }}>
          <Button
            fullWidth={width < 400}
            variant="light"
            size="sm"
            leftIcon={<PlusSmallIcon style={{ width: 20, height: 20 }} />}
            onClick={() => addProject()}
          >
            {t("profile.sendRequest")}
          </Button>
          <Group noWrap>
            <ActionIcon
              size="lg"
              variant="light"
              color="gray"
              sx={{ width: 36, height: 36 }}
              onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
            >
              <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
            <Input
              placeholder={t("search")}
              size={"sm"}
              sx={{
                width: width < 500 ? "100%" : isOpenSearchBar ? 200 : 0,
                opacity:
                  (isOpenSearchBar || width < 500) && width !== 0 ? 1 : 0,
                transition: "width .2s ease, opacity .25s ease",
              }}
              value={search}
              onChange={(e: any) => {
                setSearch(e.target.value);
              }}
              onKeyDown={getHotkeyHandler([["Enter", updateRequests]])}
            />
          </Group>
        </Group>

        <Group
          position={width < 520 && width !== 0 ? "center" : "apart"}
          align="center"
        >
          <Group>
            <Text size="xs" weight={600} color="dimmed">
              {t("rowPerPage")}
            </Text>
            <Select
              size="xs"
              value={rowsPerPage}
              onChange={(value: string) => setRowsPerPage(value)}
              data={[
                { value: "2", label: "2" },
                { value: "10", label: "10" },
                { value: "20", label: "20" },
                { value: "50", label: "50" },
              ]}
              sx={{
                width: 100,
              }}
              transition="fade"
              transitionDuration={200}
            />
          </Group>
          <Group position="center">
            <Pagination
              page={activePage}
              onChange={setActivePage}
              total={Math.ceil(metrics.projects / +rowsPerPage) || 1}
              size="sm"
            />
          </Group>
        </Group>
      </Group>

      <Masonry
        breakpointCols={breakpointColumnsObj}
        className="my-masonry-grid"
        columnClassName="my-masonry-grid_column"
      >
        {orderBy(customers, "id")
          .filter((e) => e.requestIds?.length > 0)
          .map((customer: CustomerType, i: number) => {
            const activeRequests = customer.requestIds
              .map((requestId) =>
                filteredRequests.find((request) => request.id === requestId)
              )
              .filter((x) => !!x && !x.projectId);

            if (activeRequests.length === 0) {
              return null;
            }

            return (
              <Paper
                key={"request-" + i + "-" + search}
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[8]
                      : theme.colors.gray[2],
                  padding: theme.spacing.md,
                })}
              >
                <Stack>
                  <Group>
                    <Stack spacing={0}>
                      <Text size="sm" weight={600}>
                        {customer.fullName}
                      </Text>
                      <Text size="xs" weight={600} color="dimmed">
                        {customer.shortName}
                      </Text>
                    </Stack>
                  </Group>
                  <SimpleGrid cols={width > 1000 ? 2 : 1}>
                    {orderBy(activeRequests, "id").map(
                      (request: RequestType | undefined, i: number) => {
                        if (!request) {
                          return null;
                        }

                        return (
                          <RequestCard key={"request-" + i} request={request} />
                        );
                      }
                    )}
                  </SimpleGrid>
                </Stack>
              </Paper>
            );
          })}
      </Masonry>
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
