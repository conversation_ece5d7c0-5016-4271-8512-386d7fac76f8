import { useCallback, useEffect, useState } from "react";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  MagnifyingGlassIcon,
  PlusSmallIcon,
  UserPlusIcon,
} from "@heroicons/react/24/outline";
import {
  Table,
  Paper,
  Stack,
  Group,
  Button,
  ActionIcon,
  Input,
  Text,
  Pagination,
  Space,
  Select,
  ScrollArea,
  Indicator,
  Divider,
} from "@mantine/core";
import {
  getHotkeyHandler,
  useElementSize,
  useMediaQuery,
} from "@mantine/hooks";
import UsersTableItem from "~components/Admin/Users/<USER>";
import UsersTableFilter from "~components/Admin/Users/<USER>";
import UserModal from "~components/Admin/Users/<USER>";
import UserInformations from "~components/Admin/Users/<USER>";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import UnverifiedUsers from "~components/Modals/UnverifiedUsers";
import { orderBy } from "lodash";

const Users = function () {
  const { isNavbarHover, isNavbarMinimized } = useStore(
    "global",
    (state) => ({
      isNavbarHover: state.isNavbarHover!,
      isNavbarMinimized: state.isNavbarMinimized!,
    }),
    shallow
  );
  const {
    userPrev,
    users,
    filteredUnverifiedUsers,
    filter,
    filteredUsers,
    actionGetUsers,
  } = useStore(
    "users",
    (state) => ({
      userPrev: state.userPrev!,
      users: state.users!,
      filteredUnverifiedUsers: state.filteredUnverifiedUsers!,
      filter: state.filter!,
      filteredUsers: state.filteredUsers!,
      actionGetUsers: state.actionGetUsers!,
    }),
    shallow
  );
  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );
  const {
    setIsOpenUnverifiedUsersModal,
    setUserModal,
    blankUser,
    setIsLoading,
    setLoadingLevel,
  } = useStore(
    "temp",
    (state) => ({
      setIsOpenUnverifiedUsersModal: state.setIsOpenUnverifiedUsersModal!,
      setUserModal: state.setUserModal!,
      blankUser: state.computed!.blankUser!,
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(max-width: 992px)");

  const [isOpenSearchBar, setIsOpenSearchBar] = useState(false);
  const [search, setSearch] = useState("");

  const [prev, setPrev] = useState(0);

  const addUser = useCallback(() => {
    setUserModal({
      isOpen: true,
      type: "new",
      data: {
        ...blankUser,
        id: undefined as any,
      },
    });
  }, [setUserModal, blankUser]);

  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);

  useEffect(() => {
    updateUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  useEffect(() => {
    if (prev !== userPrev) {
      setPrev(userPrev);

      setTimeout(() => {
        updateUsers();
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prev, userPrev]);

  const updateUsers = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetUsers(+rowsPerPage, +activePage - 1, search);
    } finally {
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    actionGetUsers,
    rowsPerPage,
    activePage,
    search,
  ]);

  return (
    <Stack
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <UserModal />
      <UserInformations />
      <UnverifiedUsers />

      <Group
        ref={ref}
        position={width <= 600 && width > 0 ? "center" : "apart"}
      >
        <Group sx={{ width: width <= 600 ? "100%" : "auto" }}>
          <Button
            fullWidth={width <= 600}
            variant="light"
            size="sm"
            leftIcon={<PlusSmallIcon style={{ width: 20, height: 20 }} />}
            onClick={() => addUser()}
          >
            {t("addUser")}
          </Button>
          {width > 600 ? (
            <Group>
              <ActionIcon
                size="lg"
                variant="light"
                color="gray"
                sx={{ width: 36, height: 36 }}
                onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
              >
                <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
              </ActionIcon>
              <Input
                placeholder={t("search")}
                size={"sm"}
                sx={{
                  width: isOpenSearchBar ? 200 : 0,
                  opacity: isOpenSearchBar ? 1 : 0,
                  transition: "width .2s ease, opacity .25s ease",
                }}
                value={search}
                onChange={(e: any) => {
                  setSearch(e.target.value);
                }}
                onKeyDown={getHotkeyHandler([["Enter", updateUsers]])}
              />
            </Group>
          ) : (
            <Indicator
              sx={{ width: width < 500 ? "100%" : "auto" }}
              label={filteredUnverifiedUsers.length}
              overflowCount={10}
              inline
              size={20}
            >
              <Button
                fullWidth={width < 500}
                variant="light"
                size="sm"
                leftIcon={<UserPlusIcon style={{ width: 20, height: 20 }} />}
                onClick={() => setIsOpenUnverifiedUsersModal(true)}
              >
                {t("customerInvites")}
              </Button>
            </Indicator>
          )}
        </Group>
        {width > 600 ? (
          <Indicator
            sx={{ width: width < 500 ? "100%" : "auto" }}
            label={filteredUnverifiedUsers.length}
            overflowCount={10}
            inline
            size={20}
          >
            <Button
              fullWidth={width < 500}
              variant="light"
              size="sm"
              leftIcon={<UserPlusIcon style={{ width: 20, height: 20 }} />}
              onClick={() => setIsOpenUnverifiedUsersModal(true)}
            >
              {t("customerInvites")}
            </Button>
          </Indicator>
        ) : (
          <Group noWrap sx={{ width: width <= 600 ? "100%" : "auto" }}>
            <ActionIcon
              size="lg"
              variant="light"
              color="gray"
              sx={{ width: 36, height: 36 }}
              onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
            >
              <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
            <Input
              placeholder={t("search")}
              size={"sm"}
              sx={{
                width: width <= 600 ? "100%" : isOpenSearchBar ? 200 : 0,
                opacity: isOpenSearchBar || width < 600 ? 1 : 0,
                transition: "width .2s ease, opacity .25s ease",
              }}
              value={search}
              onChange={(e: any) => {
                setSearch(e.target.value);
              }}
              onKeyDown={getHotkeyHandler([["Enter", updateUsers]])}
            />
          </Group>
        )}
      </Group>

      <ScrollArea
        styles={{
          root: {
            overflowY: "visible",
            overflow: "unset",
            maxWidth:
              width >= 1390
                ? "auto"
                : `calc(100vw - ${
                    matches
                      ? "32px"
                      : isNavbarHover || !isNavbarMinimized
                      ? "364px"
                      : "142px"
                  })`,
          },
        }}
      >
        <Paper
          sx={(theme) => ({
            width: "100%",
            minWidth: 1400,
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[8]
                : theme.colors.gray[1],
            padding: theme.spacing.md,
            flexShrink: 0,
          })}
        >
          <Group position="apart" align="center">
            <Group>
              <Text size="xs" weight={600} color="dimmed">
                {t("rowPerPage")}
              </Text>
              <Select
                size="xs"
                value={rowsPerPage}
                onChange={(value: string) => {
                  setRowsPerPage(value);
                  setActivePage(1);
                }}
                data={[
                  { value: "2", label: "2" },
                  { value: "10", label: "10" },
                  { value: "25", label: "25" },
                  { value: "50", label: "50" },
                ]}
                sx={{
                  width: 100,
                }}
                transition="fade"
                transitionDuration={200}
              />
            </Group>
            <Pagination
              page={activePage}
              onChange={setActivePage}
              total={Math.ceil(metrics.users / +rowsPerPage) || 1}
              size="sm"
            />
          </Group>

          <Space h="md" />

          <Table
            verticalSpacing="xs"
            sx={(theme) => ({
              tr: {
                ":hover:not(#table-header), &[class~='active']": {
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[7]
                      : theme.colors.gray[2],
                  transition: "background-color .1s ease",
                  marginTop: 8,
                },
              },
              "td:first-of-type, th:first-of-type": {
                borderRadius: `${theme.spacing.sm}px 0 0 ${theme.spacing.sm}px`,
              },
              "td:last-child, th:last-child": {
                borderRadius: `0 ${theme.spacing.sm}px ${theme.spacing.sm}px 0`,
              },
            })}
          >
            <thead>
              <tr id="table-header">
                <th>{t("userTable.User")}</th>
                <th>{t("userTable.Phone")}</th>
                <th>{t("userTable.EMail")}</th>
                <th>{t("userTable.Departments")}</th>
                <th>{t("userTable.RestrictedRoles")}</th>
                <th>{t("userTable.DefaultRole")}</th>
                {/* <th>{t("userTable.Plan")}</th>
          <th>{t("userTable.Status")}</th> */}
                <th style={{ textAlign: "right" }}></th>
              </tr>
            </thead>
            <tbody>
              {orderBy(
                search !== "" || filteredUsers.length > 0 ? filteredUsers : [],
                "id"
              ).map((user: UserType, i: number) => {
                return <UsersTableItem key={`user-${i}`} user={user} />;
              })}
            </tbody>
          </Table>

          {users.length === 0 && (
            <Text
              size="md"
              weight={600}
              color="dimmed"
              align="center"
              mt="md"
              sx={{ width: "100%" }}
            >
              {t("noData")}
            </Text>
          )}
        </Paper>
      </ScrollArea>
    </Stack>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Users;
