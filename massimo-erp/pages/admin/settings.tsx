import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import {
  Button,
  Divider,
  Grid,
  Group,
  Input,
  Paper,
  Select,
  SimpleGrid,
  Stack,
  Switch,
  Text,
  Title,
} from "@mantine/core";
import {
  AdjustmentsHorizontalIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";

export default function Settings() {
  const { ref, width } = useElementSize();

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity 2s ease" }}
      spacing={32}
    >
      <Group position="apart" align="flex-start">
        <Stack spacing={8}>
          <Title order={2}>Settings</Title>
          <Text size="sm" weight={600} color="dimmed">
            Select to kind of notifications you get about your activities and
            recommendations
          </Text>
        </Stack>
        <Input
          sx={{
            width: width <= 500 ? "100%" : "auto",
          }}
          size="xs"
          icon={<MagnifyingGlassIcon style={{ width: 20, height: 20 }} />}
          placeholder="Search"
        />
      </Group>
      <Divider />
      <Grid gutter={32}>
        <Grid.Col span={width > 1000 ? 4 : 12}>
          <Stack spacing={4}>
            <Text size="sm" weight={600}>
              Email Notifications
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              Lorem ipsum dolor sit amet consectetur adipisicing elit.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={width > 1000 ? 8 : 12}>
          <Stack spacing="lg">
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    News and updates
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    News about products and feature updates
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    Tips and tutorials
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    Tips on getting more out of Untitled
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    Comments
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    Comments on your posts and replies to comments
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    Reminders
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    These are notifications the remind you of updated you might
                    have missed
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
          </Stack>
        </Grid.Col>
      </Grid>

      <Divider />
      <Grid gutter={32}>
        <Grid.Col span={width > 1000 ? 4 : 12}>
          <Stack spacing={4}>
            <Text size="sm" weight={600}>
              Push Notifications
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              Lorem ipsum dolor sit amet consectetur adipisicing elit.
            </Text>
          </Stack>
        </Grid.Col>
        <Grid.Col span={width > 1000 ? 8 : 12}>
          <Stack spacing="lg">
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    Comments
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    Comments on your posts and replies to comments
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
            <Group position="apart">
              <Group align="flex-start" noWrap>
                <Switch />
                <Stack spacing={4}>
                  <Text size="sm" weight={600}>
                    Reminders
                  </Text>
                  <Text size="xs" weight={600} color="dimmed">
                    These are notifications the remind you of updated you might
                    have missed
                  </Text>
                </Stack>
              </Group>
              <Select
                sx={{
                  width: width <= 500 ? "100%" : "auto",
                }}
                size="xs"
                value="Suggested"
                data={["Suggested"]}
              />
            </Group>
          </Stack>
        </Grid.Col>
      </Grid>

      <Paper withBorder p="md">
        <Group position="apart">
          <Group>
            <AdjustmentsHorizontalIcon style={{ width: 24, height: 24 }} />
            <Stack spacing={0}>
              <Text size="sm" weight={600}>
                Advanced Filters
              </Text>
              <Text size="xs" weight={600} color="dimmed">
                Lorem ipsum, dolor sit amet consectetur adipisicing elit. Magni
                assumenda expedita nam sint
              </Text>
            </Stack>
          </Group>

          <Button size="xs" variant="outline" color="gray">
            Set up Filters
          </Button>
        </Group>
      </Paper>
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
