import {
  MagnifyingGlassIcon,
  PlusSmallIcon,
} from "@heroicons/react/24/outline";
import {
  Group,
  Stack,
  ActionIcon,
  Button,
  Input,
  Pagination,
  Select,
  Text,
  Divider,
} from "@mantine/core";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";
import Masonry from "react-masonry-css";

import ProjectCard from "~components/Admin/Projects/ProjectCard";
import { getHotkeyHandler, useElementSize } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { orderBy } from "lodash";

const breakpointColumnsObj = {
  default: 4,
  1600: 3,
  1200: 2,
  700: 1,
};

export default function Projects() {
  const { setIsLoading, setLoadingLevel } = useStore("temp", (state) => ({
    setIsLoading: state.setIsLoading!,
    setLoadingLevel: state.setLoadingLevel!,
  }));

  const { filteredProjects, projectPrev, actionGetProjects } = useStore(
    "projects",
    (state) => ({
      filteredProjects: state.filteredProjects!,
      projectPrev: state.projectPrev!,
      actionGetProjects: state.actionGetProjects!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);
  const [isOpenSearchBar, setIsOpenSearchBar] = useState(false);

  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );

  const [search, setSearch] = useState("");
  const [prev, setPrev] = useState(0);

  const activeProjects = filteredProjects.filter(
    (e) =>
      offers.find((o) => e.offerIds.at(-1) === o.id)?.status !== "Completed"
  );
  const completedProjects = filteredProjects.filter(
    (e) =>
      offers.find((o) => e.offerIds.at(-1) === o.id)?.status === "Completed"
  );

  const updateProjects = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetProjects(+rowsPerPage, +activePage - 1, search);
    } finally {
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    actionGetProjects,
    rowsPerPage,
    search,
    activePage,
  ]);

  useEffect(() => {
    updateProjects();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  useEffect(() => {
    if (prev !== projectPrev) {
      setPrev(projectPrev);

      setTimeout(() => {
        updateProjects();
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [prev, projectPrev]);

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <Group position={width <= 525 && width > 0 ? "center" : "apart"}>
        <Group position="center" sx={{ width: width <= 525 ? "100%" : "auto" }}>
          <Group noWrap>
            <ActionIcon
              size="lg"
              variant="light"
              color="gray"
              sx={{ width: 36, height: 36 }}
              onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
            >
              <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
            <Input
              placeholder={t("search")}
              size={"sm"}
              sx={{
                width: width < 500 ? "100%" : isOpenSearchBar ? 200 : 0,
                opacity:
                  (isOpenSearchBar || width < 500) && width !== 0 ? 1 : 0,
                transition: "width .2s ease, opacity .25s ease",
              }}
              value={search}
              onChange={(e: any) => {
                setSearch(e.target.value);
              }}
              onKeyDown={getHotkeyHandler([["Enter", updateProjects]])}
            />
          </Group>
        </Group>

        <Group
          position={width < 520 && width !== 0 ? "center" : "apart"}
          align="center"
        >
          <Group>
            <Text size="xs" weight={600} color="dimmed">
              {t("rowPerPage")}
            </Text>
            <Select
              size="xs"
              value={rowsPerPage}
              onChange={(value: string) => setRowsPerPage(value)}
              data={[
                { value: "2", label: "2" },
                { value: "10", label: "10" },
                { value: "20", label: "20" },
                { value: "50", label: "50" },
              ]}
              sx={{
                width: 100,
              }}
              transition="fade"
              transitionDuration={200}
            />
          </Group>
          <Group position="center">
            <Pagination
              page={activePage}
              onChange={setActivePage}
              total={Math.ceil(metrics.projects / +rowsPerPage) || 1}
              size="sm"
            />
          </Group>
        </Group>
      </Group>

      {activeProjects.length > 0 && (
        <>
          <Divider label={t("activeProjects")} labelPosition="left" />
          <Masonry
            breakpointCols={breakpointColumnsObj}
            className="my-masonry-grid"
            columnClassName="my-masonry-grid_column"
          >
            {orderBy(activeProjects, "id").map((project: any, i: number) => (
              <ProjectCard key={"project-" + i} project={project} />
            ))}
          </Masonry>
        </>
      )}

      {completedProjects.length > 0 && (
        <>
          <Divider label={t("completedProjects")} labelPosition="left" />
          <Masonry
            breakpointCols={breakpointColumnsObj}
            className="my-masonry-grid"
            columnClassName="my-masonry-grid_column"
          >
            {orderBy(completedProjects, "id").map((project: any, i: number) => (
              <ProjectCard key={"project-" + i} project={project} />
            ))}
          </Masonry>
        </>
      )}
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
