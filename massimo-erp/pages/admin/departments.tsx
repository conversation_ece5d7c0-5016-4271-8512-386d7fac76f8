import {
  MagnifyingGlassIcon,
  PlusSmallIcon,
} from "@heroicons/react/24/outline";
import {
  Group,
  Stack,
  ActionIcon,
  Button,
  Input,
  Pagination,
  Select,
  Text,
  Skeleton,
} from "@mantine/core";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import DepartmentCard from "~components/Admin/Departments/DepartmentCard";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";
import Masonry from "react-masonry-css";

import DepartmentModal from "~components/Admin/Departments/DepartmentModal";
import { getHotkeyHandler, useElementSize } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { orderBy } from "lodash";

const breakpointColumnsObj = {
  default: 4,
  1600: 3,
  1200: 2,
  790: 1,
};

export default function Departments() {
  const {
    departments,
    filteredDepartments,
    actionGetDepartments,
    actionResetDepartments,
  } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
      filteredDepartments: state.filteredDepartments!,
      actionGetDepartments: state.actionGetDepartments!,
      actionResetDepartments: state.actionResetDepartments!,
    }),
    shallow
  );

  const {
    isLoading,
    setLoadingLevel,
    setDepartmentModal,
    blankDepartment,
    setIsLoading,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      setDepartmentModal: state.setDepartmentModal!,
      blankDepartment: state.computed!.blankDepartment!,
      setIsLoading: state.setIsLoading!,
    }),
    shallow
  );

  const { actionGetUsers } = useStore(
    "users",
    (state) => ({ actionGetUsers: state.actionGetUsers! }),
    shallow
  );

  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);
  const [search, setSearch] = useState("");
  const [isOpenSearchBar, setIsOpenSearchBar] = useState(false);

  const addDepartment = useCallback(() => {
    setDepartmentModal({
      isOpen: true,
      type: "new",
      data: blankDepartment,
    });
  }, [blankDepartment, setDepartmentModal]);

  useEffect(() => {
    updateDepartments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  const updateDepartments = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetDepartments(+rowsPerPage, +activePage - 1, search!);
    } finally {
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    actionGetDepartments,
    rowsPerPage,
    search,
    activePage,
  ]);

  return (
    <Stack
      ref={ref}
      sx={{
        opacity: width === 0 ? 0 : 1,
        transition: "opacity .2s ease",
        position: "relative",
      }}
    >
      <DepartmentModal updateDepartments={updateDepartments} />

      <Group position={width <= 650 && width > 0 ? "center" : "apart"}>
        <Group
          position="center"
          sx={{ width: width <= 400 && width !== 0 ? "100%" : "auto" }}
        >
          <Button
            fullWidth={width <= 400 && width !== 0}
            variant="light"
            size="sm"
            leftIcon={<PlusSmallIcon style={{ width: 20, height: 20 }} />}
            onClick={() => addDepartment()}
          >
            {t("addDepartment")}
          </Button>
          <Group noWrap>
            <ActionIcon
              size="lg"
              variant="light"
              color="gray"
              sx={{ width: 36, height: 36 }}
              onClick={() => setIsOpenSearchBar(!isOpenSearchBar)}
            >
              <MagnifyingGlassIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
            <Input
              placeholder={t("search")}
              size={"sm"}
              sx={{
                width: width < 500 ? "100%" : isOpenSearchBar ? 200 : 0,
                opacity:
                  (isOpenSearchBar || width < 500) && width !== 0 ? 1 : 0,
                transition: "width .2s ease, opacity .25s ease",
              }}
              value={search}
              onChange={(e: any) => {
                setSearch(e.target.value);
              }}
              onKeyDown={getHotkeyHandler([["Enter", updateDepartments]])}
            />
          </Group>
        </Group>
        <Group
          position={width < 520 && width !== 0 ? "center" : "apart"}
          align="center"
        >
          <Group>
            <Text size="xs" weight={600} color="dimmed">
              {t("rowPerPage")}
            </Text>
            <Select
              size="xs"
              value={rowsPerPage}
              onChange={(value: string) => setRowsPerPage(value)}
              data={[
                { value: "2", label: "2" },
                { value: "10", label: "10" },
                { value: "20", label: "20" },
                { value: "50", label: "50" },
              ]}
              sx={{
                width: 100,
              }}
              transition="fade"
              transitionDuration={200}
            />
          </Group>
          <Pagination
            page={activePage}
            onChange={setActivePage}
            total={Math.ceil(metrics.departments / +rowsPerPage) || 1}
            size="sm"
          />
        </Group>
      </Group>

      <Masonry
        breakpointCols={breakpointColumnsObj}
        className="my-masonry-grid"
        columnClassName="my-masonry-grid_column"
      >
        {orderBy(filteredDepartments, "id").map(
          (department: any, i: number) => (
            <DepartmentCard key={"department-" + i} department={department} />
          )
        )}
      </Masonry>
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
