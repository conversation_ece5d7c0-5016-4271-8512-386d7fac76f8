import {
  Stack,
  Title,
  Text,
  Button,
  Group,
  Stepper,
  Input,
  Paper,
} from "@mantine/core";
import { useState } from "react";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

const Wizard = function () {
  const [active, setActive] = useState(1);
  const nextStep = () =>
    setActive((current) => (current < 3 ? current + 1 : current));
  const prevStep = () =>
    setActive((current) => (current > 0 ? current - 1 : current));

  return (
    <Stack justify="space-between">
      <Stepper active={active} breakpoint="xs" contentPadding={48}>
        <Stepper.Step label="First step" description="Create an account">
          <Input.Wrapper
            withAsterisk
            label="Credit card information"
            description="Please enter your credit card information, we need some money"
          >
            <Input placeholder="Your email" />
          </Input.Wrapper>
        </Stepper.Step>
        <Stepper.Step label="Second step" description="Verify email">
          <Input.Wrapper
            withAsterisk
            label="Credit card information"
            description="Please enter your credit card information, we need some money"
          >
            <Input placeholder="Your email" />
          </Input.Wrapper>
        </Stepper.Step>
        <Stepper.Step label="Final step" description="Get full access">
          <Input.Wrapper
            withAsterisk
            label="Credit card information"
            description="Please enter your credit card information, we need some money"
          >
            <Input placeholder="Your email" />
          </Input.Wrapper>
        </Stepper.Step>

        <Stepper.Completed>
          <Text size="md" weight={600} color="dimmed" align="center">
            Completed, click done button to get to app
          </Text>
        </Stepper.Completed>
      </Stepper>

      <Group position="right" mt="lg">
        <Button variant="default" onClick={prevStep} disabled={active === 0}>
          Back
        </Button>
        {active < 3 ? (
          <Button onClick={nextStep}>Next</Button>
        ) : (
          <Button onClick={nextStep}>Done</Button>
        )}
      </Group>
    </Stack>
  );
};

Wizard.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Wizard;
