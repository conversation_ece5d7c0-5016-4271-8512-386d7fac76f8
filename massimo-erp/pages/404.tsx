import {
  createStyles,
  Container,
  Title,
  Text,
  <PERSON><PERSON>,
  Group,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import Illustration from "~components/404Illustration";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

const useStyles = createStyles((theme) => ({
  root: {
    paddingTop: 80,
    paddingBottom: 80,
  },

  inner: {
    position: "relative",
  },

  image: {
    position: "absolute",
    top: 0,
    right: 0,
    left: 0,
    zIndex: 0,
    opacity: 0.75,
  },

  content: {
    paddingTop: 220,
    position: "relative",
    zIndex: 1,

    [theme.fn.smallerThan("sm")]: {
      paddingTop: 120,
    },
  },

  title: {
    fontFamily: `Greycliff CF, ${theme.fontFamily}`,
    textAlign: "center",
    fontWeight: 900,
    fontSize: 38,

    [theme.fn.smallerThan("sm")]: {
      fontSize: 32,
    },
  },

  description: {
    maxWidth: 540,
    margin: "auto",
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl * 1.5,
  },
}));

const ErrorPage = function () {
  const { classes } = useStyles();
  const { ref, width } = useElementSize();
  const { t } = useTranslation();

  return (
    <Container
      className={classes.root}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
      ref={ref}
    >
      <div className={classes.inner}>
        <Illustration className={classes.image} />
        <div className={classes.content}>
          <Title className={classes.title}>{t("404.title")}</Title>
          <Text
            color="dimmed"
            size="lg"
            align="center"
            className={classes.description}
          >
            {t("404.message")}
          </Text>
          <Group position="center">
            <Link href="/">
              <Button size="md">{t("404.button")}</Button>
            </Link>
          </Group>
        </div>
      </div>
    </Container>
  );
};

ErrorPage.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default ErrorPage;
