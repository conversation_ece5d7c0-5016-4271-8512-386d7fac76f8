import { useCallback } from "react";
import { Page, Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import {
  TextInput,
  PasswordInput,
  Anchor,
  Paper,
  Title,
  Text,
  Container,
  SimpleGrid,
  Button,
  Stack,
} from "@mantine/core";
import { getHotkeyHand<PERSON>, useHotkeys, useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useRouter } from "next/router";
import RegisterCompleteModal from "~components/RegisterCompleteModal";
import { useForm } from "@mantine/form";

const Register: Page = function () {
  const { isLoading, setIsLoading, setIsOpenRegisterCompleteModal } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      setIsOpenRegisterCompleteModal: state.setIsOpenRegisterCompleteModal!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 500px)");
  const { t } = useTranslation("common");
  const { push } = useRouter();

  const form = useForm({
    initialValues: {
      name: "",
      surname: "",
      email: "",
      password: "",
    },

    validate: {
      name: (value) => (value !== "" ? null : t("formErrors.name")),

      surname: (value) => (value !== "" ? null : t("formErrors.surname")),

      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : t("formErrors.email"),

      password: (value) =>
        value !== ""
          ? value.length >= 8
            ? null
            : t("formErrors.password.short")
          : t("formErrors.password.empty"),
    },
  });

  const signup = useCallback(() => {
    const validate = form.validate();

    if (validate.hasErrors) {
      return;
    }

    setIsLoading(true);
    setIsOpenRegisterCompleteModal(true);

    setTimeout(() => {
      console.debug(form.values);
      setIsLoading(false);
    }, 1000);
  }, [form, setIsLoading, setIsOpenRegisterCompleteModal]);

  useHotkeys([["Enter", () => signup()]]);

  return (
    <Container size={450} my={40}>
      <RegisterCompleteModal />
      <Title
        align="center"
        sx={(theme) => ({
          fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          fontWeight: 700,
        })}
        mb={8}
      >
        {t("register.title")}
      </Title>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <form onSubmit={form.onSubmit(signup)}>
          <Stack spacing="sm" mb="lg">
            <SimpleGrid cols={matches ? 1 : 2}>
              <TextInput
                disabled={isLoading}
                onKeyDown={getHotkeyHandler([["Enter", () => signup()]])}
                label={t("register.nameInput.label")}
                placeholder={t("register.nameInput.placeholder")}
                required
                {...form.getInputProps("name")}
              />
              <TextInput
                disabled={isLoading}
                onKeyDown={getHotkeyHandler([["Enter", () => signup()]])}
                label={t("register.surnameInput.label")}
                placeholder={t("register.surnameInput.placeholder")}
                required
                {...form.getInputProps("surname")}
              />
            </SimpleGrid>
            <TextInput
              disabled={isLoading}
              onKeyDown={getHotkeyHandler([["Enter", () => signup()]])}
              label={t("register.emailInput.label")}
              placeholder={t("register.emailInput.placeholder")}
              required
              {...form.getInputProps("email")}
            />
            <PasswordInput
              disabled={isLoading}
              onKeyDown={getHotkeyHandler([["Enter", () => signup()]])}
              label={t("register.passwordInput.label")}
              placeholder={t("register.passwordInput.placeholder")}
              required
              {...form.getInputProps("password")}
            />
          </Stack>
          <Button
            fullWidth
            loading={isLoading}
            loaderProps={{ variant: "bars" }}
            type="submit"
          >
            {t("register.registerButton")}
          </Button>
        </form>
        <Text color="dimmed" size="sm" mt="lg">
          {t("register.loginLinkText")}
          <Anchor size="sm" ml={5} onClick={() => push("/")}>
            {t("register.loginLink")}
          </Anchor>
        </Text>
      </Paper>
    </Container>
  );
};

Register.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Register;
