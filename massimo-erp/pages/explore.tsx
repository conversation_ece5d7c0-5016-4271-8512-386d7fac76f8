import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import {
  Box,
  Button,
  Divider,
  Group,
  Input,
  SimpleGrid,
  Stack,
  Text,
} from "@mantine/core";
import { useElementSize, useMediaQuery } from "@mantine/hooks";

import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { isNumber } from "lodash";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import Post from "~components/Post";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import Stories from "~components/Stories";
import { useStore } from "~utils/store";
import { PostType } from "~utils/types/Post";

const Explore = function () {
  const { setProfileSearchModal, setPostModal } = useStore(
    "temp",
    (state) => ({
      setProfileSearchModal: state.setProfileSearchModal!,
      setPostModal: state.setPostModal!,
    }),
    shallow
  );
  const { posts, stories, resetPosts, resetStories, actionGetFeed } = useStore(
    "posts",
    (state) => ({
      posts: state.posts!,
      stories: state.stories!,
      resetPosts: state.resetPosts!,
      resetStories: state.resetStories!,
      actionGetFeed: state.actionGetFeed!,
    }),
    shallow
  );

  const matches2 = useMediaQuery("(max-width: 992px)");
  const matches = useMediaQuery("(min-width: 1300px)");

  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState(true);

  const getPosts = useCallback(
    async (p?: number) => {
      try {
        let hasMoreData = await actionGetFeed(isNumber(p) ? p : page);
        setHasMore(hasMoreData);
      } finally {
        setPage((p) => p + 1);
      }
    },
    [actionGetFeed, page]
  );

  const handleReset = useCallback(async () => {
    await resetStories();
    await resetPosts();
    setPage(0);
    setHasMore(true);

    await getPosts(0);
  }, [getPosts, resetPosts, resetStories]);

  useEffect(() => {
    handleReset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CustomInfiniteScroll
      dataLength={posts.length}
      hasMore={hasMore}
      next={getPosts}
      height="calc(100vh - 70px)"
    >
      <Stack
        ref={ref}
        spacing="lg"
        sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
        p={matches2 ? 16 : 32}
      >
        {!matches && (
          <Input
            size="sm"
            icon={<MagnifyingGlassIcon width={16} height={16} />}
            placeholder={t("profileSearch")}
            onClick={() => {
              setProfileSearchModal({
                isOpen: true,
              });
            }}
          />
        )}

        <Stack spacing={6}>
          <Text size="md" weight={600}>
            {t("stories")}
          </Text>
          <Divider />
        </Stack>

        <Stories withName={true} group={true} feed={true} />

        <Stack spacing={6}>
          <Group position="apart">
            <Text size="md" weight={600}>
              {t("profile.posts")}
            </Text>

            <Button
              size="xs"
              variant="light"
              onClick={() =>
                setPostModal({
                  modalType: "new",
                  isOpen: true,
                  type: "post",
                })
              }
            >
              {t("profile.newPost")}
            </Button>
          </Group>
          <Divider />
        </Stack>
        {posts.length > 0 ? (
          <SimpleGrid
            cols={width > 1150 ? 3 : width > 700 ? 3 : width > 450 ? 2 : 1}
            sx={
              width > 1150
                ? { width: 1000, maxWidth: 1000 }
                : {
                    width: "100%",
                  }
            }
            mx="auto"
            spacing="md"
            verticalSpacing={32}
          >
            {posts.map((post: PostType, i: number) => {
              return <Post key={`post-${i}`} post={post} />;
            })}
          </SimpleGrid>
        ) : (
          <Text size="xs" weight={600} color="dimmed">
            {t("profile.posts")}
          </Text>
        )}
      </Stack>
    </CustomInfiniteScroll>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Explore;
