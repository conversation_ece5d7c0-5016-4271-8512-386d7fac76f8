import { useCallback } from "react";
import { useRouter } from "next/router";
import { Page, Renderers } from "~types";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { getTranslations } from "~utils/getTranslations";

import {
  TextInput,
  PasswordInput,
  Anchor,
  Paper,
  Title,
  Text,
  Container,
  Button,
  Stack,
} from "@mantine/core";
import { useHotkeys } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";
import { useTranslation } from "next-i18next";
import { useForm } from "@mantine/form";

const Login: Page = function () {
  const { actionSignIn } = useStore(
    "global",
    (state) => ({
      actionSignIn: state.actionSignIn!,
    }),
    shallow
  );
  const { isLoading, setIsLoading } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
    }),
    shallow
  );
  const { t } = useTranslation("common");
  const { push } = useRouter();

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },

    validate: {
      email: (value) =>
        /^\S+@\S+$/.test(value) ? null : t("formErrors.email"),

      password: (value) =>
        value !== ""
          ? value.length >= 8
            ? null
            : t("formErrors.password.short")
          : t("formErrors.password.empty"),
    },
  });

  const signin = useCallback(async () => {
    const validate = form.validate();

    if (validate.hasErrors) {
      return;
    }

    try {
      setIsLoading(true);

      await actionSignIn(form.values);

      showNotification({
        color: "green",
        title: t("login.success.title"),
        message: t("login.success.message"),
        autoClose: 3000,
      });
    } catch (err) {
      console.error(err);

      showNotification({
        color: "red",
        title: t("login.fail.title"),
        message: t("login.fail.message"),
        autoClose: 3000,
      });
    } finally {
      setIsLoading(false);
    }
  }, [form, setIsLoading, actionSignIn, t]);

  useHotkeys([["Enter", () => signin()]]);

  return (
    <Container id="login-form" size={450} my={40} sx={{ width: "100%" }}>
      <Title
        align="center"
        sx={(theme) => ({
          fontFamily: `Greycliff CF, ${theme.fontFamily}`,
          fontWeight: 700,
        })}
        mb={8}
      >
        {t("login.title")}
      </Title>

      <Paper withBorder shadow="md" p={30} mt={30} radius="md">
        <form onSubmit={form.onSubmit(signin)}>
          <Stack spacing="sm" mb="lg">
            <TextInput
              disabled={isLoading}
              label={t("login.emailInput.label")}
              placeholder={t("login.emailInput.placeholder")}
              required
              {...form.getInputProps("email")}
            />
            <PasswordInput
              disabled={isLoading}
              label={t("login.passwordInput.label")}
              placeholder={t("login.passwordInput.placeholder")}
              required
              {...form.getInputProps("password")}
            />
          </Stack>
          {/* <Group position="right" mt="xs">
              <Anchor<"a">
                onClick={(event) => event.preventDefault()}
                href="#"
                size="sm"
              >
                {t("login.forgotPasswordText")}
              </Anchor>
            </Group> */}
          <Button
            fullWidth
            loading={isLoading}
            loaderProps={{ variant: "bars" }}
            type="submit"
          >
            {t("login.loginButton")}
          </Button>
        </form>
        <Text color="dimmed" size="sm" mt="lg">
          {t("login.registerLinkText")}
          <Anchor size="sm" ml={5} onClick={() => push("/register")}>
            {t("login.registerLink")}
          </Anchor>
        </Text>
      </Paper>
    </Container>
  );
};

Login.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Login;
