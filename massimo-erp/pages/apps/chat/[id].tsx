import React, { useCallback, useEffect, useState } from "react";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import { Box, Group, Skeleton } from "@mantine/core";

import LeftMenu from "~components/Apps/Chat/LeftMenu";
import ChatHeader from "~components/Apps/Chat/ChatHeader";
import ChatSettings from "~components/Apps/Chat/ChatSettings";
import { useClickOutside, useElementSize, useMediaQuery } from "@mantine/hooks";
import ChatMessages from "~components/Apps/Chat/ChatMessages";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { useRouter } from "next/router";
import { NotificationType } from "~utils/types/Notification";

const Chat = function () {
  const { subscribedChats, activeChat, setActiveChat } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
      activeChat: state.activeChat!,
      setActiveChat: state.setActiveChat!,
    }),
    shallow
  );
  const { notifications, actionViewNotification } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
      actionViewNotification: state.actionViewNotification!,
    }),
    shallow
  );

  const { query, push } = useRouter();
  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(min-width: 992px)");

  const [isOpenSettings, setIsOpenSettings] = useState(false);
  const [isOpenLeftMenu, setIsOpenLeftMenu] = useState(false);

  const settingsMenuRef = useClickOutside(() => {
    if (width > 900) {
      return;
    }
    setIsOpenSettings(false);
  });
  const leftMenuRef = useClickOutside(() => setIsOpenLeftMenu(false));

  const readNotifications = useCallback(async () => {
    await notifications.forEach(async (notification: NotificationType) => {
      if (
        notification.target === "messages" &&
        notification.value === `${activeChat}`
      ) {
        await actionViewNotification([{ id: notification.id }]);
      }
    });
  }, [actionViewNotification, activeChat, notifications]);

  useEffect(() => {
    let activeChatId = 0;
    let hasChat = subscribedChats.some((e) => e.id === +(query.id as string));

    if (query.id && hasChat) {
      activeChatId = +query.id;
    }

    setActiveChat(activeChatId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    readNotifications();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeChat]);

  return (
    <Box
      sx={{
        height: matches ? "calc(100vh - 140px)" : "calc(100vh - 70px)",
        position: "relative",
        display: "flex",
        overflow: "hidden",
        alignItems: "stretch",
        opacity: width === 0 ? 0 : 1,
        transition: "opacity .2s ease",
      }}
      ref={ref}
    >
      {width !== 0 && (
        <>
          <Box
            ref={leftMenuRef}
            mr="md"
            sx={{
              flexShrink: 0,
              width: width <= 500 ? "100vw" : 270,
              position: width <= 1000 ? "absolute" : "static",
              left: width <= 1000 && !isOpenLeftMenu ? "-100%" : 0,
              transition: "left .2s ease",
              zIndex: 110,
            }}
          >
            <LeftMenu
              isOpenLeftMenu={isOpenLeftMenu}
              closeLeftMenu={() => setIsOpenLeftMenu(false)}
              openCloseButton={width <= 1000}
            />
          </Box>

          <Box
            sx={{
              position: "relative",
              maxHeight: matches ? "calc(100vh - 140px)" : "calc(100vh - 70px)",
              flexGrow: 1,
              display: "flex",
              flexWrap: "nowrap",
            }}
          >
            <Box
              sx={{
                position: "relative",
                width: "100%",
                height: "100%",
                display: "flex",
                flexDirection: "column",
                transition: "width .2s ease",
              }}
            >
              <ChatHeader
                toggleSettings={() => setIsOpenSettings(!isOpenSettings)}
                setLeftMenu={(value: boolean) => setIsOpenLeftMenu(value)}
                menuButtonOpen={width <= 1000}
                isMobile={width <= 400}
              />
              {activeChat !== 0 && <ChatMessages isMobile={width <= 450} />}
            </Box>

            {activeChat !== 0 && (
              <Box
                ref={settingsMenuRef}
                sx={(theme) => ({
                  position: width <= 900 ? "absolute" : "static",
                  top: width <= 900 ? 0 : "unset",
                  right: isOpenSettings
                    ? width <= 900
                      ? 0
                      : "unset"
                    : "-100%",
                  bottom: width <= 900 ? 0 : "unset",
                  width: isOpenSettings ? (width <= 500 ? "100%" : 286) : 0,
                  height: "100%",
                  transition: "width .2s ease",
                  flexShrink: 0,
                  zIndex: 110,
                  overflow: "hidden",
                })}
              >
                <ChatSettings
                  isOpenSettings={isOpenSettings}
                  closeSettings={() => setIsOpenSettings(false)}
                  fullWidth={width <= 500}
                />
              </Box>
            )}
          </Box>
        </>
      )}
    </Box>
  );
};

export const getServerSideProps: Renderers["getServerSideProps"] = async (
  context
) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Chat;
