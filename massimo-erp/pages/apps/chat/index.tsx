import { useRouter } from "next/router";
import { useCallback, useEffect } from "react";
import shallow from "zustand/shallow";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";
import { useStore } from "~utils/store";

const Privacy = function () {
  const { chats, subscribedChats } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
      chats: state.chats!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users, actionGetUsers } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      actionGetUsers: state.actionGetUsers!,
    }),
    shallow
  );
  const { initialized, setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      initialized: state.initialized,
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { push } = useRouter();

  const changePath = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await push(
        {
          pathname:
            "/apps/chat/" +
            ((
              subscribedChats.filter((e) =>
                ["person", "organization"].includes(e.type)
              )[0] || {}
            ).id || 0),
        },
        undefined,
        {
          shallow: true,
        }
      );
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading, setLoadingLevel, push, subscribedChats]);

  useEffect(() => {
    let aborted = false;

    if (initialized) {
      setTimeout(() => {
        changePath();
      }, 100);
    }

    return () => {
      aborted = true;
    };
  }, [changePath, chats, initialized, subscribedChats]);

  return <></>;
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Privacy;
