import dynamic from "next/dynamic";
import { useRouter } from "next/router";
import { useState } from "react";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import { Stack } from "@mantine/core";

import { useElementSize, useMediaQuery } from "@mantine/hooks";

const DynamicKanban = dynamic(() => import("~components/Apps/Kanban"), {
  ssr: false,
});

export default function Projects() {
  const { ref, width } = useElementSize();

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <DynamicKanban />
    </Stack>

    // <Tabs variant="pills" defaultValue="dashboard" value={tabValue} radius="md">
    //   <Group position="apart" align="stretch">
    //     <Stack justify="space-between" sx={{ flexGrow: 1 }}>
    //       <Stack align="flex-start">
    //         <Stack spacing={4}>
    //           <Title>Lorem ipsum dolar sit amet</Title>
    //           <Text size="sm" weight={600} color="dimmed">
    //             Updated 1 day ago
    //           </Text>
    //         </Stack>

    //         <Space />

    //         <ScrollArea offsetScrollbars scrollbarSize={3}>
    //           <Box
    //             sx={{
    //               width: matches ? "calc(100vw - 36px)" : "auto",
    //             }}
    //           >
    //             <SegmentedControl
    //               color="blue"
    //               value={tabValue}
    //               onChange={(value: string) => setTabValue(value)}
    //               data={[
    //                 { label: "Dashboard", value: "dashboard" },
    //                 { label: "Tasks", value: "tasks" },
    //                 { label: "Communication", value: "communication" },
    //                 { label: "Files", value: "files" },
    //               ]}
    //             />
    //           </Box>
    //         </ScrollArea>
    //       </Stack>

    //       <Group
    //         position="apart"
    //         align="center"
    //         mt={20}
    //         sx={{
    //           maxWidth: matches ? "calc(100vw - 70px)" : "auto",
    //         }}
    //       >
    //         <Group align="center" noWrap>
    //           <Stack spacing={0}>
    //             <Text
    //               size={12}
    //               color="dimmed"
    //               mt={-20}
    //               mb={8}
    //               sx={{ lineHeight: 1 }}
    //             >
    //               Contributors
    //             </Text>
    //             <Avatar.Group spacing="sm">
    //               <Tooltip label="John Doe" position="bottom" withArrow>
    //                 <Avatar
    //                   src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=250&q=80"
    //                   radius="xl"
    //                 />
    //               </Tooltip>
    //               <Avatar radius="xl">
    //                 <ActionIcon
    //                   variant="light"
    //                   color="blue"
    //                   radius="xl"
    //                   size="lg"
    //                 >
    //                   <PlusSmallIcon style={{ width: 20, height: 20 }} />
    //                 </ActionIcon>
    //               </Avatar>
    //             </Avatar.Group>
    //           </Stack>
    //           <Divider size="xs" my={8} orientation="vertical" />
    //           <Button color="gray" variant="outline">
    //             <FolderIcon style={{ width: 20, height: 20 }} />
    //             <Text ml={8}>9 Files</Text>
    //           </Button>
    //           <Button color="gray" variant="outline">
    //             <DocumentDuplicateIcon style={{ width: 20, height: 20 }} />
    //             <Text ml={8}>2 Invoices</Text>
    //           </Button>
    //         </Group>

    //         <Group noWrap justify="right">
    //           <Button color="gray" variant="outline">
    //             <AdjustmentsHorizontalIcon style={{ width: 20, height: 20 }} />
    //             <Text ml={8}>Filter</Text>
    //           </Button>
    //           <Divider size="xs" my={8} orientation="vertical" />
    //           <SegmentedControl
    //             color="blue"
    //             data={[
    //               {
    //                 label: (
    //                   <Center>
    //                     <ViewColumnsIcon style={{ width: 20, height: 20 }} />
    //                   </Center>
    //                 ),
    //                 value: "react",
    //               },
    //               {
    //                 label: (
    //                   <Center>
    //                     <QueueListIcon style={{ width: 20, height: 20 }} />
    //                   </Center>
    //                 ),
    //                 value: "ng",
    //               },
    //             ]}
    //           />
    //         </Group>
    //       </Group>
    //     </Stack>

    //     <Space w="lg" />

    //     <Stack>
    //       <Group position="apart" align="center">
    //         <Title order={3}>Project Drive</Title>
    //         <Group spacing="xs">
    //           <ActionIcon>
    //             <ChevronLeftIcon style={{ width: 20, height: 20 }} />
    //           </ActionIcon>
    //           <ActionIcon>
    //             <ChevronRightIcon style={{ width: 20, height: 20 }} />
    //           </ActionIcon>
    //         </Group>
    //       </Group>
    //       <Carousel
    //         sx={{ width: matches ? "100%" : 600 }}
    //         slideSize={matches ? "100%" : "50%"}
    //         slideGap="md"
    //         align="start"
    //         dragFree={matches}
    //         slidesToScroll={matches ? "auto" : 2}
    //         withControls={false}
    //         initialSlide={activeSlide}
    //       >
    //         <Carousel.Slide>
    //           <Card shadow="sm" p="md" radius="md" withBorder>
    //             <Image
    //               src="https://images.unsplash.com/photo-1527004013197-933c4bb611b3?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=720&q=80"
    //               height={120}
    //               alt="Norway"
    //               radius="md"
    //             />
    //             <Stack spacing={2} mt="md">
    //               <Group align="center" spacing="xs">
    //                 <PhotoIcon style={{ width: 20, height: 20 }} />
    //                 <Text weight={500}>Input fields master</Text>
    //               </Group>
    //               <Text size="sm" color="dimmed">
    //                 Edited Today
    //               </Text>
    //             </Stack>
    //           </Card>
    //         </Carousel.Slide>
    //         <Carousel.Slide>
    //           <Card shadow="sm" p="md" radius="md" withBorder>
    //             <Image
    //               src="https://images.unsplash.com/photo-1527004013197-933c4bb611b3?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=720&q=80"
    //               height={120}
    //               alt="Norway"
    //               radius="md"
    //             />
    //             <Stack spacing={2} mt="md">
    //               <Group align="center" spacing="xs">
    //                 <PhotoIcon style={{ width: 20, height: 20 }} />
    //                 <Text weight={500}>Input fields master</Text>
    //               </Group>
    //               <Text size="sm" color="dimmed">
    //                 Edited Today
    //               </Text>
    //             </Stack>
    //           </Card>
    //         </Carousel.Slide>
    //         <Carousel.Slide>
    //           <Card shadow="sm" p="md" radius="md" withBorder>
    //             <Image
    //               src="https://images.unsplash.com/photo-1527004013197-933c4bb611b3?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=720&q=80"
    //               height={120}
    //               alt="Norway"
    //               radius="md"
    //             />
    //             <Stack spacing={2} mt="md">
    //               <Group align="center" spacing="xs">
    //                 <PhotoIcon style={{ width: 20, height: 20 }} />
    //                 <Text weight={500}>Input fields master</Text>
    //               </Group>
    //               <Text size="sm" color="dimmed">
    //                 Edited Today
    //               </Text>
    //             </Stack>
    //           </Card>
    //         </Carousel.Slide>
    //       </Carousel>
    //     </Stack>
    //   </Group>

    //   <Space h="xl" />

    //   <Tabs.Panel value="dashboard" pt="xs">
    //     <DynamicKanban />
    //   </Tabs.Panel>

    //   <Tabs.Panel value="tasks" pt="xs">
    //     Tasks tab content
    //   </Tabs.Panel>

    //   <Tabs.Panel value="communication" pt="xs">
    //     Communication tab content
    //   </Tabs.Panel>

    //   <Tabs.Panel value="files" pt="xs">
    //     Files tab content
    //   </Tabs.Panel>
    // </Tabs>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
