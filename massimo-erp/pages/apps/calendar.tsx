import { createRef, useCallback, useEffect, useRef, useState } from "react";
import { Box, Space, Burger, ScrollArea, Center } from "@mantine/core";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

import FullCalendar from "@fullcalendar/react";
import listPlugin from "@fullcalendar/list";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";
import CalendarWrapper from "~components/Apps/Calendar/CalendarWrapper";
import CalendarSidebar from "~components/Apps/Calendar/CalendarSidebar";
import { useElementSize, useMediaQuery } from "@mantine/hooks";
import { EllipsisVerticalIcon } from "@heroicons/react/24/outline";
import { uniq } from "lodash";
import { showNotification } from "@mantine/notifications";
import { useTranslation } from "next-i18next";

function convertData(data: any) {
  return {
    id: data.id,
    title: data.title,
    start: data.start,
    end: data.end || data.start,
    ...data._def.extendedProps,
  };
}

const Calendar = () => {
  const {
    tasks,
    taskPrev,
    filteredTasks,
    actionGetTasks,
    actionResetTasks,
    statusColors,
    actionUpdateMultipleTask,
  } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      taskPrev: state.taskPrev!,
      statusColors: state.statusColors!,
      filteredTasks: state.filteredTasks!,
      actionGetTasks: state.actionGetTasks!,
      actionResetTasks: state.actionResetTasks!,
      actionUpdateMultipleTask: state.actionUpdateMultipleTask!,
    }),
    shallow
  );

  const {
    setIsOpenNewTaskModal,
    setNewTaskModalData,
    setIsOpenTaskViewModal,
    setTaskViewModalData,
    activeCalendarFilter,
  } = useStore(
    "temp",
    (state) => ({
      setIsOpenNewTaskModal: state.setIsOpenNewTaskModal!,
      setNewTaskModalData: state.setNewTaskModalData!,
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
      activeCalendarFilter: state.activeCalendarFilter!,
    }),
    shallow
  );

  const { isNavbarMinimized, isNavbarHover, activeUserId, selectedProjectId } =
    useStore(
      "global",
      (state) => ({
        isNavbarMinimized: state.isNavbarMinimized,
        isNavbarHover: state.isNavbarHover,
        activeUserId: state.activeUserId,
        selectedProjectId: state.selectedProjectId,
      }),
      shallow
    );

  const calendarRef = createRef<any>();
  const matches = useMediaQuery("(max-width: 992px)");
  const { ref, width } = useElementSize();
  const [activeDates, setActiveDates] = useState<[Date, Date] | []>([]);
  const { t } = useTranslation();

  const [sidebarOpen, setSidebarOpen] = useState(false);

  const eventClick = useCallback(
    (event: any) => {
      setTaskViewModalData(tasks.filter((e) => e.id === +event.id)[0]);
      setIsOpenTaskViewModal(true);
    },
    [setTaskViewModalData, setIsOpenTaskViewModal, tasks]
  );

  const dateClick = useCallback(
    async (date: Date) => {
      await setNewTaskModalData({
        start: date,
        end: date,
      });
      setIsOpenNewTaskModal(true);
    },
    [setIsOpenNewTaskModal, setNewTaskModalData]
  );

  useEffect(() => {
    if (calendarRef.current) {
      let aborted = false;
      let calendarApi = calendarRef.current.getApi();

      const changeHanler = (e: any) => {
        setActiveDates([e.start, e.end]);
      };

      calendarApi.on("datesSet", changeHanler);

      if (activeDates.length === 0) {
        setActiveDates([
          calendarApi.view.activeStart,
          calendarApi.view.activeEnd,
        ]);
      }

      setTimeout(() => {
        if (!aborted) {
          calendarApi.updateSize();
        }
      }, 500);

      return () => {
        aborted = true;
        calendarApi.off("datesSet", changeHanler);
      };
    }
  }, [calendarRef, isNavbarMinimized, isNavbarHover, activeDates]);

  const { setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );
  const [prev, setPrev] = useState(0);

  const updateTasks = useCallback(
    async (start: Date, end: Date, search?: string) => {
      setIsLoading(true);
      setLoadingLevel(0);

      if (!selectedProjectId) {
        showNotification({
          color: "red",
          title: t("task.project.fail.title"),
          message: t("task.project.fail.message"),
          autoClose: 3000,
        });

        setIsLoading(false);
      }

      try {
        await actionGetTasks(
          {
            start,
            end,
            userId: activeUserId!,
            projectId: selectedProjectId,
          },
          search
        );
      } finally {
        setIsLoading(false);
      }
    },
    [
      actionGetTasks,
      activeUserId,
      selectedProjectId,
      setIsLoading,
      setLoadingLevel,
      t,
    ]
  );

  useEffect(() => {
    if (prev !== taskPrev) {
      setPrev(taskPrev);

      setTimeout(() => {
        if (activeDates.length > 0) {
          updateTasks(activeDates[0]!, activeDates[1]!);
        }
      }, 100);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeDates, prev, taskPrev]);

  useEffect(() => {
    if (activeDates.length > 0) {
      updateTasks(activeDates[0]!, activeDates[1]!);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeDates, selectedProjectId]);

  useEffect(() => {
    actionResetTasks();
  }, [actionResetTasks]);

  const updateTask = useCallback(
    async (data: any) => {
      setIsLoading(true);
      setLoadingLevel(0);

      try {
        await actionUpdateMultipleTask([
          {
            ...data,
            id: +data.id,
            contributorIds: uniq([...data.contributorIds, activeUserId]),
          },
        ]);

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.taskUpdate"),
          autoClose: 3000,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [actionUpdateMultipleTask, activeUserId, setIsLoading, setLoadingLevel, t]
  );

  return (
    <Box
      ref={ref}
      sx={{
        display: "flex",
        maxWidth: "100%",
        position: "relative",
        gap: !matches ? 16 : 0,
        width: `calc(${matches ? "100vw - 32px" : "100%"})`,
        height: "calc(100vh - 140px)",
        opacity: width === 0 ? 0 : 1,
        transition: "opacity .2s ease",
      }}
    >
      <CalendarSidebar
        sidebarOpen={sidebarOpen}
        setSidebarOpen={(value: boolean) => setSidebarOpen(value)}
      />
      <ScrollArea
        styles={{
          root: {
            width: "100%",
            height: "100%",
          },
          viewport: {
            "& > div": {
              height: "100%",
            },
          },
        }}
      >
        <CalendarWrapper>
          <FullCalendar
            events={filteredTasks
              .filter((e) => activeCalendarFilter.includes(e.status))
              .map((e) => ({ ...e, id: `${e.id}` }))}
            plugins={[
              dayGridPlugin,
              timeGridPlugin,
              interactionPlugin,
              listPlugin,
            ]}
            headerToolbar={{
              left: "sidebarToggle, prev,next, title",
              right: "dayGridMonth,timeGridWeek,timeGridDay,listMonth",
            }}
            customButtons={{
              sidebarToggle: {
                text: (
                  <Center>
                    <EllipsisVerticalIcon style={{ width: 24, height: 24 }} />
                  </Center>
                ) as any,
                click() {
                  setSidebarOpen(true);
                },
              },
            }}
            eventClassNames={({ event }) =>
              `fc-${statusColors[event._def.extendedProps.status]}`
            }
            initialView="dayGridMonth"
            viewHeight={1000}
            editable={true}
            selectable={true}
            selectMirror={true}
            dayMaxEvents={2}
            dragScroll={true}
            height={"auto"}
            ref={calendarRef}
            eventClick={({ event }) => eventClick(event)}
            dateClick={({ date }) => dateClick(date)}
            eventDrop={({ event }) => updateTask(convertData(event))}
          />
        </CalendarWrapper>
      </ScrollArea>
    </Box>
  );
};

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Calendar;
