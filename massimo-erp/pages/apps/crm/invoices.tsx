import { useElementSize } from "@mantine/hooks";
import { Render<PERSON> } from "~types";
import { getTranslations } from "~utils/getTranslations";

import { Stack } from "@mantine/core";

export default function Invoices() {
  const { ref, width } = useElementSize();

  return (
    <Stack
      ref={ref}
      sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
    >
      <div>Invoices</div>
    </Stack>
  );
}

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};
