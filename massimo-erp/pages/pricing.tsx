import {
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  Group,
  List,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Title,
} from "@mantine/core";
import Link from "next/link";
import React from "react";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

const plans = [
  {
    isPopular: false,
    name: "Personal",
    description:
      "Perfect for an individual or a small team starting to get bigger",
    price: 6.0,
    unit: "₺",
  },
  {
    isPopular: true,
    name: "Premium",
    description: "Perfect for growing teams wanting to be in more control",
    price: 15.0,
    unit: "₺",
  },
  {
    isPopular: false,
    name: "Enterprise",
    description: "Perfect for companies wanting advanced tools and support",
    price: 69.0,
    unit: "₺",
  },
];

const Pricing = function () {
  return (
    <SimpleGrid cols={3} spacing="xl">
      {plans.map((plan: any, i: number) => (
        <Paper
          key={"plan-" + i}
          sx={(theme) => ({
            position: "relative",
            width: 320,
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[8]
                : theme.colors.gray[2],
            padding: theme.spacing.md,
            "::before": {
              content: "'POPULAR'",
              position: "absolute",
              height: 24,
              top: -12,
              left: "50%",
              transform: "translateX(-50%)",
              display: plan.isPopular ? "flex" : "none",
              alignItems: "center",
              background: theme.colors.blue[9],
              color: theme.white,
              borderRadius: 16,
              padding: `0 ${theme.spacing.md}px`,
              fontSize: theme.fontSizes.sm,
              fontWeight: 600,
              lineHeight: 1,
            },
          })}
          shadow="xl"
          pt="lg"
        >
          <Stack spacing="xl">
            <Stack spacing="sm">
              <Title order={3}>{plan.name}</Title>
              <Text size="sm" weight={600} color="dimmed">
                {plan.description}
              </Text>
            </Stack>
            <Divider />
            <Stack spacing={4}>
              <Group align="baseline" spacing={8}>
                <Text size="sm" weight={700} color="dimmed">
                  TL
                </Text>
                <Text size={32} weight={600} sx={{ lineHeight: 1 }}>
                  {plan.unit}
                  {plan.price}
                </Text>
              </Group>
              <Text size="xs" color="dimmed">
                Billed monthly
              </Text>
              <Group spacing={4} align="baseline">
                <Text size="sm" weight={600}>
                  {plan.unit}
                  {plan.price * 10}
                </Text>{" "}
                <Text size="xs" color="dimmed">
                  billed yearly
                </Text>
              </Group>
            </Stack>

            <Link href="/" passHref>
              <Button
                variant={plan.isPopular ? "filled" : "light"}
                color={plan.isPopular ? "blue" : "gray"}
              >
                Get Started
              </Button>
            </Link>
          </Stack>
        </Paper>
      ))}
    </SimpleGrid>
  );
};

Pricing.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default Pricing;
