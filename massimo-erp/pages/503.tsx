import {
  createStyles,
  Container,
  Title,
  Text,
  Button,
  Group,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useRouter } from "next/router";
import Illustration from "~components/503Illustration";
import { Renderers } from "~types";
import { getTranslations } from "~utils/getTranslations";

const useStyles = createStyles((theme) => ({
  root: {
    paddingTop: 120,
    paddingBottom: 120,
  },

  inner: {
    position: "relative",
  },

  image: {
    position: "absolute",
    top: 0,
    right: 0,
    left: 0,
    zIndex: 0,
    opacity: 0.65,
  },

  content: {
    paddingTop: 220,
    position: "relative",
    zIndex: 1,

    [theme.fn.smallerThan("sm")]: {
      paddingTop: 120,
    },
  },

  title: {
    fontFamily: `Greycliff CF, ${theme.fontFamily}`,
    textAlign: "center",
    fontWeight: 900,
    fontSize: 38,
    color: theme.white,

    [theme.fn.smallerThan("sm")]: {
      fontSize: 32,
    },
  },

  description: {
    maxWidth: 460,
    margin: "auto",
    marginTop: theme.spacing.xl,
    marginBottom: theme.spacing.xl * 1.5,
    color: theme.colors[theme.primaryColor][1],
  },
}));

const ErrorPage = function () {
  const { classes } = useStyles();
  const { reload } = useRouter();
  const { ref, width } = useElementSize();
  const { t } = useTranslation();

  return (
    <div className={classes.root}>
      <Container
        ref={ref}
        sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
      >
        <div className={classes.inner}>
          <Illustration className={classes.image} />
          <div className={classes.content}>
            <Title className={classes.title}>{t("503.title")}</Title>
            <Text size="lg" align="center" className={classes.description}>
            {t("503.message")}
            </Text>
            <Group position="center">
              <Button size="md" color="blue" onClick={() => reload()}>
                {t("503.button")}
              </Button>
            </Group>
          </div>
        </div>
      </Container>
    </div>
  );
};

ErrorPage.layout = "auth";

export const getStaticProps: Renderers["getStaticProps"] = async (context) => {
  return await getTranslations(context, {
    namespaces: ["common"],
  });
};

export default ErrorPage;
