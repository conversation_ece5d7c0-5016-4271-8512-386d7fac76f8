import { Stack, Box, Image, Text, useMantineTheme } from "@mantine/core";
import { useTranslation } from "next-i18next";
import DashboardItemTemplate from "./Dashboard/DashboardItemTemplate";

export default function DataNotFound() {
  const theme = useMantineTheme();
  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack align="center" spacing={6}>
        <Box
          sx={{
            width: 100,
            height: 75,
            overflow: "hidden",
            display: "flex",
            alignItems: "center",
          }}
        >
          <Image
            src={
              theme.colorScheme === "dark"
                ? "/animated-eye-dark.gif"
                : "/animated-eye-light.gif"
            }
            alt="No Data"
            height={100}
            width={100}
            fit={"cover"}
          />
        </Box>
        <Text size="sm" weight={600} mb={12} color="dimmed">
          {t("dataNotFound")}
        </Text>
      </Stack>
    </DashboardItemTemplate>
  );
}
