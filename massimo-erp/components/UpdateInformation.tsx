import { Box, Group, Loader, Text } from "@mantine/core";
import React from "react";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";

const UpdateInformation = () => {
  const { updateInformation } = useStore("temp", (state) => ({
    updateInformation: state.updateInformation,
  }));

  return (
    <Box
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[3],
        position: "absolute",
        bottom: 0,
        left: 0,
        borderRadius: theme.radius.md,
        zIndex: 10000,
      })}
      px="xs"
      py={6}
      m="xs"
    >
      <Group spacing="xs">
        <Loader size={16} variant="bars" />
        <Text size="xs" weight={600} color="dimmed" transform="capitalize">
          {updateInformation?.type}
        </Text>
      </Group>
    </Box>
  );
};

export default UpdateInformation;
