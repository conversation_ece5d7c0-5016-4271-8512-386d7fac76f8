import React from "react";
import { But<PERSON>, Text, Stack, useMantineTheme } from "@mantine/core";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { ExclamationTriangleIcon } from "@heroicons/react/24/outline";

const ErrorContent: CC<{
  onClick: () => void;
}> = function ({ onClick }) {
  const theme = useMantineTheme();
  const { t } = useTranslation();

  return (
    <Stack align="center" pt="xl">
      <ExclamationTriangleIcon
        width={36}
        height={36}
        color={theme.colors.red[9]}
      />
      <Stack spacing={4} align="center">
        <Text size="xl" weight={600}>
          {t("error.title")}
        </Text>
        <Text size="xs" weight={600}>
          {t("error.message")}
        </Text>
      </Stack>
      <Button color="gray" onClick={onClick}>
        {t("error.try")}
      </Button>
    </Stack>
  );
};

class ErrorBoundary extends React.Component {
  state: {
    hasError: false;
  };

  constructor(props: { children: any }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: any) {
    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.log({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorContent onClick={() => this.setState({ hasError: false })} />
      );
    }

    return (this.props as { children: any }).children;
  }
}

export default ErrorBoundary;
