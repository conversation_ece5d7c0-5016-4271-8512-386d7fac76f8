import { Avatar } from "@mantine/core";
import { findLast } from "lodash";
import { forwardRef } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";

const ActiveAvatar: CC<{
  userId?: number | null;
  [x: string]: any;
}> = forwardRef(function ActiveAvatar({ userId, ...props }, ref) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const activeUser = users.find(
    (user) => user.id === (!userId ? activeUserId : userId)
  );

  const findedFile = findLast(
    files,
    (file) => file[1] === "avatar" && activeUser?.fileIds?.includes(file[0])
  ) as [number, string, string];

  const avatar = findedFile ? findedFile[2] : "";

  return (
    <Avatar
      ref={ref as any}
      src={userId === null ? undefined : avatar}
      {...props}
    />
  );
});

export default ActiveAvatar;
