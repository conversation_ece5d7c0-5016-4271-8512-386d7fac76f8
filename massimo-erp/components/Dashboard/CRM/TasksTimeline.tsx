import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  MantineTheme,
  Accordion,
  Timeline,
  Text,
  Group,
  Badge,
  Anchor,
  Avatar,
  Progress,
  Paper,
  Stack,
  Space,
  Tooltip,
  Title,
  Image,
  Box,
  useMantineTheme,
  Divider,
} from "@mantine/core";
import { PlusIcon } from "@heroicons/react/24/outline";
import DashboardItemTemplate from "../DashboardItemTemplate";

import { TaskType } from "~utils/types/Task";
import { useCallback, useState } from "react";
import { formatDate, formatTime } from "~utils/tools";
import { useTranslation } from "next-i18next";
import DataNotFound from "~components/DataNotFound";
import { findLast } from "lodash";
import ActiveAvatar from "~components/ActiveAvatar";

interface TimelineType {
  name: string;
  color: string;
  tasks: TaskType[];
}

const TasksTimeline = function () {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const {
    setIsOpenTaskViewModal,
    setTaskViewModalData,
    setLastOffer,
    setProjectModal,
  } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
      setLastOffer: state.setLastOffer!,
      setProjectModal: state.setProjectModal!,
    }),
    shallow
  );

  const { tasks, statusColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      statusColors: state.statusColors!,
    }),
    shallow
  );

  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );
  const { tasksTimeline } = useStore(
    "analytics",
    (state) => ({
      tasksTimeline: state.tasksTimeline!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const activeTimeline = tasksTimeline.filter(
    (e: TimelineType) => e.tasks && e.tasks.length > 0
  );

  const [openedAccordion, setOpenedAccordion] = useState("Today");

  const openTaskModal = useCallback(
    (data: TaskType) => {
      setIsOpenTaskViewModal(true);
      setTaskViewModalData(data);
    },
    [setIsOpenTaskViewModal, setTaskViewModalData]
  );

  return (
    <Accordion
      variant="separated"
      defaultValue="Today"
      value={openedAccordion}
      onChange={(value: string) => setOpenedAccordion(value)}
    >
      {tasksTimeline.every((e) => e.tasks && e.tasks.length === 0) && (
        <DataNotFound />
      )}

      {activeTimeline.map((timelineItem: TimelineType, i: number) => (
        <Accordion.Item
          key={"timeline-" + timelineItem.name + i}
          value={t(`crm.${timelineItem.name}`)}
          sx={(theme: MantineTheme) => ({
            button: {
              width: "100%",
              backgroundColor:
                theme.colorScheme === "dark"
                  ? theme.colors[timelineItem.color][8]
                  : theme.colors[timelineItem.color][6],
              color: theme.colors.gray[0],
              borderRadius: theme.radius.md,
              borderBottomLeftRadius:
                openedAccordion === t(`crm.${timelineItem.name}`)
                  ? "0"
                  : theme.radius.md,
              borderBottomRightRadius:
                openedAccordion === t(`crm.${timelineItem.name}`)
                  ? "0"
                  : theme.radius.md,
              transition: "all .2s ease",
              ":hover": {
                backgroundColor:
                  theme.colorScheme === "dark"
                    ? theme.colors[timelineItem.color][9]
                    : theme.colors[timelineItem.color][7],
              },
            },
            border: "none",
            marginTop: i > 0 ? theme.spacing.md : "0",
          })}
        >
          <Accordion.Control>{t(`crm.${timelineItem.name}`)}</Accordion.Control>
          <Accordion.Panel
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[2],
              borderBottomLeftRadius: theme.radius.md,
              borderBottomRightRadius: theme.radius.md,
            })}
          >
            {/* <Timeline active={-1} bulletSize={20} lineWidth={2} pt="md">
              {tasks
                .filter((e) => timelineItem.tasks.includes(e.id))
                .map((task: TaskType, i: number) => (
                  <Timeline.Item
                    key={"timelineTask-" + i}
                    title={
                      <Title
                        order={5}
                        sx={(theme) => ({
                          lineHeight: 1,
                          cursor: "pointer",
                          ":hover": {
                            color: theme.colors.gray[5],
                          },
                        })}
                        onClick={() => openTaskModal(task)}
                      >
                        {task.title}
                      </Title>
                    }
                    sx={{
                      display: "flex",
                      flexDirection: "column",
                      alignItems: "flex-start",
                    }}
                  >
                    <Group py="xs" spacing="xs">
                      <Badge color={statusColors[task.status]} size="xs">
                        {task.status}
                      </Badge>
                      <Group spacing={4}>
                        <Text color="dimmed" size="xs">
                          {t("crm.project")}:
                        </Text>
                        <Anchor size="xs">
                          {
                            offers.filter(
                              (x) =>
                                projects
                                  .filter((e) => e.id == task.projectId)[0]
                                  .offerIds.slice(-1)[0] === x.id
                            )[0].name
                          }
                        </Anchor>
                      </Group>
                      <Group spacing={4}>
                        <Text color="dimmed" size="xs">
                          {t("crm.endDate")}:
                        </Text>
                        <Text size="xs">{formatDate(task.end)}</Text>
                      </Group>
                    </Group>
                    <Group spacing={4}>
                      <Text color="dimmed" size="xs">
                        {t("crm.assignees")}:
                      </Text>
                      <Avatar.Group spacing="sm">
                        {task.assigneeIds.map(
                          (assigneeId: number, i: number) => {
                            const activeUser = users.find(
                              (e) => e.id == assigneeId
                            );

                            return (
                              <Tooltip
                                key={"taskAssignee-" + i}
                                label={`${activeUser?.name} ${activeUser?.name}`}
                                position="bottom-start"
                              >
                                <ActiveAvatar
                                  userId={assigneeId}
                                  size="sm"
                                  radius="xl"
                                />
                              </Tooltip>
                            );
                          }
                        )}
                      </Avatar.Group>
                    </Group>
                    <Space h="xs" />
                    <Group sx={{ flexWrap: "nowrap" }} spacing="xs">
                      <Text size="xs" sx={{ flexShrink: 0 }} weight={600}>
                        {task.progress}%
                      </Text>
                      <Progress
                        sx={{ width: "100%" }}
                        value={task.progress}
                        size="md"
                        radius="xl"
                      />
                    </Group>
                    <Space h="xs" />
                  </Timeline.Item>
                ))}
            </Timeline> */}

            {timelineItem.tasks.map((task: TaskType, ind: number) => {
              const project = projects.find((e) => e.id === task.projectId);
              const lastOffer = offers.find(
                (e) => e.id === project?.offerIds.at(-1)
              );
              const request = requests.find((e) => e.id === project?.requestId);
              const customer = customers.find(
                (e) => e.id === request?.customerId
              );

              return (
                <Stack key={`task-${task.id}`} spacing={0}>
                  <Stack spacing={8} mt={4}>
                    <Group>
                      <Text
                        size="md"
                        weight={600}
                        sx={{
                          cursor: "pointer",
                          transition: "filter .2s ease",
                          ":hover": {
                            filter: "brightness(.9)",
                          },
                        }}
                        onClick={() => openTaskModal(task)}
                      >
                        {task.title}
                      </Text>
                    </Group>
                    <Group align="baseline" spacing="xs">
                      <Badge size="sm" color={statusColors[task.status]}>
                        {task.status}
                      </Badge>
                      <Group spacing={8}>
                        <Text size="xs" weight={600} color="dimmed">
                          {t("profile.tasks.project")}
                        </Text>
                        <Anchor
                          size="xs"
                          weight={600}
                          onClick={() => {
                            setLastOffer(lastOffer!);
                            setProjectModal({
                              isOpen: true,
                              type: "edit",
                              isOwner:
                                customer?.personIds.includes(activeUserId),
                              data: project,
                            });
                          }}
                        >
                          {lastOffer?.name}
                        </Anchor>
                      </Group>
                      <Group spacing={8}>
                        <Text size="xs" weight={600} color="dimmed">
                          {t("profile.tasks.endDate")}
                        </Text>
                        <Text size="xs" weight={600}>
                          {formatDate(task.end)} {formatTime(task.end)}
                        </Text>
                      </Group>
                    </Group>
                  </Stack>
                  {ind !== timelineItem.tasks.length - 1 && (
                    <Divider mt="md" mb="sm" />
                  )}
                </Stack>
              );
            })}
          </Accordion.Panel>
        </Accordion.Item>
      ))}
    </Accordion>
  );
};

export default TasksTimeline;
