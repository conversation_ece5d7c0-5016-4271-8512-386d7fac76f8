import { useCallback } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Table,
  Box,
  Group,
  Text,
  Badge,
  Center,
  Title,
  useMantineTheme,
} from "@mantine/core";

import { TaskTagType, TaskType } from "~utils/types/Task";
import DashboardItemTemplate from "../DashboardItemTemplate";
import { useTranslation } from "next-i18next";
import { formatDate, formatTime } from "~utils/tools";

const MonthList = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const TasksTable = function () {
  const theme = useMantineTheme();
  const { tasks, filteredTasks, statusColors, priorityColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      filteredTasks: state.filteredTasks!,
      statusColors: state.statusColors!,
      priorityColors: state.priorityColors!,
    }),
    shallow
  );

  const { setIsOpenTaskViewModal, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );
  const { highPriorityTaskIds } = useStore(
    "analytics",
    (state) => ({
      highPriorityTaskIds: state.highPriorityTaskIds!,
    }),
    shallow
  );

  const openTaskView = useCallback(
    (task: TaskType) => {
      setIsOpenTaskViewModal(true);
      setTaskViewModalData(task);
    },
    [setIsOpenTaskViewModal, setTaskViewModalData]
  );

  const { t } = useTranslation();

  const highPriorityTasks = filteredTasks.filter((e) =>
    highPriorityTaskIds.includes(e.id)
  );

  const rows = highPriorityTasks.map((task) => {
    const endDate = formatDate(task.end);
    const endTime = formatTime(task.end);

    return (
      <tr
        key={task.id}
        onClick={() => openTaskView(task)}
        style={{
          position: "relative",
          cursor: "pointer",
        }}
      >
        <td>
          <Badge size="sm" color={statusColors[task.status]}>
            {t(`status.${task.status}`)}
          </Badge>
        </td>
        <td>
          <Text
            weight={600}
            sx={{
              maxWidth: 120,
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
            }}
          >
            {task.title}
          </Text>
        </td>
        {/* <td>
            <Group>
              {(task.tags || []).map((tag: TaskTagType, i) => (
                <Badge key={"taskTag-" + i} color={tag.color}>
                  {" "}
                  {tag.label}{" "}
                </Badge>
              ))}
            </Group>
          </td> */}
        <td align="right">
          <Badge color={priorityColors[task.priority]}>
            {endDate}, {endTime}
          </Badge>
        </td>
      </tr>
    );
  });

  return (
    <DashboardItemTemplate>
      <Text size="sm" weight={600} mb={4}>
        {t("highPriorityTasks")}
      </Text>
      {highPriorityTasks.length > 0 ? (
        <Table
          verticalSpacing="xs"
          sx={(theme) => ({
            tr: {
              borderRadius: "8px !important",
              ":hover:not(#table-header)": {
                background:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[5]
                    : theme.colors.gray[1],
              },
            },
            "td:first-of-type, th:first-of-type": {
              borderRadius: `${theme.spacing.sm}px 0 0 ${theme.spacing.sm}px`,
            },
            "td:last-child, th:last-child": {
              borderRadius: `0 ${theme.spacing.sm}px ${theme.spacing.sm}px 0`,
            },
          })}
        >
          <thead>
            <tr id="table-header">
              {/* <th style={{ width: 140 }}>{t("crm.tasksTable.status/title")}</th>
              <th style={{ flexGrow: 1 }}>{t("crm.tasksTable.tags")}</th>
              <th style={{ textAlign: "right" }}>
                {t("crm.tasksTable.deadline")}
              </th> */}

              <th>{t("offersModal.tasksTable.status")}</th>
              <th>{t("offersModal.tasksTable.title")}</th>
              <th style={{ textAlign: "right" }}>
                {t("offersModal.tasksTable.date")}
              </th>
            </tr>
          </thead>
          <tbody>{rows}</tbody>
        </Table>
      ) : (
        // <Center py="md">
        //   <Tex order={4}></Tex>
        // </Center>
        <Text align="center" size="sm" weight={600} color="dimmed" my="sm">
          {t("crm.tasksTable.noTask")}
        </Text>
      )}
    </DashboardItemTemplate>
  );
};

export default TasksTable;
