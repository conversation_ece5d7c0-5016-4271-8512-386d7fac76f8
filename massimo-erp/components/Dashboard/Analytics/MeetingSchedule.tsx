import { CalendarIcon } from "@heroicons/react/24/outline";
import { Avatar, Badge, Group, Stack, Text } from "@mantine/core";
import { findLast } from "lodash";
import { useTranslation } from "next-i18next";
import React from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import DashboardItemTemplate from "../DashboardItemTemplate";

export default function MeetingSchedule() {
  const { meetings } = useStore(
    "analytics",
    (state) => ({
      meetings: state.meetings!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack>
        <Text size="md" weight={600}>
          {t("crm.meetingSchedule")}
        </Text>
        {meetings.map((data: any, i: number) => {
          /*const findedFile = findLast(files, (file) =>
            (activeProfile?.fileIds || []).includes(file[0])
          );
          const avatar = findedFile ? findedFile[1] : undefined;*/
          return (
            <Group key={"meeting-" + i} position="apart" noWrap>
              <Group spacing="sm" noWrap>
                <Avatar src={data.avatar} />
                <Stack spacing={0} sx={{ display: "flex", width: "100%" }}>
                  <Text
                    size="sm"
                    weight={600}
                    sx={{
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                  >
                    {data.title}
                  </Text>
                  <Group spacing={4} noWrap>
                    <CalendarIcon style={{ width: 14, height: 14 }} />
                    <Text
                      size="xs"
                      weight={600}
                      color="dimmed"
                      sx={{
                        maxWidth: 120,
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                      }}
                    >
                      {data.subtitle}
                    </Text>
                  </Group>
                </Stack>
              </Group>
              <Badge color={data.chipColor} sx={{ flexShrink: 0 }}>
                {data.chipText}
              </Badge>
            </Group>
          );
        })}
        {meetings.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" align="center">
            {t("noData")}
          </Text>
        )}
      </Stack>
    </DashboardItemTemplate>
  );
}
