import {
  CreditCardIcon,
  DevicePhoneMobileIcon,
  SparklesIcon,
  WrenchScrewdriverIcon,
} from "@heroicons/react/24/outline";
import {
  Box,
  Divider,
  Group,
  Stack,
  Text,
  ThemeIcon,
  useMantineTheme,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

const projectIcons = {
  "IOS Application": DevicePhoneMobileIcon,
  "Web Application": SparklesIcon,
  "Bank Dashboard": CreditCardIcon,
  "UI Kit Design": WrenchScrewdriverIcon,
};

const Chart: CC = function () {
  const { projectTimeline } = useStore(
    "analytics",
    (state) => ({
      projectTimeline: state.projectTimeline!,
    }),
    shallow
  );

  const theme = useMantineTheme();

  const options: ApexOptions = {
    chart: {
      parentHeightOffset: 0,
      toolbar: {
        show: true,
        offsetX: 12,
        offsetY: -8,
        tools: { download: false },
      },
    },
    tooltip: { enabled: false },
    plotOptions: {
      bar: {
        barHeight: "60%",
        horizontal: true,
        borderRadius: 8,
        distributed: true,
      },
    },
    stroke: {
      width: 2,
      colors: [
        theme.colorScheme === "dark"
          ? theme.colors.dark[6]
          : theme.colors.gray[1],
      ],
    },
    colors: [
      hexToRGBA(theme.colors.red[9], 1),
      hexToRGBA(theme.colors.green[9], 1),
      hexToRGBA(theme.colors.gray[8], 1),
      hexToRGBA(theme.colors.blue[9], 1),
      hexToRGBA(theme.colors.yellow[9], 1),
    ],
    dataLabels: {
      enabled: true,
      formatter: (val, opts) => projectTimeline.labels[opts.dataPointIndex],
    },
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    legend: { show: false },
    grid: {
      strokeDashArray: 6,
      xaxis: {
        lines: { show: true },
      },
      yaxis: {
        lines: { show: false },
      },
      padding: {
        top: -22,
        left: 16,
        right: 18,
        bottom: 0,
      },
    },
    xaxis: {
      type: "datetime",
      axisTicks: { show: false },
      axisBorder: { show: false },
      labels: {
        datetimeFormatter: {
          year: "MMM",
          month: "MMM",
        },
      },
    },
    yaxis: {
      labels: {
        show: true,
        align: "left",
      },
    },
  };

  return (
    <ReactApexcharts
      height={230}
      type="rangeBar"
      series={projectTimeline.series}
      options={options}
    />
  );
};

export default function ProjectTimeline({ seperate }: any) {
  const theme = useMantineTheme();

  const { projectTimeline } = useStore(
    "analytics",
    (state) => ({
      projectTimeline: state.projectTimeline!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  return (
    <DashboardItemTemplate>
      <Box
        sx={{
          position: "relative",
          width: "100%",
          display: "flex",
          justifyContent: "stretch",
          alignItems: "stretch",
          flexDirection: seperate ? "column" : "row",
        }}
        ref={ref}
      >
        <Stack
          sx={{
            width: !seperate ? width - 232 : "100%",
          }}
        >
          <Stack spacing={0}>
            <Text size="md" weight={600}>
              {t("crm.projectTimeline.title")}
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              {t("crm.projectTimeline.total")} 840{" "}
              {t("crm.projectTimeline.taskCompleted")}
            </Text>
          </Stack>
          <Box
            sx={{
              "& .apexcharts-data-labels .apexcharts-datalabel": {
                fill: theme.colors.gray[3],
              },
              "& .apexcharts-canvas": {
                "& .apexcharts-yaxis-label": { fontSize: "0.875rem" },
                "& .apexcharts-xaxis-label": {
                  letterSpacing: "0.4px",
                  fill: theme.colors.gray[1],
                },
              },
            }}
          >
            <Chart />
          </Box>
        </Stack>
        <Divider orientation="vertical" mx="md" />
        <Stack
          sx={{
            flexShrink: 0,
            width: 200,
            float: "right",
          }}
          spacing="lg"
        >
          <Stack spacing={0}>
            <Text size="md" weight={600}>
              Project List
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              4 Ongoing Project
            </Text>
          </Stack>
          <Stack>
            {projectTimeline.projectList.map((e: any, i: number) => {
              const Icon = projectIcons[e.title as keyof typeof projectIcons];

              return (
                <Group key={"project-" + i}>
                  <ThemeIcon variant="light" color={e.color} size="xl">
                    <Icon style={{ width: 20, height: 20 }} />
                  </ThemeIcon>
                  <Stack spacing={0}>
                    <Text size="sm" weight={600}>
                      IOS Application
                    </Text>
                    <Text size="xs" weight={600} color="dimmed">
                      Task: 840/2.5k
                    </Text>
                  </Stack>
                </Group>
              );
            })}
          </Stack>
        </Stack>
      </Box>
    </DashboardItemTemplate>
  );
}
