import { Box, Group, Stack, Text, useMantineTheme } from "@mantine/core";
import { useForceUpdate } from "@mantine/hooks";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import React, { useEffect } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

const Chart: CC<{
  organicSessions: {
    name: string;
    value: number;
  }[];
}> = function ({ organicSessions }) {
  const theme = useMantineTheme();
  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      sparkline: { enabled: true },
      toolbar: { show: false },
    },
    colors: [
      theme.colors.teal[9],
      hexToRGBA(theme.colors.teal[9], 0.8),
      hexToRGBA(theme.colors.teal[9], 0.6),
      hexToRGBA(theme.colors.teal[9], 0.4),
      hexToRGBA(theme.colors.teal[9], 0.2),
    ],
    legend: { show: false },
    tooltip: { enabled: false },
    dataLabels: { enabled: false },
    stroke: {
      width: 3,
      lineCap: "round",
      colors: [
        theme.colorScheme === "dark"
          ? theme.colors.dark[6]
          : theme.colors.gray[1],
      ],
    },
    labels: organicSessions.map((e) => t(`status.${e.name}`)),
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    plotOptions: {
      pie: {
        endAngle: 130,
        startAngle: -130,
        customScale: 0.9,
        donut: {
          size: "83%",
          labels: {
            show: true,
            name: {
              offsetY: 25,
            },
            value: {
              offsetY: -15,
              formatter: (value) => value,
            },
            total: {
              show: true,
              label: "2022",
              formatter: (value) =>
                `${value.globals.seriesTotals.reduce(
                  (total: number, num: number) => total + num
                )}`,
            },
          },
        },
      },
    },
  };

  const forceUpdate = useForceUpdate();

  useEffect(() => {
    forceUpdate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ReactApexcharts
      type="donut"
      height={240}
      options={options}
      series={organicSessions.map((e) => e.value)}
    />
  );
};

export default function OrganicSessions() {
  const { isNavbarHover, isNavbarMinimized } = useStore(
    "global",
    (state) => ({
      isNavbarHover: state.isNavbarHover,
      isNavbarMinimized: state.isNavbarMinimized,
    }),
    shallow
  );
  const { organicSessions } = useStore(
    "analytics",
    (state) => ({
      organicSessions: state.organicSessions!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();
  const forceUpdate = useForceUpdate();

  useEffect(() => {
    forceUpdate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isNavbarHover, isNavbarMinimized]);

  return (
    <DashboardItemTemplate>
      <Stack spacing={4} justify="space-between" sx={{ height: "100%" }}>
        <Text size="md" weight={600}>
          {t("analytics.OrganicSessions")}
        </Text>
        <Box
          sx={{
            "& .apexcharts-datalabel-value": {
              fontSize: "2rem !important",
            },
            "& .apexcharts-datalabel-label": {
              fontSize: "1rem !important",
              fill:
                theme.colorScheme === "dark"
                  ? `${theme.colors.gray[6]} !important`
                  : `${theme.colors.dark[8]} !important`,
            },
          }}
        >
          <Chart organicSessions={organicSessions} />
        </Box>
        <Group position="center">
          {organicSessions.map((e: any, i: number) => (
            <Group spacing={8} key={"organicSession-" + i}>
              <Box
                sx={{
                  width: 12,
                  height: 12,
                  borderRadius: "50%",
                  background: hexToRGBA(
                    theme.colors.teal[9],
                    1 - (i - 1) * 0.2
                  ),
                }}
              />
              <Text size="xs" weight={600} color="dimmed">
                {t(`status.${e.name}`)}
              </Text>
            </Group>
          ))}
        </Group>
      </Stack>
    </DashboardItemTemplate>
  );
}
