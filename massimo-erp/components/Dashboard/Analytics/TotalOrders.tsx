import { ChevronUpIcon, ShoppingCartIcon } from "@heroicons/react/24/outline";
import { Group, Stack, ThemeIcon, Box, Text, Badge } from "@mantine/core";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import DashboardItemTemplate from "../DashboardItemTemplate";

export default function TotalOrdersAndSales() {
  const { totalOrders } = useStore(
    "analytics",
    (state) => ({
      totalOrders: state.totalOrders!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack spacing={4} justify="space-between" sx={{ height: "100%" }}>
        <Stack spacing={4}>
          <Group position="apart">
            <ThemeIcon size="xl" variant="light" color="orange">
              <ShoppingCartIcon style={{ width: 24, height: 24 }} />
            </ThemeIcon>
            <Group align="flex-start">
              <Box
                sx={(theme) => ({
                  display: "flex",
                  alignItems: "center",
                  color: theme.colors.green[9],
                })}
              >
                <Text size="md" weight={600} mr={4}>
                  {totalOrders.growth}%
                </Text>
                <ChevronUpIcon style={{ width: 16, height: 16 }} />
              </Box>
            </Group>
          </Group>
          <Text size={26} weight={600} mt="xs">
            {totalOrders.value}
          </Text>
          <Text size="sm" weight={600} color="dimmed">
            {t("analytics.totalOrders")}
          </Text>
        </Stack>

        <Group mt="sm">
          <Badge variant="light" color="cyan">
            {t("analytics.last")} 4 {t("analytics.month")}
          </Badge>
        </Group>
      </Stack>
    </DashboardItemTemplate>
  );
}
