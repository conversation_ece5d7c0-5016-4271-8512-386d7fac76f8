import { ChevronUpIcon } from "@heroicons/react/24/outline";
import { Group, Stack, Box, Text, useMantineTheme } from "@mantine/core";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function TotalRevenue() {
  const theme = useMantineTheme();

  const { totalRevenue } = useStore(
    "analytics",
    (state) => ({
      totalRevenue: state.totalRevenue!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    grid: {
      padding: {
        top: -15,
        left: -14,
        right: -4,
        bottom: -11,
      },
      yaxis: {
        lines: { show: false },
      },
    },
    legend: { show: false },
    dataLabels: { enabled: false },
    colors: [
      hexToRGBA(theme.colors.green[9], 1),
      hexToRGBA(theme.colors.red[9], 1),
    ],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "45%",
      },
    },
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    xaxis: {
      labels: { show: true },
      axisTicks: { show: false },
      axisBorder: { show: false },
      categories: ["Jan", "Feb", "Mar", "Apr"],
    },
    yaxis: {
      labels: { show: false },
    },
  };

  return (
    <DashboardItemTemplate>
      <Stack
        spacing={4}
        sx={{
          "& .apexcharts-canvas .apexcharts-datalabel-value": {
            fontWeight: 600,
            fontSize: "1rem !important",
            fill:
              theme.colorScheme === "dark"
                ? theme.colors.gray[4]
                : theme.colors.dark[8],
          },
        }}
      >
        <Group position="apart">
          <Text size={26} weight={600}>
            ₺{totalRevenue.value}
          </Text>
          <Box
            sx={(theme) => ({
              display: "flex",
              alignItems: "center",
              color: theme.colors.green[9],
            })}
          >
            <Text size="md" weight={600} mr={4}>
              {totalRevenue.growth}%
            </Text>
            <ChevronUpIcon style={{ width: 16, height: 16 }} />
          </Box>
        </Group>
        <Text size="sm" weight={600} color="dimmed">
          {t("analytics.totalRevenue")}
        </Text>
        <ReactApexcharts
          type="bar"
          height={120}
          options={options}
          series={totalRevenue.series}
        />
      </Stack>
    </DashboardItemTemplate>
  );
}
