import { ChevronUpIcon } from "@heroicons/react/24/outline";
import { Group, Stack, Box, Text, useMantineTheme } from "@mantine/core";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function TotalGrowth() {
  const theme = useMantineTheme();

  const { totalGrowth } = useStore(
    "analytics",
    (state) => ({
      totalGrowth: state.totalGrowth!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      toolbar: {
        show: true,
        tools: {
          download: false,
          selection: true,
          zoom: true,
          zoomin: true,
          zoomout: true,
          pan: true,
          reset: true,
          customIcons: [],
        },
        offsetX: 12,
        offsetY: -8,
      },
    },
    legend: { show: false },
    stroke: {
      width: 5,
      colors: [
        theme.colorScheme === "dark"
          ? theme.colors.dark[6]
          : theme.colors.gray[0],
      ],
    },
    colors: [
      hexToRGBA(theme.colors.blue[9], 1),
      hexToRGBA(theme.colors.green[9], 1),
      hexToRGBA(theme.colors.gray[8], 1),
    ],
    labels: [
      `${new Date().getFullYear()}`,
      `${new Date().getFullYear() - 1}`,
      `${new Date().getFullYear() - 2}`,
    ],
    tooltip: {
      y: { formatter: (val: number) => `${val}%` },
    },
    dataLabels: {
      enabled: false,
    },
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    plotOptions: {
      pie: {
        donut: {
          size: "70%",
          labels: {
            show: true,
            name: { show: false },
            total: {
              label: "",
              show: true,
              formatter(val: string) {
                return typeof val === "string" ? `${val}%` : "0%";
              },
            },
            value: {
              offsetY: 6,
              formatter(val: string) {
                return `${val}%`;
              },
            },
          },
        },
      },
    },
  };

  return (
    <DashboardItemTemplate>
      <Stack
        spacing={4}
        sx={{
          "& .apexcharts-canvas .apexcharts-datalabel-value": {
            fontWeight: 600,
            fontSize: "1rem !important",
            fill:
              theme.colorScheme === "dark"
                ? theme.colors.gray[4]
                : theme.colors.dark[8],
          },
        }}
      >
        <Group position="apart">
          <Text size={26} weight={600}>
            ₺{totalGrowth.value}
          </Text>
          <Box
            sx={(theme) => ({
              display: "flex",
              alignItems: "center",
              color: theme.colors.green[9],
            })}
          >
            <Text size="md" weight={600} mr={4}>
              {totalGrowth.growth}%
            </Text>
            <ChevronUpIcon style={{ width: 16, height: 16 }} />
          </Box>
        </Group>
        <Text size="sm" weight={600} color="dimmed">
          {t("analytics.totalGrowth")}
        </Text>
        <ReactApexcharts
          type="donut"
          height={148}
          options={options}
          series={totalGrowth.series}
        />
      </Stack>
    </DashboardItemTemplate>
  );
}
