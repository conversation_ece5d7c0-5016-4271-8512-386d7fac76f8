import {
  Stack,
  Box,
  Text,
  useMantineTheme,
  Group,
  Button,
} from "@mantine/core";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function WeekslyOverview() {
  const { weekslyOverview } = useStore(
    "analytics",
    (state) => ({
      weekslyOverview: state.weekslyOverview!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      offsetY: -9,
      offsetX: -16,
      parentHeightOffset: 0,
      toolbar: {
        show: true,
        offsetX: 12,
        offsetY: -8,
        tools: { download: false },
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 8,
        columnWidth: "35%",
        colors: {
          ranges: [
            {
              from: 40,
              to: 50,
              color: hexToRGBA(theme.colors.blue[9], 1),
            },
          ],
        },
      },
    },
    markers: {
      size: 3.5,
      strokeWidth: 2,
      fillOpacity: 1,
      strokeOpacity: 1,
      colors: [
        theme.colorScheme === "dark"
          ? theme.colors.dark[5]
          : theme.colors.gray[1],
      ],
      strokeColors: hexToRGBA(theme.colors.blue[9], 1),
    },
    stroke: {
      width: [0, 2],
      colors: [
        hexToRGBA(
          theme.colorScheme === "dark"
            ? theme.colors.dark[6]
            : theme.colors.gray[0],
          1
        ),
        hexToRGBA(theme.colors.blue[9], 1),
      ],
    },
    legend: { show: false },
    grid: { strokeDashArray: 7 },
    dataLabels: { enabled: false },
    colors: [
      hexToRGBA(
        theme.colorScheme === "dark"
          ? theme.colors.dark[5]
          : theme.colors.gray[3],
        1
      ),
    ],
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    xaxis: {
      categories: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"],
      tickPlacement: "on",
      labels: { show: false },
      axisTicks: { show: false },
      axisBorder: { show: false },
    },
    yaxis: {
      min: 0,
      max: 12,
      show: true,
      tickAmount: 3,
      labels: {
        formatter: (value) =>
          `${value > 999 ? `${(value / 1000).toFixed(0)}` : value}`,
      },
    },
  };

  return (
    <DashboardItemTemplate>
      <Stack justify="space-between" sx={{ height: "100%" }}>
        <Text size="md" weight={600}>
          {t("analytics.weekslyOverview.title")}
        </Text>
        <Box
          sx={{
            "& .apexcharts-xcrosshairs.apexcharts-active": { opacity: 0 },
            "& .apexcharts-canvas .apexcharts-yaxis-label": {
              fontSize: "0.75rem",
              fill:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[1]
                  : theme.colors.dark[8],
            },
          }}
        >
          <ReactApexcharts
            type="line"
            height={250}
            series={weekslyOverview}
            options={options}
          />
        </Box>
      </Stack>
    </DashboardItemTemplate>
  );
}
