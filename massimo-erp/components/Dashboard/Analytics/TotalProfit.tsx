import { ArrowPathIcon, ChevronDownIcon } from "@heroicons/react/24/outline";
import { Group, Stack, Box, Text, useMantineTheme } from "@mantine/core";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function TotalProfitAndGrowth() {
  const { totalProfit } = useStore(
    "analytics",
    (state) => ({
      totalProfit: state.totalProfit!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      stacked: true,
      parentHeightOffset: 0,
      toolbar: {
        show: false,
        offsetX: 12,
        offsetY: -8,
      },
    },
    legend: { show: false },
    dataLabels: { enabled: false },
    colors: [
      hexToRGBA(theme.colors.gray[8], 1),
      hexToRGBA(theme.colors.blue[9], 1),
    ],
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "30%",
      },
    },
    grid: {
      padding: {
        top: -21,
        right: 0,
        left: -17,
        bottom: -16,
      },
      yaxis: {
        lines: { show: false },
      },
    },
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    xaxis: {
      labels: { show: false },
      axisTicks: { show: false },
      axisBorder: { show: false },
    },
    yaxis: {
      labels: { show: false },
    },
  };

  return (
    <DashboardItemTemplate>
      <Stack spacing={4}>
        <Group position="apart">
          <Text size={26} weight={600}>
            ₺{totalProfit.value}
          </Text>
          <Box
            sx={(theme) => ({
              display: "flex",
              alignItems: "center",
              color: theme.colors.green[9],
            })}
          >
            <Text size="md" weight={600} mr={4}>
              {totalProfit.growth}%
            </Text>
            <ChevronDownIcon style={{ width: 16, height: 16 }} />
          </Box>
        </Group>
        <Text size="sm" weight={600} color="dimmed">
          {t("analytics.totalProfit")}
        </Text>
        <ReactApexcharts
          type="bar"
          height={120}
          options={options}
          series={totalProfit.series}
        />
      </Stack>
    </DashboardItemTemplate>
  );
}
