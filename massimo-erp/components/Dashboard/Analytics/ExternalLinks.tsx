import { ChevronDownIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import {
  Stack,
  Text,
  Group,
  Badge,
  Box,
  Image,
  useMantineTheme,
} from "@mantine/core";

import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function ExternalLinks() {
  const theme = useMantineTheme();

  const { externalLink } = useStore(
    "analytics",
    (state) => ({
      externalLink: state.externalLink!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      stacked: true,
      parentHeightOffset: 0,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        columnWidth: "41%",
      },
    },
    xaxis: {
      labels: { show: true },
      axisTicks: { show: false },
      axisBorder: { show: false },
      categories: ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"],
    },
    yaxis: { show: false },
    colors: [
      hexToRGBA(theme.colors.blue[9], 1),
      hexToRGBA(theme.colors.gray[8], 1),
    ],
    grid: {
      strokeDashArray: 10,
      padding: {
        top: 0,
        left: -4,
        right: -5,
        bottom: -11,
      },
    },
    legend: { show: false },
    dataLabels: { enabled: false },
    stroke: {
      width: 6,
      curve: "smooth",
      lineCap: "round",
      colors: [
        theme.colorScheme === "dark"
          ? theme.colors.dark[6]
          : theme.colors.gray[1],
      ],
    },
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    responsive: [
      {
        breakpoint: theme.breakpoints.xl,
        options: {
          plotOptions: {
            bar: {
              columnWidth: "50%",
            },
          },
        },
      },
      {
        breakpoint: theme.breakpoints.lg,
        options: {
          plotOptions: {
            bar: {
              columnWidth: "50%",
            },
          },
        },
      },
      {
        breakpoint: theme.breakpoints.sm,
        options: {
          plotOptions: {
            bar: {
              columnWidth: "35%",
            },
          },
        },
      },
      {
        breakpoint: 430,
        options: {
          plotOptions: {
            bar: {
              columnWidth: "45%",
            },
          },
        },
      },
    ],
  };

  return (
    <DashboardItemTemplate>
      <Stack justify="space-between" sx={{ height: "100%" }}>
        <Stack>
          <Text size="md" weight={600}>
            {t("analytics.externalLinks")}
          </Text>
          <Box
            sx={{
              "& .apexcharts-xcrosshairs.apexcharts-active": { opacity: 0 },
              marginBottom: 4,
            }}
          >
            <ReactApexcharts
              type="bar"
              height={203}
              series={externalLink.series}
              options={options}
            />
          </Box>
        </Stack>
        <Stack spacing="xs">
          {externalLink.data.map((data: any, i: number) => (
            <Group key={"externalLinkData-" + i} position="apart" noWrap>
              <Group noWrap>
                <Box
                  sx={{
                    width: 12,
                    height: 12,
                    borderRadius: "50%",
                    background: theme.colors[data.color][7],
                  }}
                />
                <Text size="sm" weight={600}>
                  {data.title}
                </Text>
              </Group>
              <Text size="sm" weight={600} color="dimmed">
                {data.amount}
              </Text>
              <Group noWrap spacing={8}>
                <Text size="md" weight={600}>
                  {data.trendAmount}%
                </Text>
                {data.color === "green" ? (
                  <ChevronUpIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors.green[9],
                    }}
                  />
                ) : (
                  <ChevronDownIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors.red[9],
                    }}
                  />
                )}
              </Group>
            </Group>
          ))}
        </Stack>
      </Stack>
    </DashboardItemTemplate>
  );
}
