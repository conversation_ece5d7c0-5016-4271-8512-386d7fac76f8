import { ChevronRightIcon } from "@heroicons/react/24/outline";
import {
  Stack,
  Box,
  Text,
  useMantineTheme,
  Group,
  ThemeIcon,
} from "@mantine/core";
import { ApexOptions } from "apexcharts";
import { useTranslation } from "next-i18next";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

const ReactApexcharts = dynamic(() => import("react-apexcharts"), {
  ssr: false,
});

export default function VisitsByDay() {
  const { visitsByDay } = useStore(
    "analytics",
    (state) => ({
      visitsByDay: state.visitsByDay!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();

  const options: ApexOptions = {
    chart: {
      parentHeightOffset: 0,
      toolbar: { show: false },
    },
    plotOptions: {
      bar: {
        borderRadius: 8,
        distributed: true,
        columnWidth: "51%",
      },
    },
    legend: { show: false },
    dataLabels: { enabled: false },
    colors: [
      hexToRGBA(theme.colors.yellow[9], 0.1),
      hexToRGBA(theme.colors.yellow[9], 1),
      hexToRGBA(theme.colors.yellow[9], 0.1),
      hexToRGBA(theme.colors.yellow[9], 1),
      hexToRGBA(theme.colors.yellow[9], 1),
      hexToRGBA(theme.colors.yellow[9], 0.1),
      hexToRGBA(theme.colors.yellow[9], 0.1),
    ],
    states: {
      hover: {
        filter: { type: "none" },
      },
      active: {
        filter: { type: "none" },
      },
    },
    xaxis: {
      axisTicks: { show: false },
      axisBorder: { show: false },
      categories: ["S", "M", "T", "W", "T", "F", "S"],
    },
    yaxis: { show: false },
    grid: {
      show: false,
      padding: {
        top: -30,
        left: -7,
        right: -4,
      },
    },
  };

  return (
    <DashboardItemTemplate>
      <Stack>
        <Stack spacing={0}>
          <Text size="md" weight={600}>
            {t("analytics.visitsByDay.title")}
          </Text>
          <Text size="sm" weight={600} color="dimmed">
            {t("analytics.visitsByDay.total")} {visitsByDay.total}
          </Text>
        </Stack>
        <Box
          sx={{
            "& .apexcharts-canvas .apexcharts-text": {
              fill:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[3]
                  : theme.colors.dark[7],
            },
            paddingTop: theme.spacing.lg,
          }}
        >
          <ReactApexcharts
            type="bar"
            height={215}
            options={options}
            series={visitsByDay.series}
          />
        </Box>
        <Group position="apart">
          <Stack spacing={0}>
            <Text size="sm" weight={600}>
              {t("analytics.visitsByDay.mostVisitedDay")}
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              {t("analytics.visitsByDay.total")}{" "}
              {visitsByDay.mostVisitedDay.value} {t("analytics.visitsByDay.message")}
              {visitsByDay.mostVisitedDay.name}
            </Text>
          </Stack>
          <ThemeIcon variant="light" color="blue" size="lg">
            <ChevronRightIcon style={{ width: 20, height: 20 }} />
          </ThemeIcon>
        </Group>
      </Stack>
    </DashboardItemTemplate>
  );
}
