import { BanknotesIcon, ChevronUpIcon } from "@heroicons/react/24/outline";
import { Group, Stack, ThemeIcon, Box, Text, Badge } from "@mantine/core";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import DashboardItemTemplate from "../DashboardItemTemplate";

export default function TotalSales() {
  const { totalSales } = useStore(
    "analytics",
    (state) => ({
      totalSales: state.totalSales!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack spacing={4} justify="space-between" sx={{ height: "100%" }}>
        <Stack spacing={4}>
          <Group position="apart">
            <ThemeIcon size="xl" variant="light" color="green">
              <BanknotesIcon style={{ width: 24, height: 24 }} />
            </ThemeIcon>
            <Group align="flex-start">
              <Box
                sx={(theme) => ({
                  display: "flex",
                  alignItems: "center",
                  color: theme.colors.green[9],
                })}
              >
                <Text size="md" weight={600} mr={4}>
                  {totalSales.growth}%
                </Text>
                <ChevronUpIcon style={{ width: 16, height: 16 }} />
              </Box>
            </Group>
          </Group>
          <Text size={26} weight={600} mt="xs">
            ₺{totalSales.value}
          </Text>
          <Text size="sm" weight={600} color="dimmed">
            {t("analytics.totalSales")}
          </Text>
        </Stack>
        <Group mt="sm">
          <Badge variant="light" color="teal">
            {t("analytics.last")} 6 {t("analytics.month")}
          </Badge>
        </Group>
      </Stack>
    </DashboardItemTemplate>
  );
}
