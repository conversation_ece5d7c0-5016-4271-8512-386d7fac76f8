import { ChevronUpIcon, LinkIcon } from "@heroicons/react/24/outline";
import { Group, Stack, ThemeIcon, Box, Text, Badge } from "@mantine/core";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import DashboardItemTemplate from "../DashboardItemTemplate";

export default function TotalImpressions() {
  const { totalImpressions } = useStore(
    "analytics",
    (state) => ({
      totalImpressions: state.totalImpressions!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack spacing={4} justify="space-between" sx={{ height: "100%" }}>
        <Stack spacing={4}>
          <Group position="apart">
            <ThemeIcon size="xl" variant="light" color="blue">
              <LinkIcon style={{ width: 24, height: 24 }} />
            </ThemeIcon>
          </Group>
          <Text size={26} weight={600} mt="xs">
            {totalImpressions || 0}
          </Text>
          <Text size="sm" weight={600} color="dimmed">
            {t("analytics.totalImpressions")}
          </Text>
        </Stack>

        <Group mt="sm">
          <Badge variant="light" color="grape">
            {t("analytics.last")} 1 {t("analytics.year")}
          </Badge>
        </Group>
      </Stack>
    </DashboardItemTemplate>
  );
}
