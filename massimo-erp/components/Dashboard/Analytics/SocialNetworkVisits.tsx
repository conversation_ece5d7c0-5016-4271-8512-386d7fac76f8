import { ChevronUpIcon } from "@heroicons/react/24/outline";
import { Stack, Text, Group, Badge, Image } from "@mantine/core";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import DashboardItemTemplate from "../DashboardItemTemplate";

export default function SocialNetworkVisits() {
  const { socialNetworkVisits } = useStore(
    "analytics",
    (state) => ({
      socialNetworkVisits: state.socialNetworkVisits!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <DashboardItemTemplate>
      <Stack>
        <Text size="md" weight={600}>
          {t("analytics.socialNetwork")}
        </Text>
        <Stack spacing={4}>
          <Group>
            <Text size={28} weight={600}>
              {socialNetworkVisits.data.value}
            </Text>
            <Group
              sx={(theme) => ({
                color: theme.colors.green[9],
              })}
              spacing={4}
            >
              <ChevronUpIcon style={{ width: 16, height: 16 }} />
              <Text size="md" weight={600}>
                {socialNetworkVisits.data.growUp}%
              </Text>
            </Group>
          </Group>
          <Text size="xs" weight={600} color="dimmed">
            Last 1 Year Visits
          </Text>
        </Stack>
        {socialNetworkVisits.series.map((data: any, i: number) => (
          <Group key={"visit-" + i} position="apart">
            <Group>
              <Image
                src={data.imgSrc}
                width={34}
                alt={data.imgAlt}
                withPlaceholder
              />
              <Stack spacing={0}>
                <Text size="sm" weight={600}>
                  {data.title}
                </Text>
                <Text size="xs" weight={600} color="dimmed">
                  {data.subtitle}
                </Text>
              </Stack>
            </Group>
            <Group>
              <Text size="md" weight={600}>
                {data.amount}
              </Text>
              <Badge color={data.chipColor}>{data.chipText}</Badge>
            </Group>
          </Group>
        ))}
      </Stack>
    </DashboardItemTemplate>
  );
}
