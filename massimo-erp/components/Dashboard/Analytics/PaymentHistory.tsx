import { Stack, Text, Table, Group, Image, Box } from "@mantine/core";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate } from "~utils/tools";
import DashboardItemTemplate from "../DashboardItemTemplate";

export default function PaymentHistory() {
  const { paymentHistory } = useStore(
    "analytics",
    (state) => ({
      paymentHistory: state.paymentHistory!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const rows = paymentHistory.map((element) => (
    <tr key={element.id}>
      <td>
        <Group noWrap>
          <Box
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[7]
                  : theme.colors.gray[3],
              padding: 8,
              borderRadius: 4,
            })}
          >
            <Image
              src={`/images/${element.card.type}.png`}
              alt="card-type"
              width={28}
              withPlaceholder
            />
          </Box>
          <Stack spacing={0}>
            <Text size="sm" weight={600}>
              *{element.card.last4Digits}
            </Text>
            <Text size="xs" color="dimmed">
              {element.card.method}
            </Text>
          </Stack>
        </Group>
      </td>
      <td>{formatDate(element.date)}</td>
      <td align="right">
        <Stack spacing={0}>
          <Text size="sm" weight={600}>
            -${element.spendings.spent}
          </Text>
          <Text size="sm" weight={600} color="dimmed">
            ${element.spendings.balance}
          </Text>
        </Stack>
      </td>
    </tr>
  ));

  return (
    <DashboardItemTemplate>
      <Stack>
        <Text size="md" weight={600}>
          {t("crm.paymentHistory.title")}
        </Text>
        <Table>
          <thead>
            <tr>
              <th>{t("crm.paymentHistory.card")}</th>
              <th>{t("crm.paymentHistory.date")}</th>
              <th style={{ textAlign: "right" }}>
                {t("crm.paymentHistory.spendings")}
              </th>
            </tr>
          </thead>
          <tbody>{rows}</tbody>
        </Table>
        {paymentHistory.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" align="center">
            {t("noData")}
          </Text>
        )}
      </Stack>
    </DashboardItemTemplate>
  );
}
