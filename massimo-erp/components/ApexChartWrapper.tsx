import { Box } from "@mantine/core";

export default function ApexChartWrapper(props: any) {
  return (
    <Box
      sx={(theme) => ({
        "& .apexcharts-canvas": {
          "& .apexcharts-gridline": {
            stroke:
              theme.colorScheme === "dark"
                ? `${theme.colors.gray[8]} !important`
                : `${theme.colors.dark[8]} !important`,
          },
          "& line[stroke='transparent']": {
            display: "none",
          },
          "& .apexcharts-xaxis > line, & .apexcharts-yaxis > line": {
            stroke:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
          },
          "& .apexcharts-xaxis-tick, & .apexcharts-yaxis-tick": {
            stroke:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
          },
          "& .apexcharts-tooltip": {
            borderColor:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
            "& .apexcharts-tooltip-title": {
              fontWeight: 600,
              borderColor:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[3],
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[3],
            },
            "&.apexcharts-theme-dark": {
              "& .apexcharts-tooltip-text-label, & .apexcharts-tooltip-text-value":
                {
                  color: theme.colors.gray[1],
                },
            },
            "& .bar-chart": {
              padding: theme.spacing.md,
            },
          },
          "& .apexcharts-xaxistooltip": {
            borderColor:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
            background:
              theme.colorScheme === "light"
                ? theme.colors.gray[3]
                : theme.colors.dark[8],
            "& .apexcharts-xaxistooltip-text": {
              color:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[1]
                  : theme.colors.dark[9],
            },
            "&:after": {
              borderBottomColor:
                theme.colorScheme === "light"
                  ? theme.colors.gray[3]
                  : theme.colors.dark[8],
            },
            "&:before": {
              borderBottomColor:
                theme.colorScheme === "light"
                  ? theme.colors.gray[3]
                  : theme.colors.dark[8],
            },
          },
          "& .apexcharts-yaxistooltip": {
            borderColor:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[3],
            background:
              theme.colorScheme === "light"
                ? theme.colors.gray[3]
                : theme.colors.dark[8],
            "& .apexcharts-yaxistooltip-text": {
              color: theme.colors.dark[9],
            },
            "&:after": {
              borderLeftColor:
                theme.colorScheme === "light"
                  ? theme.colors.gray[3]
                  : theme.colors.dark[8],
            },
            "&:before": {
              borderLeftColor:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[3],
            },
          },
          "& .apexcharts-yaxis .apexcharts-yaxis-texts-g .apexcharts-yaxis-label":
            {
              textAnchor: undefined,
            },
          "& .apexcharts-text, & .apexcharts-tooltip-text, & .apexcharts-datalabel-label, & .apexcharts-datalabel":
            {
              filter: "none",
              fontWeight: 400,
              fill:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[5]
                  : theme.colors.dark[5],
            },
          "& .apexcharts-pie-label": {
            filter: "none",
            fill:
              theme.colorScheme === "dark"
                ? theme.colors.gray[1]
                : theme.colors.dark[9],
          },
          "& .apexcharts-pie": {
            "& .apexcharts-datalabel-label, .apexcharts-datalabel-value": {
              fontSize: "1.5rem",
            },
          },
          "& .apexcharts-marker": {
            boxShadow: "none",
          },
          "& .apexcharts-legend-series": {
            margin: `${theme.spacing.xs}px ${theme.spacing.sm}px !important`,
            "& .apexcharts-legend-text": {
              marginLeft: theme.spacing.xs,
              color: `${
                theme.colorScheme === "dark"
                  ? theme.colors.dark[9]
                  : theme.colors.gray[1]
              } !important`,
            },
          },
          "& .apexcharts-xcrosshairs, & .apexcharts-ycrosshairs, & .apexcharts-gridline":
            {
              stroke:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[3],
            },
          "& .apexcharts-heatmap-rect": {
            stroke:
              theme.colorScheme === "light"
                ? theme.colors.dark[7]
                : theme.colors.dark[8],
          },
          "& .apexcharts-radialbar > g > g:first-of-type .apexcharts-radialbar-area":
            {
              stroke: theme.colors.dark[8],
            },
          "& .apexcharts-radar-series polygon": {
            stroke: theme.colors.dark[6],
            fill: theme.colors.dark[7],
          },
          "& .apexcharts-radar-series line": {
            stroke: theme.colors.dark[6],
          },
        },
      })}
    >
      {props.children}
    </Box>
  );
}
