import { ChevronLeftIcon, ChevronRightIcon } from "@heroicons/react/24/outline";
import { ActionIcon, Box, Group } from "@mantine/core";
import React from "react";
import { CC } from "~types";

const PostSliderControls: CC<{
  length: number;
  active: number;
  setActive: (value: number) => void;
}> = ({ length, active, setActive }) => {
  return (
    <Box>
      {active > 0 && (
        <ActionIcon
          size="md"
          variant="filled"
          color="gray"
          radius="xl"
          sx={{
            position: "absolute",
            top: "50%",
            left: 4,
            transform: "translateY(-50%) !important",
            zIndex: 10,
          }}
          onClick={() => setActive(--active)}
        >
          <ChevronLeftIcon width={20} height={20} />
        </ActionIcon>
      )}
      {active < length - 1 && (
        <ActionIcon
          size="md"
          variant="filled"
          color="gray"
          radius="xl"
          sx={{
            position: "absolute",
            top: "50%",
            right: 4,
            transform: "translateY(-50%) !important",
            zIndex: 10,
          }}
          onClick={() => setActive(++active)}
        >
          <ChevronRightIcon width={20} height={20} />
        </ActionIcon>
      )}
      {length > 1 && (
        <Group
          spacing={8}
          sx={(theme) => ({
            position: "absolute",
            bottom: 8,
            left: "50%",
            transform: "translateX(-50%)",
            zIndex: 10,
          })}
        >
          {Array(length)
            .fill(null)
            .map((id, i) => (
              <Box
                key={`dot-${i}`}
                sx={(theme) => ({
                  width: 8,
                  height: 8,
                  borderRadius: "50%",
                  background:
                    active === i ? theme.colors.gray[6] : theme.colors.gray[8],
                  cursor: "pointer",
                  transition: "background-color .1s ease, box-shadow .1s ease",
                })}
                onClick={() => setActive(i)}
              />
            ))}
        </Group>
      )}
    </Box>
  );
};

export default PostSliderControls;
