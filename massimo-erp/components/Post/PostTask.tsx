import { PostType } from "~utils/types/Post";

import { Paper, Group, Box, Tooltip, Title, Text } from "@mantine/core";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { formatDate } from "~utils/tools";
import { TaskType } from "~utils/types/Task";
import { useTranslation } from "next-i18next";

interface PropType {
  taskId: TaskType["id"];
}

export default function PostTask(props: PropType) {
  const { taskId } = props;

  const { tasks, priorityColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { setIsOpenTaskViewModal, setTaskViewModalData, setPostViewModal } =
    useStore(
      "temp",
      (state) => ({
        setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
        setTaskViewModalData: state.setTaskViewModalData!,
        setPostViewModal: state.setPostViewModal!,
      }),
      shallow
    );

  const { t } = useTranslation();
  const activeTask = tasks.find((e) => e.id === taskId);

  return (
    <Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[6]
            : theme.colors.gray[3],
        cursor: "pointer",
        transition: "filter .1s ease",
        ":hover": {
          filter: "brightness(.9)",
        },
      })}
      px="md"
      py="xs"
      m="sm"
      mb={0}
      onClick={() => {
        if (!activeTask) {
          return;
        }

        setTaskViewModalData(activeTask);
        setIsOpenTaskViewModal(true);
        setPostViewModal({
          isOpen: false,
        });
      }}
    >
      <Group align="baseline">
        <Tooltip
          label={
            `${t("newTask.priority.label")}: ` +
            t(`priority.${activeTask?.priority}`)
          }
        >
          <Box
            sx={(theme) => ({
              width: 10,
              height: 10,
              borderRadius: "50%",
              background: theme.colors[priorityColors["Medium"]][9],
            })}
          />
        </Tooltip>
        <Text size="sm" weight={600}>
          {activeTask?.title}
        </Text>
        {!!activeTask?.start && !!activeTask?.end && (
          <Text color="dimmed" size="xs" weight={600} sx={{ lineHeight: 1 }}>
            {formatDate(activeTask.start)} - {formatDate(activeTask.end)}
          </Text>
        )}
      </Group>
    </Paper>
  );
}
