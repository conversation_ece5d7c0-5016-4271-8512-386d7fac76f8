import { PaperAirplaneIcon } from "@heroicons/react/24/outline";
import { ActionIcon, Group, TextInput } from "@mantine/core";
import { getHotkey<PERSON><PERSON><PERSON>, useDebouncedState, useFocusWithin, useMergedRef } from "@mantine/hooks";
import React, { useCallback, useEffect, useRef, useState } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";

const SendCommentArea: CC<{
  postId: number;
}> = ({ postId }) => {
  const { activeUserId, setIsFocus } = useStore(
    "global",
    (state) => ({
      setIsFocus: state.setIsFocus!,
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { actionGetComments, actionUpdateComments } = useStore(
    "posts",
    (state) => ({
      actionGetComments: state.actionGetComments!,
      actionUpdateComments: state.actionUpdateComments!,
    }),
    shallow
  );

  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const [comment, setComment] = useDebouncedState("", 200, {
    leading: true,
  });
  const [isSending, setIsSending] = useState(false);
  const { ref, focused } = useFocusWithin();
  const mergedRef = useMergedRef(inputRef, ref);

  const sendComment = useCallback(async () => {
    if (comment === "") {
      return;
    }

    setIsSending(true);

    try {
      await actionUpdateComments([
        { post: postId, user: activeUserId, description: comment },
      ]);
    } finally {
      setIsSending(false);
      if (inputRef.current) {
        inputRef.current.value = "";
      }
    }
  }, [actionUpdateComments, activeUserId, comment, postId]);

  useEffect(() => {
    setIsFocus(focused);
  }, [focused])

  return (
    <Group noWrap>
      <TextInput
        ref={mergedRef as any}
        placeholder="Send a comment"
        defaultValue={comment}
        onChange={(event) => setComment(event.target.value)}
        onKeyDown={getHotkeyHandler([["Enter", sendComment]])}
        styles={{
          root: {
            width: "100%",
          },
          input: {
            background: "transparent",
            border: "none",
          },
        }}
      />
      <ActionIcon
        size="xl"
        loading={isSending}
        loaderProps={{ size: "xs" }}
        onClick={sendComment}
      >
        <PaperAirplaneIcon width={20} height={20} />
      </ActionIcon>
    </Group>
  );
};

export default SendCommentArea;
