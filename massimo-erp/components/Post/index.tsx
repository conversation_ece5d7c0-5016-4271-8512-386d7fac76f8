import shallow from "zustand/shallow";
import { cloneDeep } from "lodash";

import { useStore } from "~utils/store";

import {
  ChatBubbleLeftIcon,
  ChatBubbleOvalLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpIcon,
  HeartIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
  Square2StackIcon,
} from "@heroicons/react/24/outline";
import HeartIconFilled from "@heroicons/react/24/solid/HeartIcon";
import {
  Group,
  Paper,
  Stack,
  Text,
  ActionIcon,
  Divider,
  Textarea,
  Button,
  Space,
  Collapse,
  useMantineTheme,
  Anchor,
  Image,
  TextInput,
  Box,
  AspectRatio,
  Center,
} from "@mantine/core";
import PostHeader from "./PostHeader";
import PostTask from "./PostTask";
import { useCallback, useEffect, useMemo, useState } from "react";
import { getHotkeyHandler } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { CommentType, PostType } from "~utils/types/Post";
import { formatDate, formatTime } from "~utils/tools";
import PostFooter from "./PostFooter";
import PostSliderControls from "./PostSliderControls";
import SendCommentArea from "./SendCommentArea";
import { CC } from "~types";
import ActiveAvatar from "~components/ActiveAvatar";
import { useRouter } from "next/router";

const Post: CC<{
  post: PostType;
}> = function ({ post }) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setPostViewModal } = useStore(
    "temp",
    (state) => ({
      setPostViewModal: state.setPostViewModal!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { files, actionPaginateFiles } = useStore(
    "files",
    (state) => ({
      files: state.files!,
      actionPaginateFiles: state.actionPaginateFiles!,
    }),
    shallow
  );
  const { actionUpdatePostMeta } = useStore(
    "posts",
    (state) => ({
      actionUpdatePostMeta: state.actionUpdatePostMeta!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { push } = useRouter();

  const [activeIndex, setActiveIndex] = useState(0);

  const file = files.find((e) => e[0] === post.fileIds[0]) || [];
  const postUser = useMemo(
    () => users.find((e) => e.id === post.userId),
    [post, users]
  );

  const getFiles = useCallback(async () => {
    if (post.fileIds.length > 0) {
      await actionPaginateFiles(post.fileIds as number[]);
    }
  }, [actionPaginateFiles, post.fileIds]);

  const handleLike = useCallback(async () => {
    const isLiked = post.isLiked !== null;

    let rawData = isLiked
      ? {
          id: post.isLiked,
          post: post.id,
        }
      : {
          isLike: !post?.isLiked,
          isView: false,
          user: activeUserId,
          post: post.id,
        };

    await actionUpdatePostMeta([rawData], post.isLiked !== null);
  }, [actionUpdatePostMeta, activeUserId, post.id, post?.isLiked]);

  useEffect(() => {
    getFiles();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (post.fileIds.length < activeIndex) {
      setActiveIndex(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [post]);

  return (
    <Stack spacing="sm" sx={{ position: "relative" }}>
      {post?.fileIds?.length > 1 && (
        <Box
          sx={{
            position: "absolute",
            top: 0,
            right: 0,
            zIndex: 2,
          }}
          p="xs"
        >
          <Square2StackIcon style={{ width: 18, height: 18 }} />
        </Box>
      )}

      <AspectRatio
        ratio={4 / 3}
        sx={{
          background: `url(${file[2]}) no-repeat center center`,
          backgroundSize: "cover",
          width: "100%",
          position: "relative",
          borderTopLeftRadius: theme.radius.md,
          borderTopRightRadius: theme.radius.md,
          overflow: "hidden",
          cursor: "pointer"
        }}
      />

      <Group position="apart" noWrap>
        <Group spacing={8}>
          <ActiveAvatar userId={postUser?.id} size={30} radius="xl" />
          <Anchor
            sx={{
              color:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[3]
                  : theme.colors.dark[6],
            }}
            underline={false}
            size="xs"
            weight={600}
            onClick={() => push(`/profile/user/${postUser?.id}`)}
          >
            {postUser?.name} {postUser?.surname}
          </Anchor>
        </Group>

        <Group spacing={8}>
          <ActionIcon variant="light" radius="xl" onClick={handleLike}>
            {post?.isLiked !== null ? (
              <HeartIconFilled
                style={{
                  width: 18,
                  height: 18,
                  color: theme.colors.red[9],
                }}
              />
            ) : (
              <HeartIcon style={{ width: 18, height: 18 }} />
            )}
          </ActionIcon>
          <ActionIcon
            variant="light"
            radius="xl"
            onClick={() => {
              setPostViewModal({
                isOpen: true,
                postId: post?.id,
              });
            }}
          >
            <ChatBubbleOvalLeftIcon style={{ width: 18, height: 18 }} />
          </ActionIcon>
        </Group>
      </Group>
    </Stack>
  );
};

export default Post;

/*
<Box
        sx={{
          position: "relative",
          width: "100%",
          height: "100%",
          zIndex: 1
        }}
      >
        {post?.fileIds?.length > 1 && (
          <Box
            sx={{
              position: "absolute",
              top: 0,
              right: 0,
              zIndex: 2,  
            }}
            p="xs"
          >
            <Square2StackIcon style={{ width: 24, height: 24 }} />
          </Box>
        )}

        <Box
          sx={{
            position: "absolute",
            bottom: 0,
            left: 0,
            zIndex: 2,
          }}
          p={12}
        >
          <Group spacing={8}>
            <ActiveAvatar userId={postUser?.id} size={30} radius="xl" />
            <Text size="xs" weight={600}>
              {postUser?.name} {postUser?.surname}
            </Text>
          </Group>
        </Box>

        <Box
          sx={(theme) => ({
            position: "absolute",
            bottom: 0,
            left: 0,
            right: 0,
            height: 0,
            boxShadow: "0px 0px 60px 40px black",
            zIndex: 1,
          })}
        />
      </Box>

      <Center
        sx={{
          background: "#00000088",
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          opacity: 0,
          transition: "opacity .1s ease",
          zIndex: 2,

          ":hover": {
            opacity: 1,
          },
        }}
      >
        <Group spacing={10} noWrap>
          <Group spacing={4} noWrap>
            {post?.isLiked !== null ? (
              <HeartIconFilled
                style={{
                  width: 24,
                  height: 24,
                  color: theme.colors.red[9],
                }}
              />
            ) : (
              <HeartIcon style={{ width: 24, height: 24 }} />
            )}
            <Text size="sm" weight={600}>
              {post?.likeCount}
            </Text>
          </Group>

          <Group spacing={4} noWrap>
            <ChatBubbleOvalLeftIcon style={{ width: 24, height: 24 }} />
            <Text size="sm" weight={600}>
              {post?.commentCount}
            </Text>
          </Group>
        </Group>
      </Center>

*/

/*
<Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        overflow: "hidden",
      })}
      radius="md"
    >
      <Stack spacing={0}>
        <Box
          sx={(theme) => ({
            minHeight: 56,
            position: "relative",
            borderTopLeftRadius: theme.radius.md,
            borderTopRightRadius: theme.radius.md,
          })}
        >
          <Box
            sx={{
              position: "absolute",
              top: 0,
              left: 0,
              right: 0,
              zIndex: 12,
            }}
            p="sm"
          >
            <PostHeader post={post} />
          </Box>

          {hasImage && (
            <Box
              sx={{
                position: "relative",
                overflow: "hidden",
                minHeight: 200,
              }}
            >
              <Box
                sx={{
                  position: "absolute",
                  top: -1,
                  left: 0,
                  right: 0,
                  boxShadow: "0px 0px 80px 40px black",
                  zIndex: 10,
                }}
              />

              <Box
                sx={{
                  position: "absolute",
                  bottom: -1,
                  left: 0,
                  right: 0,
                  boxShadow: "0px 0px 35px 15px black",
                  zIndex: 9,
                }}
              />

              <PostSliderControls
                active={activeIndex}
                length={post.fileIds.length}
                setActive={(v) => setActiveIndex(v)}
              />

              <Image
                width="100%"
                src={file[2]}
                alt="img"
                withPlaceholder
                sx={{ minHeight: 200 }}
              />
            </Box>
          )}
        </Box>

        <Stack spacing="xs" p="sm" pt={hasImage ? "sm" : 0}>
          <PostFooter post={post} />

          {post.description !== "" && (
            <Group spacing={8} sx={{ order: hasImage ? 2 : 0 }}>
              {hasImage &&
                (!!activeUser ? (
                  <Text size="sm" weight={600}>
                    {activeUser?.name} {activeUser?.surname}
                  </Text>
                ) : (
                  <Text size="sm" weight={600}>
                    {activeCustomer?.fullName}
                  </Text>
                ))}
              <Text size="sm">{post.description}</Text>
            </Group>
          )}
        </Stack>

        <Divider />

        <SendCommentArea postId={post.id} />
      </Stack>
    </Paper>
*/
