import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Image,
  AspectRatio,
  Box,
  Group,
  ActionIcon,
  CloseButton,
  Avatar,
  Stack,
  Text,
  useMantineTheme,
  Spoiler,
  SimpleGrid,
  Grid,
  Divider,
  ScrollArea,
  Anchor,
  TextInput,
  Loader,
  Menu,
} from "@mantine/core";
import {
  useElementSize,
  useHotkeys,
  useIntersection,
  useMediaQuery,
  useOs,
  useViewportSize,
} from "@mantine/hooks";
import {
  ChatBubbleOvalLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EllipsisVerticalIcon,
  HeartIcon,
  PaperAirplaneIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import HeartIconFilled from "@heroicons/react/24/solid/HeartIcon";
import { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import ActiveAvatar from "~components/ActiveAvatar";
import { UserType } from "~utils/types/User";
import { CustomerType } from "~utils/types/Customer";
import { useTranslation } from "next-i18next";
import { formatDate, formatTime, wait } from "~utils/tools";
import PostHeader from "./PostHeader";
import { CC } from "~types";
import { CommentType, PostType } from "~utils/types/Post";
import Link from "next/link";
import PostFooter from "./PostFooter";
import PostSliderControls from "./PostSliderControls";
import PostTask from "./PostTask";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import SendCommentArea from "./SendCommentArea";

const CommentArea: CC<{
  postId: number;
  description: string;
  closeModal: () => void;
}> = function ({ postId, description, closeModal }) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { comments, actionGetComments, actionUpdateComments } = useStore(
    "posts",
    (state) => ({
      comments: state.comments!,
      actionGetComments: state.actionGetComments!,
      actionUpdateComments: state.actionUpdateComments!,
    }),
    shallow
  );

  const { push } = useRouter();
  const { t } = useTranslation();

  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRole = roles.find(
    (e) => e.isDefaultRole && e.id === activeUser?.defaultRoleId[0]
  );

  const getComments = useCallback(async () => {
    try {
      const hasMoreData = await actionGetComments(postId as number, page);
      setHasMore(hasMoreData);
    } finally {
      setPage((p) => p + 1);
    }
  }, [actionGetComments, page, postId]);

  const deleteComment = useCallback(
    async (id: number) => {
      try {
        await actionUpdateComments(
          [
            {
              id,
              post: postId,
            },
          ],
          true
        );
      } catch (err) {
        throw err;
      }
    },
    [actionUpdateComments, postId]
  );

  useEffect(() => {
    getComments();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CustomInfiniteScroll
      scrollableTarget="commentArea"
      dataLength={comments.length}
      hasMore={hasMore}
      next={getComments}
    >
      <Stack my="md" px="md">
        {description !== "" && (
          <Text size="sm" weight={600}>
            {description}
          </Text>
        )}

        {comments.map((comment: CommentType, i: number) => {
          const user = users.find((e) => e.id === comment.userId);

          const commentOwnerName = `${user?.name} ${user?.surname}`;
          const commentProfileLink = `/profile/user/${user?.id}`;

          if (!user) {
            return null;
          }

          return (
            <Group
              key={"comment-" + comment.id}
              spacing={12}
              noWrap
              align="flex-start"
              position="apart"
            >
              <Group>
                <ActiveAvatar userId={user?.id} radius="xl" size={30} />
                <Stack spacing={4}>
                  <Text size="sm" sx={{ width: "100%" }}>
                    <b
                      style={{ marginRight: 8, cursor: "pointer" }}
                      onClick={() => {
                        closeModal();
                        push(commentProfileLink);
                      }}
                    >
                      {commentOwnerName}
                    </b>
                    {comment.description}
                  </Text>
                  <Group>
                    <Text size="xs" color="dimmed">
                      {formatTime(comment.updatedAt)}{" "}
                      {formatDate(comment.updatedAt)}
                    </Text>
                  </Group>
                </Stack>
              </Group>

              {(activeUserDefaultRole?.weight || 0) < 0 && (
                <Menu width={150} position="bottom-end">
                  <Menu.Target>
                    <ActionIcon>
                      <EllipsisVerticalIcon width={20} height={20} />
                    </ActionIcon>
                  </Menu.Target>

                  <Menu.Dropdown>
                    <Menu.Item onClick={() => deleteComment(comment.id)}>
                      {t("delete")}
                    </Menu.Item>
                  </Menu.Dropdown>
                </Menu>
              )}
            </Group>
          );
        })}
      </Stack>
    </CustomInfiniteScroll>
  );
};

const PostViewModalContent: CC<{
  activePost?: PostType;
  hasImage?: boolean;
  closeModal: () => void;
}> = function ({ activePost, hasImage, closeModal }) {
  const { postViewModal } = useStore(
    "temp",
    (state) => ({
      postViewModal: state.postViewModal!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const { ref, width } = useElementSize();

  const [activeIndex, setActiveIndex] = useState(0);
  const file =
    files.find((e) => e[0] === activePost?.fileIds[activeIndex]) || [];

  return (
    <Grid gutter={0} ref={ref} sx={{ position: "relative" }}>
      {hasImage && (
        <Grid.Col span={width > 900 ? 7 : 12}>
          <Box
            sx={{
              position: "relative",
              height: "100%",
              maxHeight: 600,
              display: "flex",
              alignItems: "center",
              overflow: "hidden",
            }}
          >
            <PostSliderControls
              active={activeIndex}
              length={activePost?.fileIds.length || 0}
              setActive={(v) => setActiveIndex(v)}
            />

            <Box
              sx={{
                position: "absolute",
                bottom: -1,
                left: 0,
                right: 0,
                boxShadow: "0px 0px 35px 15px black",
                zIndex: 9,
              }}
            />

            <Image
              width="100%"
              height="100%"
              fit="cover"
              alt="img"
              src={file[2]}
              styles={{
                root: {
                  height: "100%",
                  width: "100%",
                },
                figure: {
                  height: "100%",
                  width: "100%",
                },
                image: {
                  userSelect: "none",
                },
                imageWrapper: {
                  width: "100%",
                  height: "100%",
                },
              }}
              withPlaceholder
            ></Image>
          </Box>
        </Grid.Col>
      )}

      <Grid.Col span={width > 900 && hasImage ? 5 : 12}>
        <Stack
          sx={{ position: "relative", height: "100%", maxHeight: 600 }}
          spacing={0}
        >
          {!!activePost && (
            <Stack spacing={0}>
              <Group spacing={0} noWrap p="md">
                <PostHeader post={activePost} />
                <CloseButton ml={4} onClick={closeModal} />
              </Group>
              <Divider />
            </Stack>
          )}
          <Box
            id="commentArea"
            sx={{
              position: "relative",
              height: "100%",
              minHeight: 400,
              overflow: "auto",
            }}
          >
            <CommentArea
              description={activePost?.description || ""}
              postId={postViewModal.postId}
              closeModal={closeModal}
            />
          </Box>
          <Divider />
          {activePost?.id && <SendCommentArea postId={activePost?.id} />}
          <Divider />
          {/* {!!activePost?.taskIds && activePost?.taskIds.length > 0 && (
            <PostTask taskId={activePost?.taskIds[0]} />
          )} */}

          {!!activePost && <PostFooter post={activePost} p="md" />}
        </Stack>
      </Grid.Col>
    </Grid>
  );
};

const PostViewModal = function () {
  const { postViewModal, setPostViewModal } = useStore(
    "temp",
    (state) => ({
      postViewModal: state.postViewModal!,
      setPostViewModal: state.setPostViewModal!,
    }),
    shallow
  );
  const { posts, resetComments } = useStore(
    "posts",
    (state) => ({
      posts: state.posts!,
      resetComments: state.resetComments!,
    }),
    shallow
  );

  const { isFocus } = useStore(
    "global",
    (state) => ({
      isFocus: state.isFocus!,
    }),
    shallow
  );

  const closeModal = useCallback(async () => {
    setPostViewModal({
      isOpen: false,
    });

    await wait(301);

    resetComments();
    setPostViewModal({
      postId: 0,
    });
  }, [resetComments, setPostViewModal]);

  const os = useOs();

  const activePost = posts.find(
    (e) => e.type === "post" && e.id === postViewModal.postId
  );

  const hasImage = activePost?.fileIds && activePost?.fileIds.length > 0;

  return (
    <Modal
      opened={postViewModal.isOpen}
      onClose={closeModal}
      centered
      transition={
        postViewModal.isOpen !== undefined ? "slide-down" : "slide-up"
      }
      withCloseButton={false}
      size={hasImage ? 1200 : 600}
      zIndex={100000000}
      overflow="outside"
      styles={{
        root: {
          marginBottom:
            isFocus && (os == "android" || os == "ios") ? "50vh" : "0",
        },
        body: {
          position: "relative",
        },
        modal: {
          position: "relative",
          padding: "0px !important",
          overflow: "hidden",
        },
        inner: {},
        overlay: {},
      }}
    >
      <PostViewModalContent
        activePost={activePost}
        hasImage={hasImage}
        closeModal={closeModal}
      />
    </Modal>
  );
};

export default PostViewModal;
