import { useCallback } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate, formatTime } from "~utils/tools";
import { PostType } from "~utils/types/Post";

import {
  EllipsisVerticalIcon,
  TrashIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import {
  Group,
  Avatar,
  Stack,
  Title,
  Text,
  Menu,
  ActionIcon,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import Link from "next/link";
import PostOwner from "./PostOwner";

interface PropType {
  post: PostType;
}

export default function PostHeader(props: PropType) {
  const { post } = props;

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setPostModal, setPostViewModal } = useStore(
    "temp",
    (state) => ({
      setPostModal: state.setPostModal!,
      setPostViewModal: state.setPostViewModal!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { actionUpdatePosts } = useStore(
    "posts",
    (state) => ({
      actionUpdatePosts: state.actionUpdatePosts!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const editPost = useCallback(() => {
    setPostViewModal({
      isOpen: false,
      postId: undefined,
    });
    setPostModal({
      modalType: "edit",
      isOpen: true,
      type: "post",
      data: post,
    });
  }, [setPostViewModal, setPostModal, post]);

  const handleDeletePost = useCallback(async () => {
    setPostViewModal({
      isOpen: false,
      postId: undefined,
    });
    await actionUpdatePosts(
      [
        {
          id: post.id,
        },
      ],
      true
    );
  }, [actionUpdatePosts, post.id, setPostViewModal]);

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRole = roles.find(
    (e) => e.isDefaultRole && e.id === activeUser?.defaultRoleId[0]
  );

  const activeOwner = users.find((e) => e.id === post.userId);
  const activeCustomer = customers.find((e) => e.id === post.organizationId);

  return (
    <Group position="apart" noWrap sx={{ position: "relative", width: "100%" }}>
      <PostOwner user={activeOwner} />

      {(post.userId === activeUserId ||
        activeUserDefaultRole?.weight ||
        0 < 0) && (
        <Menu width={150} shadow="md" position="bottom-end" transition="fade">
          <Menu.Target>
            <ActionIcon size="md">
              <EllipsisVerticalIcon width={22} height={22} />
            </ActionIcon>
          </Menu.Target>

          <Menu.Dropdown>
            <Menu.Item onClick={() => editPost()}>{t("post.edit")}</Menu.Item>
            <Menu.Item onClick={() => handleDeletePost()}>
              {t("post.delete")}
            </Menu.Item>
          </Menu.Dropdown>
        </Menu>
      )}
    </Group>
  );
}
