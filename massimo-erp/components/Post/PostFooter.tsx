import {
  ActionIcon,
  Anchor,
  Group,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { ChatBubbleOvalLeftIcon, HeartIcon } from "@heroicons/react/24/outline";
import HeartIconFilled from "@heroicons/react/24/solid/HeartIcon";
import { formatDate, formatTime } from "~utils/tools";
import { CC } from "~types";
import { PostType } from "~utils/types/Post";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useCallback } from "react";

const PostFooter: CC<{
  post: PostType;
  p?: string;
}> = ({ post, p }) => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setPostViewModal, setViewLikesModal } = useStore(
    "temp",
    (state) => ({
      setPostViewModal: state.setPostViewModal!,
      setViewLikesModal: state.setViewLikesModal!,
    }),
    shallow
  );
  const { actionUpdatePostMeta } = useStore(
    "posts",
    (state) => ({
      actionUpdatePostMeta: state.actionUpdatePostMeta!,
    }),
    shallow
  );

  const theme = useMantineTheme();

  const handleLike = useCallback(async () => {
    const isLiked = post.isLiked !== null;

    let rawData = isLiked
      ? {
          id: post.isLiked,
          post: post.id,
        }
      : {
          isLike: !post?.isLiked,
          isView: false,
          user: activeUserId,
          post: post.id,
        };

    await actionUpdatePostMeta([rawData], post.isLiked !== null);
  }, [actionUpdatePostMeta, activeUserId, post.id, post?.isLiked]);

  return (
    <Group position="apart" p={p} sx={{ order: 1 }}>
      <Group>
        <Group spacing={4}>
          <ActionIcon size="md" variant="transparent" onClick={handleLike}>
            {post?.isLiked !== null ? (
              <HeartIconFilled
                style={{
                  width: 24,
                  height: 24,
                  color: theme.colors.red[9],
                }}
              />
            ) : (
              <HeartIcon style={{ width: 24, height: 24 }} />
            )}
          </ActionIcon>

          {post.userId === activeUserId ? (
            <Anchor
              size="sm"
              weight={600}
              sx={(theme) => ({
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[4]
                    : theme.colors.dark[6],
                textDecoration: "none !important",
              })}
              onClick={() =>
                setViewLikesModal({
                  isOpen: true,
                  postId: post.id,
                })
              }
            >
              {post?.likeCount}
            </Anchor>
          ) : (
            <Text size="sm" weight={600}>
              {post?.likeCount}
            </Text>
          )}
        </Group>

        <Group spacing={4}>
          <ActionIcon
            size="md"
            variant="transparent"
            onClick={() =>
              setPostViewModal({
                isOpen: true,
                postId: post?.id,
              })
            }
          >
            <ChatBubbleOvalLeftIcon />
          </ActionIcon>
          <Text size="sm" weight={600}>
            {post?.commentCount}
          </Text>
        </Group>
      </Group>

      {!!post?.updatedAt && (
        <Text color="dimmed" size="xs" weight={600}>
          {formatTime(post?.updatedAt)} {formatDate(post?.updatedAt)}
        </Text>
      )}
    </Group>
  );
};

export default PostFooter;
