import {
  ActionIcon,
  Avatar,
  Group,
  Text,
  useMantineTheme,
} from "@mantine/core";
import {
  ChatBubbleOvalLeftIcon,
  HeartIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import HeartIconFilled from "@heroicons/react/24/solid/HeartIcon";
import { formatDate, formatTime } from "~utils/tools";
import { CC } from "~types";
import { PostType } from "~utils/types/Post";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import Link from "next/link";
import { UserType } from "~utils/types/User";
import { CustomerType } from "~utils/types/Customer";

const PostOwner: CC<{
  user?: UserType;
}> = ({ user }) => {
  const { setPostViewModal } = useStore(
    "temp",
    (state) => ({
      setPostViewModal: state.setPostViewModal!,
    }),
    shallow
  );

  return !!user ? (
    <Group spacing={12} noWrap>
      <ActiveAvatar userId={user?.id} radius="xl" size={32} />

      <Link
        href={`/profile/user/${user?.id}`}
        onClick={() =>
          setPostViewModal({
            isOpen: false,
          })
        }
      >
        <Text size="sm" weight={600} sx={{ whiteSpace: "nowrap" }}>
          {user?.name} {user?.surname}
        </Text>
      </Link>
    </Group>
  ) : (
    <></>
  );
};

export default PostOwner;
