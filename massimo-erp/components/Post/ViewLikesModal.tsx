import { Anchor, Group, Modal, Stack, Text } from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { useStore } from "~utils/store";

const ViewLikesModal = () => {
  const { viewLikesModal, setViewLikesModal } = useStore(
    "temp",
    (state) => ({
      viewLikesModal: state.viewLikesModal!,
      setViewLikesModal: state.setViewLikesModal!,
    }),
    shallow
  );
  const { likes, actionGetLikes, resetLikes } = useStore(
    "posts",
    (state) => ({
      likes: state.likes!,
      actionGetLikes: state.actionGetLikes!,
      resetLikes: state.resetLikes!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { push } = useRouter();
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(0);

  const getLikes = useCallback(async () => {
    try {
      const hasMoreData = await actionGetLikes(viewLikesModal.postId, page);
      setHasMore(hasMoreData);
    } finally {
      setPage((p) => p + 1);
    }
  }, [actionGetLikes, page, viewLikesModal.postId]);

  const closeModal = useCallback(() => {
    setViewLikesModal({
      isOpen: false,
      postId: 0,
    });
    setPage(0);
    setHasMore(false);
    resetLikes();
  }, [setViewLikesModal, resetLikes]);

  useEffect(() => {
    if (viewLikesModal.isOpen) {
      setPage(0);
      getLikes();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [viewLikesModal.isOpen]);

  return (
    <Modal
      title={t("likes")}
      opened={viewLikesModal.isOpen}
      onClose={closeModal}
      centered
      transition={
        viewLikesModal.isOpen !== undefined ? "slide-down" : "slide-up"
      }
      size={320}
      zIndex={100000000}
      overflow="outside"
    >
      {likes.length === 0 && (
        <Text size="xs" weight={600} color="dimmed" align="center">
          {t("noLikes")}
        </Text>
      )}

      {likes.length > 0 && (
        <CustomInfiniteScroll
          dataLength={likes.length}
          hasMore={hasMore}
          height={320}
          next={getLikes}
        >
          <Stack sx={{ width: "100%", maxWidth: 550 }} mx="auto" spacing="sm">
            {likes.map((userId: number, i: number) => {
              const user = users.find((e) => e.id === userId);

              return (
                <Group key={`user-${userId}`} spacing="sm">
                  <ActiveAvatar userId={userId} size={32} radius="xl" />
                  <Anchor
                    size="sm"
                    weight={600}
                    sx={(theme) => ({
                      color:
                        theme.colorScheme === "dark"
                          ? theme.colors.gray[4]
                          : theme.colors.dark[8],
                      textDecoration: "none !important",
                    })}
                    onClick={() => {
                      push(`/profile/user/${userId}`);
                      closeModal();
                    }}
                  >
                    {user?.name} {user?.surname}
                  </Anchor>
                </Group>
              );
            })}
          </Stack>
        </CustomInfiniteScroll>
      )}
    </Modal>
  );
};

export default ViewLikesModal;
