import { ArrowRightIcon, PencilIcon } from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Box,
  Group,
  Paper,
  Stack,
  Text,
  Tooltip,
  Badge,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { CustomerType } from "~utils/types/Customer";
import { ProjectType, RequestType } from "~utils/types/Project";
import { TaskTagType } from "~utils/types/Task";

interface PropType {
  project: ProjectType;
}

export default function ProjectCard(props: PropType) {
  const { project } = props;
  const { push } = useRouter();
  const { t } = useTranslation();

  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );
  const { setProjectModal, setLastOffer } = useStore(
    "temp",
    (state) => ({
      setProjectModal: state.setProjectModal!,
      setLastOffer: state.setLastOffer!,
    }),
    shallow
  );

  const activeCustomer = customers.find(
    (e) => e.id === requests.find((x) => x.id === project.requestId)?.customerId
  ) as CustomerType | undefined;

  const lastOffer = offers
    .filter((e) => project.offerIds.includes(e.id))
    .slice(-1)[0];

  const editDepartment = useCallback(() => {
    setLastOffer(
      offers.filter((e) => project.offerIds.includes(e.id)).slice(-1)[0]
    );
    setProjectModal({
      isOpen: true,
      type: "edit",
      isOwner: true,
      data: project,
    });
  }, [offers, project, setLastOffer, setProjectModal]);

  return (
    <Paper
      sx={(theme) => ({
        position: "relative",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[1],
        padding: theme.spacing.md,
        display: "flex",
        alignItems: "stretch",
      })}
    >
      <Tooltip label={t(`priority.${project?.priority}`)}>
        <Box
          sx={(theme) => ({
            width: 4,
            height: "auto",
            borderRadius: 8,
            background:
              theme.colors[priorityColors[project?.priority || "Very Low"]][9],
            marginRight: 8,
            flexShrink: 0,
          })}
        />
      </Tooltip>
      <Stack align="flex-start" spacing={8} sx={{ width: "calc(100% - 12px)" }}>
        <Group
          position="apart"
          align="flex-start"
          noWrap
          sx={{ width: "100%" }}
        >
          <Stack spacing={0}>
            <Text size="sm" weight={600}>
              {lastOffer?.name}
            </Text>
            {lastOffer?.description !== "" && (
              <Text size="sm" color="dimmed">
                {lastOffer?.description}
              </Text>
            )}
          </Stack>
          <ActionIcon onClick={() => editDepartment()}>
            <PencilIcon style={{ width: 16, height: 16 }} />
          </ActionIcon>
        </Group>

        <Group spacing={4} sx={{ width: "100%" }}>
          {project.departmentIds?.map((departmentId: number, i: number) => {
            const department = departments.filter(
              (e) => e.id === departmentId
            )[0] || { color: "#000000" };

            return (
              <Tooltip key={"projectTag-" + i} label={department.label}>
                <Badge
                  variant="light"
                  styles={{
                    root: {
                      background: hexToRGBA(department.color, 0.1),
                      color: department.color,
                    },
                  }}
                >
                  {department.label}
                </Badge>
              </Tooltip>
            );
          })}
        </Group>

        <Box
          sx={{
            position: "relative",
            width: "100%",
            cursor: "pointer",
            transition: "filter .2s ease",
            ":hover": {
              filter: "brightness(.8)",
            },
          }}
          onClick={() => push(`/profile/organization/${activeCustomer?.id}`)}
        >
          <Group
            spacing={8}
            noWrap
            sx={{
              position: "relative",
            }}
          >
            <ArrowRightIcon style={{ width: 16, height: 16, flexShrink: 0 }} />
            <Tooltip label={activeCustomer?.fullName}>
              <Stack
                spacing={0}
                sx={{
                  position: "relative",
                  width: "calc(100% - 36px)",
                }}
              >
                <Text
                  size="sm"
                  weight={600}
                  sx={{
                    maxWidth: "100%",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                  }}
                >
                  {activeCustomer?.fullName}
                </Text>
                <Text size="xs" weight={600} color="dimmed">
                  {activeCustomer?.shortName}
                </Text>
              </Stack>
            </Tooltip>
          </Group>
        </Box>
      </Stack>
    </Paper>
  );
}
