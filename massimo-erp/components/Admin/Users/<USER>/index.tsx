import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Stack,
  Text,
  SimpleGrid,
  Grid,
  Input,
  Select,
  Divider,
  Group,
  Button,
  Switch,
  Avatar,
  FileInput,
  useMantineTheme,
  PasswordInput,
  LoadingOverlay,
  Image,
} from "@mantine/core";
import ReactInputMask from "react-input-mask";
import { useCallback, useEffect, useState } from "react";
import { openConfirmModal } from "@mantine/modals";
import {
  cloneDeep,
  findLast,
  isEqual,
  isString,
  isUndefined,
  omit,
} from "lodash";
import { showNotification } from "@mantine/notifications";
import DepartmentRoleSelectorModal from "./RoleValuesModal";
import { stat } from "fs/promises";
import { ArrowDownTrayIcon, TrashIcon } from "@heroicons/react/24/outline";
import { useElementSize, useForceUpdate, useMediaQuery } from "@mantine/hooks";
import { CC } from "~types";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";

const UserAvatar: CC<{
  avatar: File | undefined;
}> = function ({ avatar }) {
  const blob = avatar && new Blob([avatar], { type: "image/jpeg" });
  const blobURL = blob && URL.createObjectURL(blob);

  return <Avatar src={blobURL || ""} alt={avatar?.name} />;
};

const UserModalContent: CC<{
  closeModal: () => void;
}> = ({ closeModal }) => {
  const { activeUserId, deleteData } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
      deleteData: state.deleteData!,
    }),
    shallow
  );

  const { users, plans, actionUpdateMultipleUser } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      plans: state.plans!,
      actionUpdateMultipleUser: state.actionUpdateMultipleUser!,
    }),
    shallow
  );

  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    userModal,
    setUserModal,
    blankUser,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      userModal: state.userModal!,
      setUserModal: state.setUserModal!,
      blankUser: state.computed!.blankUser!,
    }),
    shallow
  );

  const { files, actionUploadFiles } = useStore(
    "files",
    (state) => ({
      files: state.files!,
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 992px)");
  const { ref, width } = useElementSize();

  const [userAvatar, setUserAvatar] = useState<File>();

  const activeUser = users.find((user) => user.id === activeUserId);
  const activeUserDefaultRole = roles.find(
    (e) => e.id === activeUser?.defaultRoleId![0]
  );
  const shownUserDefaultRole = roles.find(
    (e) => e.id === userModal.data.defaultRoleId![0]
  );

  const isDisabled =
    shownUserDefaultRole?.name === "super-admin" &&
    activeUserDefaultRole?.name !== "super-admin";

  const findedFile = findLast(
    files,
    (file) => file[1] === "avatar" && userModal.data?.fileIds?.includes(file[0])
  ) as [number, string, string];

  const deleteUser = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await deleteData("users", userModal.data.id!);
    } finally {
      closeModal();
      setIsLoading(false);
    }
  }, [
    closeModal,
    deleteData,
    setIsLoading,
    setLoadingLevel,
    userModal.data.id,
  ]);

  const saveUser = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    const newData = userModal.data as UserType;

    try {
      let userId: number | undefined;

      if (userModal.type === "new") {
        const response = await actionUpdateMultipleUser(
          [omit(newData, ["id"])],
          false
        );

        userId = response[0].id;

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.userCreate"),
          autoClose: 3000,
        });
      } else if (userModal.type === "edit") {
        await actionUpdateMultipleUser([newData], false);

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.userUpdate"),
          autoClose: 3000,
        });
      }

      if (!isUndefined(userAvatar)) {
        await actionUploadFiles(
          [
            {
              userId: userId || newData.id!,
              file: userAvatar,
              id: findedFile ? findedFile[0] : undefined,
              type: "avatar",
            },
          ],
          "avatar",
          userId || newData.id,
          true
        );
      }
    } catch (error) {
      console.error(error);
    } finally {
      closeModal();
      setIsLoading(false);
    }
  }, [
    actionUpdateMultipleUser,
    actionUploadFiles,
    closeModal,
    findedFile,
    setIsLoading,
    setLoadingLevel,
    t,
    userAvatar,
    userModal.data,
    userModal.type,
  ]);

  return (
    <>
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />

      <DepartmentRoleSelectorModal />

      <Stack spacing="md" ref={ref}>
        <Stack spacing={4}>
          <Divider
            label={t("userModal.contactInformations")}
            labelPosition="center"
          />

          <Grid>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <SimpleGrid cols={1}>
                <Group align="flex-end" noWrap>
                  {!userAvatar && userModal.type === "edit" ? (
                    <ActiveAvatar userId={userModal.data.id} />
                  ) : (
                    <UserAvatar avatar={userAvatar} />
                  )}
                  <FileInput
                    styles={{
                      root: {
                        width: "100%",
                      },
                    }}
                    accept="image/png,image/jpeg,image/webp"
                    disabled={isLoading || isDisabled}
                    label={t("userModal.avatarInput.label")}
                    placeholder={t("userModal.avatarInput.placeholder")}
                    icon={
                      <ArrowDownTrayIcon style={{ width: 16, height: 16 }} />
                    }
                    onChange={async (value) => {
                      if (value !== null) {
                        setUserAvatar(value);
                      }
                      // if (value && userModal.data.id) {
                      //   const res = await actionUploadFiles(
                      //     [
                      //       {
                      //         userId: userModal.data.id,
                      //         file: value,
                      //         id: findedFile ? findedFile[0] : undefined,
                      //         type: "avatar",
                      //       },
                      //     ],
                      //     "avatar"
                      //   );
                      // }
                    }}
                  />
                </Group>
              </SimpleGrid>
            </Grid.Col>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <PasswordInput
                disabled={isLoading || isDisabled}
                placeholder={t("userModal.passwordInput.placeholder")}
                label={t("userModal.passwordInput.label")}
                withAsterisk
                value={userModal.data.password || ""}
                onChange={(event) => {
                  setUserModal({
                    data: {
                      ...userModal.data,
                      password: event.target.value,
                    },
                  });
                }}
              />
            </Grid.Col>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <Input.Wrapper
                withAsterisk
                label={t("userModal.nameInput.label")}
              >
                <Input
                  disabled={isLoading || isDisabled}
                  placeholder={t("userModal.nameInput.placeholder")}
                  value={userModal.data.name || ""}
                  onChange={(event: any) =>
                    setUserModal({
                      data: { ...userModal.data, name: event.target.value },
                    })
                  }
                />
              </Input.Wrapper>
            </Grid.Col>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <Input.Wrapper
                withAsterisk
                label={t("userModal.surnameInput.label")}
              >
                <Input
                  disabled={isLoading || isDisabled}
                  placeholder={t("userModal.surnameInput.placeholder")}
                  value={userModal.data.surname || ""}
                  onChange={(event: any) =>
                    setUserModal({
                      data: { ...userModal.data, surname: event.target.value },
                    })
                  }
                />
              </Input.Wrapper>
            </Grid.Col>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <Input.Wrapper
                withAsterisk
                label={t("userModal.phoneInput.label")}
              >
                <Input
                  disabled={isLoading || isDisabled}
                  placeholder={t("userModal.phoneInput.placeholder")}
                  component={ReactInputMask}
                  mask="+99 (999) 999 99 99"
                  value={userModal.data.phone || ""}
                  onChange={(event: any) =>
                    setUserModal({
                      data: { ...userModal.data, phone: event.target.value },
                    })
                  }
                />
              </Input.Wrapper>
            </Grid.Col>
            <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
              <Input.Wrapper
                withAsterisk
                label={t("userModal.emailInput.label")}
              >
                <Input
                  disabled={isLoading || isDisabled}
                  placeholder={t("userModal.emailInput.placeholder")}
                  type="email"
                  value={userModal.data.email}
                  onChange={(event: any) =>
                    setUserModal({
                      data: { ...userModal.data, email: event.target.value },
                    })
                  }
                />
              </Input.Wrapper>
            </Grid.Col>
          </Grid>
        </Stack>

        {(activeUserDefaultRole?.weight || 0) < 0 && (
          <>
            <Stack spacing={4}>
              <Divider
                label={t("userModal.siteOptions")}
                labelPosition="center"
              />
              <SimpleGrid cols={matches ? 1 : 2}>
                <Select
                  disabled={isLoading || isDisabled}
                  withAsterisk
                  label={t("userModal.defaultRole.label")}
                  placeholder={t("userModal.defaultRole.placeholder")}
                  value={`${userModal.data.defaultRoleId![0]}`}
                  onChange={(value: string) =>
                    setUserModal({
                      data: { ...userModal.data, defaultRoleId: [+value] },
                    })
                  }
                  data={[
                    ...roles
                      .filter((role) => role.isDefaultRole)
                      .map((role) => {
                        return {
                          value: `${role.id}`,
                          label: t(`defaultRoles.${role.label?.toLowerCase()}`),
                          disabled: role.name === "super-admin",
                        };
                      }),
                  ]}
                  styles={{
                    input: {
                      textTransform: "capitalize",
                    },
                    item: {
                      textTransform: "capitalize",
                    },
                  }}
                  transition="fade"
                  transitionDuration={200}
                />
                {/* <Select
              disabled={true}
              withAsterisk
              label={t("userModal.plan.label")}
              placeholder={t("userModal.plan.placeholder")}
              value={userModal.data.plan || ""}
              onChange={(value: any) =>
                setUserModal({
                  data: { ...userModal.data, plan: value },
                })
              }
              data={plans}
              styles={{
                input: {
                  textTransform: "capitalize",
                },
                item: {
                  textTransform: "capitalize",
                },
              }}
              transition="fade"
              transitionDuration={200}
            /> */}
              </SimpleGrid>
            </Stack>

            <Stack spacing={8}>
              <Divider
                label={t("userModal.userInformations")}
                labelPosition="center"
              />
              <SimpleGrid cols={2} mt={-8}>
                <Switch
                  label={t("userModal.isBanned")}
                  disabled={isLoading || isDisabled}
                  onLabel={t("on")}
                  offLabel={t("off")}
                  checked={userModal.data.isBanned}
                  onChange={(event) =>
                    setUserModal({
                      data: {
                        ...userModal.data,
                        isBanned: event.currentTarget.checked,
                      },
                    })
                  }
                />
                <Switch
                  label={t("userModal.isCustomer")}
                  disabled={isLoading || isDisabled}
                  onLabel={t("on")}
                  offLabel={t("off")}
                  checked={userModal.data.isCustomer}
                  onChange={(event) =>
                    setUserModal({
                      data: {
                        ...userModal.data,
                        isCustomer: event.currentTarget.checked,
                      },
                    })
                  }
                />
                <Switch
                  label={t("userModal.isVerified")}
                  disabled={isLoading || isDisabled}
                  onLabel={t("on")}
                  offLabel={t("off")}
                  checked={userModal.data.isValid}
                  onChange={(event) =>
                    setUserModal({
                      data: {
                        ...userModal.data,
                        isValid: event.currentTarget.checked,
                      },
                    })
                  }
                />
              </SimpleGrid>
            </Stack>
          </>
        )}
        <Divider orientation="horizontal" />
        <Group position="apart">
          <Group>
            {(activeUserDefaultRole?.weight || 0) < 0 && (
              <>
                <Button
                  onClick={() =>
                    setUserModal({
                      isOpenRoleValuesModal: true,
                    })
                  }
                >
                  {t("roleAccesses")}
                </Button>
                {userModal.type === "edit" &&
                  !isDisabled &&
                  userModal.data.id !== activeUserId &&
                  shownUserDefaultRole?.name !== "super-admin" && (
                    <Button
                      color="red"
                      leftIcon={<TrashIcon width={16} height={16} />}
                      onClick={() => deleteUser()}
                    >
                      {t("delete")}
                    </Button>
                  )}
              </>
            )}
          </Group>
          <Group>
            <Button variant="light" color="gray" onClick={() => closeModal()}>
              {t("back")}
            </Button>
            <Button onClick={() => saveUser()}>{t("save")}</Button>
          </Group>
        </Group>
      </Stack>
    </>
  );
};

const UserModal: CC = function () {
  const { users, filteredUsers, actionUpdateMultipleUser } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      filteredUsers: state.filteredUsers!,
      actionUpdateMultipleUser: state.actionUpdateMultipleUser!,
    }),
    shallow
  );

  const {
    isLoading,
    loadingLevel,
    setIsLoading,
    setLoadingLevel,
    userModal,
    setUserModal,
    blankUser,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      userModal: state.userModal!,
      setUserModal: state.setUserModal!,
      blankUser: state.computed!.blankUser!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 640px)");

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setUserModal({
      isOpen: false,
    });

    setTimeout(() => {
      setUserModal({
        data: blankUser,
      });
    }, 300);
  }, [isLoading, loadingLevel, setUserModal, blankUser]);

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });

  return (
    <Modal
      opened={userModal.isOpen}
      onClose={() => {
        if (isLoading && loadingLevel === 1) {
          return;
        }

        if (
          userModal.type === "edit"
            ? !isEqual(
                userModal.data,
                users.find((e: any) => e.id === userModal.data.id)
              )
            : !isEqual(omit(blankUser, ["id"]), omit(userModal.data, ["id"]))
        ) {
          confirmModal();
        } else {
          closeModal();
        }
      }}
      title={t(`userModal.title.${userModal.type}`)}
      centered
      transition={userModal.isOpen ? "slide-down" : "slide-up"}
      size={800}
      styles={{
        inner: {
          overflow: matches ? "auto" : "hidden",
        },
      }}
    >
      <UserModalContent closeModal={closeModal}></UserModalContent>
    </Modal>
  );
};

export default UserModal;
