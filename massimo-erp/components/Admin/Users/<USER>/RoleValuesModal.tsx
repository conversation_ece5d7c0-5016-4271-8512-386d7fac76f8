import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import {
  Modal,
  SegmentedControl,
  Table,
  Group,
  Text,
  Stack,
  Box,
  Center,
  Divider,
  Button,
  LoadingOverlay,
  useMantineTheme,
  Tooltip,
} from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { hexToRGBA } from "~utils/tools";
import { useTranslation } from "next-i18next";
import { RoleType } from "~utils/types/Roles";
import { DepartmentType } from "~utils/types/Department";
import { UserType } from "~utils/types/User";
import { CC } from "~types";
import { orderBy } from "lodash";

const DepartmentRoleSelectorModal: CC = function () {
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    userModal,
    setUserModal,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      userModal: state.userModal!,
      setUserModal: state.setUserModal!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const selectRole = useCallback((value: string) => {}, []);
  const theme = useMantineTheme();

  const closeModal = useCallback(() => {
    if (isLoading) {
      return;
    }

    setUserModal({
      isOpenRoleValuesModal: false,
    });
  }, [setUserModal, isLoading]);

  return (
    <Modal
      opened={userModal.isOpenRoleValuesModal}
      onClose={() => closeModal()}
      title={t("roleAccesses")}
      centered
      transition={userModal.isOpenRoleValuesModal ? "slide-down" : "slide-up"}
      size={600}
      zIndex={100000}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 2}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <Stack
        sx={{
          width: "100%",
          position: "relative",
        }}
      >
        <Table
          sx={{
            maxWidth: "100%",
          }}
        >
          <thead>
            <tr>
              <th>{t("Department")}</th>
              <th>{t("role")}</th>
            </tr>
          </thead>
          <tbody>
            {orderBy(
              departments.filter((e) =>
                userModal.data.departmentIds?.includes(e.id)
              ),
              "id"
            ).map((department: DepartmentType, i: number) => (
              <tr key={department.id}>
                <td
                  style={{
                    justifyContent: "flex-start",
                  }}
                >
                  <Stack
                    align="flex-start"
                    sx={{
                      width: "50%",
                      position: "relative",
                    }}
                  >
                    <Box
                      sx={(theme) => ({
                        maxWidth: 170,
                        position: "relative",
                        backgroundColor: hexToRGBA(department.color, 0.1),
                        color: department.color,
                        padding: "0px 10px",
                        borderRadius: 16,
                        fontSize: 14,
                        fontWeight: 600,
                        letterSpacing: 0.25,
                        lineHeight: "24px",
                        cursor: "default",
                      })}
                    >
                      <Tooltip label={department.label}>
                        <Text
                          size="xs"
                          weight={600}
                          sx={{
                            maxWidth: "100%",
                            color: department.color,
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                        >
                          {department.label}
                        </Text>
                      </Tooltip>
                    </Box>
                  </Stack>
                </td>
                <td align="right">
                  {department.roleIds.length > 0 ? (
                    <Stack spacing={8} sx={{ position: "static" }}>
                      {department.roleIds.map(
                        (roleId: number, index: number) => {
                          if (!roles.find((e) => e.id === roleId)) {
                            return null;
                          }

                          const values =
                            userModal.data.roleValues![`${department.id}`];
                          let activeValue = "null";

                          if (
                            !!values &&
                            Object.keys(values).includes(`${roleId}`)
                          ) {
                            activeValue =
                              userModal.data.roleValues![department.id][roleId];
                          }

                          const roleLabel = roles.filter(
                            (e) => e.id === roleId
                          )[0].label;

                          return (
                            <Group
                              key={"role-" + index}
                              noWrap
                              position="apart"
                              sx={{
                                position: "relative",
                              }}
                            >
                              <Tooltip label={roleLabel}>
                                <Text
                                  size="xs"
                                  weight={600}
                                  sx={{
                                    maxWidth: 150,
                                    whiteSpace: "nowrap",
                                    textOverflow: "ellipsis",
                                    overflow: "hidden",
                                  }}
                                >
                                  {roleLabel}
                                </Text>
                              </Tooltip>
                              <SegmentedControl
                                size="xs"
                                value={activeValue}
                                data={[
                                  {
                                    label: (
                                      <Center sx={{ width: 16, height: 22 }}>
                                        <XMarkIcon
                                          style={{
                                            width: 16,
                                            height: 16,
                                            color: theme.colors.red[9],
                                          }}
                                        />
                                      </Center>
                                    ),
                                    value: "false",
                                  },
                                  {
                                    label: (
                                      <Center sx={{ width: 16, height: 22 }}>
                                        /
                                      </Center>
                                    ),
                                    value: "null",
                                  },
                                  {
                                    label: (
                                      <Center sx={{ width: 16, height: 22 }}>
                                        <CheckIcon
                                          style={{
                                            width: 16,
                                            height: 16,
                                            color: theme.colors.green[9],
                                          }}
                                        />
                                      </Center>
                                    ),
                                    value: "true",
                                  },
                                ]}
                                onChange={(value) => {
                                  setUserModal({
                                    data: {
                                      ...userModal.data,
                                      roleValues: {
                                        ...userModal.data.roleValues,
                                        [department.id]: {
                                          ...userModal.data.roleValues![
                                            department.id
                                          ],
                                          [roleId]: value,
                                        },
                                      },
                                    },
                                  });
                                }}
                              />
                            </Group>
                          );
                        }
                      )}
                    </Stack>
                  ) : (
                    <Text
                      size="xs"
                      weight={600}
                      align="left"
                      color="dimmed"
                      my="sm"
                    >
                      {t("departmentModal.noRole")}
                    </Text>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
        {userModal.data.departmentIds!.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" align="center">
            {t("userModalEmptyDepartmentText")}
          </Text>
        )}
        <Divider orientation="horizontal" />
        <Group position="right">
          <Group>
            <Button onClick={() => closeModal()}>{t("ok")}</Button>
          </Group>
        </Group>
      </Stack>
    </Modal>
  );
};

export default DepartmentRoleSelectorModal;
