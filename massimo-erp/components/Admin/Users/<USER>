import { AdjustmentsHorizontalIcon } from "@heroicons/react/24/outline";
import { Menu, Button, SimpleGrid, Stack, Text, Checkbox } from "@mantine/core";
import { useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

interface PropType {
  fullWidth: boolean;
}

export default function UsersTableFilter(props: PropType) {
  const { fullWidth } = props;

  const {
    plans,
    status,
    roleColors,
    planColors,
    statusColors,
    filter,
    setFilter,
  } = useStore(
    "users",
    (state) => ({
      plans: state.plans!,
      status: state.status!,
      roleColors: state.roleColors!,
      planColors: state.planColors!,
      statusColors: state.statusColors!,
      filter: state.filter!,
      setFilter: state.setFilter!,
    }),
    shallow
  );

  return (
    <Menu width={500} position="bottom-end" transition="fade" shadow="md">
      <Menu.Target>
        <Button
          fullWidth={fullWidth}
          variant="light"
          size="sm"
          leftIcon={
            <AdjustmentsHorizontalIcon style={{ width: 20, height: 20 }} />
          }
        >
          Filter
        </Button>
      </Menu.Target>
      {/* <Menu.Dropdown p="md">
        <SimpleGrid cols={fullWidth ? 1 : 3} sx={{ width: "100%" }}>
          <Stack spacing={8}>
            <Text size="sm" weight={600} color="dimmed">
              Role
            </Text>
            <Checkbox
              color="teal"
              label="All"
              checked={filter.roles.length === roles.length}
              indeterminate={
                filter.roles.length > 0 && filter.roles.length !== roles.length
              }
              onChange={(value) => {
                filter.roles.length === roles.length
                  ? setFilter("roles", [])
                  : setFilter("roles", roles);
              }}
            />
            {roles.map((role: string, i: number) => (
              <Checkbox
                key={"role-" + i}
                color={roleColors[role]}
                label={role}
                checked={filter.roles.includes(role)}
                onChange={(value) =>
                  filter.roles.includes(role)
                    ? setFilter(
                        "roles",
                        filter.roles.filter((e) => e !== role)
                      )
                    : setFilter("roles", [...filter.roles, role])
                }
                sx={{
                  textTransform: "capitalize",
                }}
              />
            ))}
          </Stack>
          <Stack spacing={8}>
            <Text size="sm" weight={600} color="dimmed">
              Plan
            </Text>
            <Checkbox
              color="teal"
              label="All"
              checked={filter.plans.length === plans.length}
              indeterminate={
                filter.plans.length > 0 && filter.plans.length !== plans.length
              }
              onChange={(value) => {
                filter.plans.length === plans.length
                  ? setFilter("plans", [])
                  : setFilter("plans", plans);
              }}
            />
            {plans.map((plan: string, i: number) => (
              <Checkbox
                key={"plan-" + i}
                color={planColors[plan]}
                label={plan}
                checked={filter.plans.includes(plan)}
                onChange={() =>
                  filter.plans.includes(plan)
                    ? setFilter(
                        "plans",
                        filter.plans.filter((e) => e !== plan)
                      )
                    : setFilter("plans", [...filter.plans, plan])
                }
                sx={{
                  textTransform: "capitalize",
                }}
              />
            ))}
          </Stack>
          <Stack spacing={8}>
            <Text size="sm" weight={600} color="dimmed">
              Status
            </Text>
            <Checkbox
              color="teal"
              label="All"
              checked={filter.status.length === status.length}
              indeterminate={
                filter.status.length > 0 &&
                filter.status.length !== status.length
              }
              onChange={(value) => {
                filter.status.length === status.length
                  ? setFilter("status", [])
                  : setFilter("status", status);
              }}
            />
            {status.map((statusItem: string, i: number) => (
              <Checkbox
                key={"plan-" + i}
                color={statusColors[statusItem]}
                label={statusItem}
                checked={filter.status.includes(statusItem)}
                onChange={() =>
                  filter.status.includes(statusItem)
                    ? setFilter(
                        "status",
                        filter.status.filter((e) => e !== statusItem)
                      )
                    : setFilter("status", [...filter.status, statusItem])
                }
                sx={{
                  textTransform: "capitalize",
                }}
              />
            ))}
          </Stack>
        </SimpleGrid>
      </Menu.Dropdown> */}
    </Menu>
  );
}
