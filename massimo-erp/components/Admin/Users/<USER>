import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { Mo<PERSON>, Stack, Group, Text, Button } from "@mantine/core";
import { useCallback } from "react";
import { useTranslation } from "next-i18next";

export default function UserInformations() {
  const { userInformationsModal, setUserInformationsModal } = useStore(
    "temp",
    (state) => ({
      userInformationsModal: state.userInformationsModal!,
      setUserInformationsModal: state.setUserInformationsModal!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const closeModal = useCallback(() => {
    setUserInformationsModal({
      isOpen: false,
      data: {
        ip: "",
      },
    });
  }, [setUserInformationsModal]);

  return (
    <Modal
      opened={userInformationsModal.isOpen}
      onClose={() => closeModal()}
      title={t("userInformationsModal.title")}
      centered
      transition={userInformationsModal.isOpen ? "slide-down" : "slide-up"}
      size={400}
      zIndex={100000}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
    >
      <Stack>
        <Group position="apart">
          <Group align="baseline" spacing={8}>
            <Text size="xs" weight={600} color="dimmed">
              IP:
            </Text>
            <Text>{userInformationsModal.data.ip}</Text>
          </Group>

          <Button size="xs" color="red">
            Ban
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
