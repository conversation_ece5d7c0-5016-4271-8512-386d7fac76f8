import {
  Cog6<PERSON>oothIcon,
  EllipsisHorizontalIcon,
  PencilIcon,
  ChartPieIcon,
  UserIcon,
  ComputerDesktopIcon,
  TrashIcon,
  InformationCircleIcon,
  CodeBracketIcon,
  BuildingStorefrontIcon,
  BriefcaseIcon,
} from "@heroicons/react/24/outline";
import {
  Anchor,
  Group,
  ActionIcon,
  Menu,
  Text,
  Badge,
  Stack,
  Image,
  Box,
  useMantineTheme,
  Tooltip,
} from "@mantine/core";
import _, { findLast, get, isBoolean, map, reduce } from "lodash";
import { useTranslation } from "next-i18next";
import { useCallback } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { DepartmentType } from "~utils/types/Department";
import { RoleType } from "~utils/types/Roles";
import { UserType } from "~utils/types/User";

const roleIcons: {
  [key: string]: any;
} = {
  "super-admin": CodeBracketIcon,
  admin: ComputerDesktopIcon,
  empty: function A() {},
  staff: BriefcaseIcon,
};

const DepartmentAndRole: CC<{
  departmentId: string;
  roleId: string;
  user: UserType;
  isRestricted: boolean;
}> = function ({ departmentId, roleId, user, isRestricted }) {
  const { setUserModal, blankDepartment } = useStore(
    "temp",
    (state) => ({
      setUserModal: state.setUserModal!,
      blankDepartment: state.computed!.blankDepartment!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  let activeDepartment: DepartmentType =
    departments.find((e) => +e.id === +departmentId) || blankDepartment;
  let activeRole = roles.find((e: RoleType) => +e.id === +roleId) || {
    label: "",
  };

  const openRoleAccesses = useCallback(() => {
    setUserModal({
      isOpen: true,
      type: "edit",
      data: user,
    });
    setTimeout(() => {
      setUserModal({
        isOpenRoleValuesModal: true,
      });
    }, 50);
  }, [user, setUserModal]);

  return (
    <Tooltip label={activeDepartment.label + " : " + activeRole.label}>
      <Box
        sx={() => ({
          backgroundColor: hexToRGBA(activeDepartment.color, 0.1),
          color: activeDepartment.color,
          padding: "0px 10px",
          borderRadius: 16,
          fontSize: 11,
          fontWeight: 600,
          letterSpacing: 0.25,
          lineHeight: "20px",
          cursor: "pointer",
          transition: "filter .2s ease",
          ":hover": {
            filter: "brightness(.9)",
          },
          display: "flex",
          flexWrap: "nowrap",
        })}
        onClick={() => openRoleAccesses()}
      >
        <Text
          sx={{
            maxWidth: 150,
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            overflow: "hidden",
          }}
        >
          {activeDepartment.label}
        </Text>
        <Text
          sx={{
            maxWidth: 150,
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            overflow: "hidden",
          }}
        >
          : {activeRole.label}
        </Text>
      </Box>
    </Tooltip>
  );
};

const UsersTableItem: CC<{
  user: UserType;
}> = function (props) {
  const { user } = props;

  const { statusColors, roleColors, removeUser } = useStore(
    "users",
    (state) => ({
      statusColors: state.statusColors!,
      roleColors: state.roleColors!,
      removeUser: state.removeUser!,
    }),
    shallow
  );
  const {
    setIsLoading,
    setLoadingLevel,
    setUserModal,
    setUserInformationsModal,
  } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      setUserModal: state.setUserModal!,
      setUserInformationsModal: state.setUserInformationsModal!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { deleteData, activeUserId } = useStore(
    "global",
    (state) => ({
      deleteData: state.deleteData!,
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const theme = useMantineTheme();

  const shownUserDefaultRole = roles.find(
    (e) => e.id === user.defaultRoleId![0]
  );

  let ItemIcon =
    roleIcons[
      roles.find((role) => role.id === (user.defaultRoleId[0] || null))?.name ||
        "empty"
    ];

  const editItem = useCallback(() => {
    setUserModal({
      isOpen: true,
      type: "edit",
      data: user,
    });
  }, [setUserModal, user]);

  // const openUserInformations = useCallback(() => {
  //   setUserInformationsModal({
  //     isOpen: true,
  //     data: {
  //       ip: "***********",
  //     },
  //   });
  // }, [setUserInformationsModal]);

  const removeItem = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await deleteData("users", user.id);
    } finally {
      setIsLoading(false);
    }
  }, [deleteData, setIsLoading, setLoadingLevel, user.id]);

  const mappedData = reduce(
    user.departmentIds,
    ((acc: number[][], departmentId: number) => {
      const department = departments.find((x) => x.id === departmentId);

      if (department) {
        const matches: number[] = department.roleIds.filter((x) =>
          roles.some((y) => y.id === x)
        );

        acc.push(...matches.map((x) => [departmentId, x]));
      }

      return acc;
    }) as any,
    []
  );

  return (
    <tr>
      <td>
        <Group spacing="sm" noWrap>
          <ActiveAvatar userId={user.id} radius="xl" />
          <Tooltip label={`${user.name} ${user.surname}`}>
            <Anchor
              onClick={editItem}
              sx={{
                maxWidth: 200,
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {user.name} {user.surname}
            </Anchor>
          </Tooltip>
        </Group>
      </td>
      <td>{user.phone}</td>
      <td>
        <Anchor href={`mailto:${user.email}`}>{user.email}</Anchor>
      </td>
      <td>
        <Stack align="flex-start" spacing={8}>
          {mappedData.map(([departmentId, roleId]) => {
            const value = get(user, `roleValues.${departmentId}.${roleId}`);

            if (["null", "false"].includes(value)) {
              return null;
            }

            return (
              <DepartmentAndRole
                key={`role-${departmentId}-${roleId}`}
                departmentId={departmentId}
                roleId={roleId}
                user={user}
                isRestricted={false}
              />
            );
          })}
          {/**
             * return Object.keys([departmentId]).map(
                (roleId: string, a: number) => {
                  const value = user.roleValues[departmentId][roleId];

                  return value !== "false" ? (
                    <DepartmentAndRole
                      key={`role-${i}-${a}-${value}`}
                      departmentId={departmentId}
                      roleId={roleId}
                      user={user}
                      isRestricted={false}
                    />
                  ) : (
                    false
                  );
                }
              );
             */}
        </Stack>
      </td>
      <td>
        <Stack align="flex-start" spacing={8}>
          {mappedData.map(([departmentId, roleId]) => {
            const value = get(user, `roleValues.${departmentId}.${roleId}`);
            if (value !== "false") {
              return null;
            }

            return (
              <DepartmentAndRole
                key={`role-${departmentId}-${roleId}`}
                departmentId={departmentId}
                roleId={roleId}
                user={user}
                isRestricted={false}
              />
            );
          })}
        </Stack>
      </td>
      <td>
        {user.defaultRoleId[0] && (
          <Group spacing={8}>
            <ItemIcon
              style={{
                width: 20,
                height: 20,
                color:
                  theme.colors[
                    roleColors[
                      roles.find(
                        (role) => role.id === (user.defaultRoleId[0] || null)
                      )?.name || "empty"
                    ]
                  ][9],
              }}
            />
            <Text size="sm" weight={600} color="dimmed" transform="capitalize">
              {t(
                `defaultRoles.${roles
                  .find((role) => user.defaultRoleId[0] === role.id)
                  ?.label?.toLowerCase()}`
              )}
            </Text>
          </Group>
        )}
      </td>
      {/* <td>
        <Text transform="capitalize">{user.plan}</Text>
      </td>
      <td>
        <Badge color={statusColors[user.status]}>{user.status}</Badge>
      </td> */}
      <td align="right">
        <Menu width={150} position="bottom-end" shadow="sm" transition="fade">
          <Menu.Target>
            <ActionIcon>
              <EllipsisHorizontalIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
          </Menu.Target>
          <Menu.Dropdown>
            <Menu.Item onClick={() => editItem()}>
              <Group spacing="sm">
                <PencilIcon style={{ width: 16, height: 16 }} />
                <Text>{t("userTable.edit")}</Text>
              </Group>
            </Menu.Item>
            {/* <Menu.Item onClick={() => openUserInformations()}>
              <Group spacing="sm">
                <InformationCircleIcon style={{ width: 16, height: 16 }} />
                <Text>{t("userTable.informations")}</Text>
              </Group>
            </Menu.Item> */}
            {shownUserDefaultRole?.name !== "super-admin" &&
              user.id !== activeUserId && (
                <Menu.Item onClick={() => removeItem()}>
                  <Group spacing="sm">
                    <TrashIcon style={{ width: 16, height: 16 }} />
                    <Text>{t("userTable.delete")}</Text>
                  </Group>
                </Menu.Item>
              )}
          </Menu.Dropdown>
        </Menu>
      </td>
    </tr>
  );
};

export default UsersTableItem;
