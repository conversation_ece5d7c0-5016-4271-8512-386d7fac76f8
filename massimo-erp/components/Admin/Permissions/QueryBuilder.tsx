import React, { useState } from "react";
import {
  Query,
  Builder,
  Utils as Qb<PERSON><PERSON><PERSON>,
  JsonGroup,
} from "react-awesome-query-builder";
// types
import {
  Config,
  ImmutableTree,
  BuilderProps,
} from "react-awesome-query-builder";

import "react-awesome-query-builder/lib/css/styles.css";
import "react-awesome-query-builder/lib/css/compact_styles.css";

import config from "~components/Admin/Permissions/config";

interface PropType {
  data: {
    name: string;
    path: string;
    type: "edit" | "view";
    value: JsonGroup;
  };
}

export default function QueryBuilder(props: PropType) {
  const { data } = props;

  const [state, setState] = useState({
    tree: QbUtils.checkTree(QbUtils.loadTree(data.value), config),
    config: config,
  });

  const onChange = (immutableTree: ImmutableTree, config: Config) => {
    setState({ tree: immutableTree, config: config });

    const jsonTree = QbUtils.getTree(immutableTree);
    console.debug(jsonTree);
  };

  const renderBuilder = (props: BuilderProps) => (
    <div className="query-builder-container" style={{ padding: "10px" }}>
      <div className="query-builder qb-lite">
        <Builder {...props} />
      </div>
    </div>
  );

  return (
    <Query
      {...config}
      value={state.tree}
      onChange={onChange}
      renderBuilder={renderBuilder}
    />
  );
}
