import { useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { DepartmentType } from "~utils/types/Department";

import {
  Accordion,
  Table,
  SegmentedControl,
  Center,
  useMantineTheme,
} from "@mantine/core";

import Masonry from "react-masonry-css";
import { RoleType } from "~utils/types/Roles";
import {
  CheckIcon,
  EyeIcon,
  PencilIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";

interface PropType {
  data: any;
}

const AccessSelector = function (props: {
  value: number;
  onChange: (value: number) => void;
}) {
  const { value, onChange } = props;
  const theme = useMantineTheme();

  return (
    <>
      <SegmentedControl
        size="xs"
        value={value + ""}
        onChange={(value: string) => onChange(+value)}
        data={[
          {
            label: (
              <Center sx={{ width: 16, height: 22 }}>
                <XMarkIcon
                  style={{
                    width: 16,
                    height: 16,
                    color: theme.colors.red[9],
                  }}
                />
              </Center>
            ),
            value: "1",
          },
          {
            label: <Center sx={{ width: 16, height: 22 }}>/</Center>,
            value: "2",
          },
          {
            label: (
              <Center sx={{ width: 16, height: 22 }}>
                <EyeIcon
                  style={{
                    width: 16,
                    height: 16,
                    color: theme.colors.green[9],
                  }}
                />
              </Center>
            ),
            value: "3",
          },
          {
            label: (
              <Center sx={{ width: 16, height: 22 }}>
                <PencilIcon
                  style={{
                    width: 16,
                    height: 16,
                    color: theme.colors.blue[9],
                  }}
                />
              </Center>
            ),
            value: "4",
          },
        ]}
      />
    </>
  );
};

export default function AccessContent(props: PropType) {
  const { data } = props;

  const { accesses, setAccesses } = useStore(
    "temp",
    (state) => ({
      accesses: state.accesses!,
      setAccesses: state.setAccesses!,
    }),
    shallow
  );

  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );

  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const [activeValue, setActiveValue] = useState("");

  return (
    <Accordion
      variant="separated"
      value={activeValue}
      onChange={(value: string) => setActiveValue(value)}
    >
      <Masonry
        breakpointCols={{
          default: 3,
          1200: 2,
          750: 1,
        }}
        className="my-masonry-grid access"
        columnClassName="my-masonry-grid_column"
      >
        <Accordion.Item value="defaultRoles">
          <Accordion.Control>
            {t("accessContent.defaultRoles")}
          </Accordion.Control>
          <Accordion.Panel>
            <Table>
              <thead>
                <tr>
                  <th>{t("accessContent.roleName")}</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {roles
                  .filter((e) => e.isDefaultRole)
                  .map((role: RoleType, i: number) => {
                    return (
                      <tr key={role.id}>
                        <td>{role.label}</td>
                        <td align="right">
                          <AccessSelector
                            value={data.defaultRoles[role.name!]}
                            onChange={(value: number) =>
                              setAccesses({
                                ...data,
                                defaultRoles: {
                                  ...data.defaultRoles,
                                  [role.name!]: value,
                                },
                              })
                            }
                          />
                        </td>
                      </tr>
                    );
                  })}
              </tbody>
            </Table>
          </Accordion.Panel>
        </Accordion.Item>

        {departments.map((department: DepartmentType, i: number) => (
          <Accordion.Item
            key={"department-" + i}
            value={`${department.id}`}
            sx={(theme) => ({
              button: {
                background: hexToRGBA(department.color, 0.3),
                borderRadius: theme.radius.md,
                borderBottomLeftRadius:
                  +activeValue === department.id ? "0" : theme.radius.md,
                borderBottomRightRadius:
                  +activeValue === department.id ? "0" : theme.radius.md,
                transition: "border-radius .2s ease",
              },
            })}
          >
            <Accordion.Control>{department.label}</Accordion.Control>
            <Accordion.Panel
              sx={(theme) => ({
                borderBottomLeftRadius: theme.radius.md,
                borderBottomRightRadius: theme.radius.md,
              })}
            >
              <Table>
                <thead>
                  <tr>
                    <th>Role Name</th>
                    <th></th>
                  </tr>
                </thead>
                <tbody>
                  {roles
                    .filter((e) => department.roleIds.includes(e.id as number))
                    .map((role: RoleType, i: number) => (
                      <tr key={role.id}>
                        <td>{role.label}</td>
                        <td align="right">
                          <AccessSelector
                            value={
                              data.departmentRoles[department.name][
                                role.name as string
                              ]
                            }
                            onChange={(value: number) =>
                              setAccesses({
                                ...data,
                                departmentRoles: {
                                  ...data.departmentRoles,
                                  [department.name]: {
                                    ...data.departmentRoles[department.name],
                                    [role.name as string]: value,
                                  },
                                },
                              })
                            }
                          />
                        </td>
                      </tr>
                    ))}
                </tbody>
              </Table>
            </Accordion.Panel>
          </Accordion.Item>
        ))}
      </Masonry>
    </Accordion>
  );
}
