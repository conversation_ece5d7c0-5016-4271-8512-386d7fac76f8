import {
  ArrowPathIcon,
  ClipboardDocumentListIcon,
  CubeIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  Paper,
  Group,
  Stack,
  Text,
  Loader,
  Box,
  CheckIcon,
  Tooltip,
  useMantineTheme,
} from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate } from "~utils/tools";
import { RequestType } from "~utils/types/Project";

interface PropType {
  request: RequestType;
}

export default function RequestCard(props: PropType) {
  const { request } = props;

  const { setRequestViewModal } = useStore(
    "temp",
    (state) => ({
      setRequestViewModal: state.setRequestViewModal!,
    }),
    shallow
  );

  const theme = useMantineTheme();

  return (
    <Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[6]
            : theme.colors.gray[3],
        cursor: "pointer",
        transition: "filter .2s ease",
        ":hover": {
          filter: "brightness(.9)",
        },
        padding: theme.spacing.sm,
      })}
      onClick={() => {
        setRequestViewModal({
          isOpen: true,
          data: request,
        });
      }}
    >
      <Group position="apart" px={4}>
        <Stack spacing={4}>
          <Text size="xs" weight={600}>
            {request.name}
          </Text>
          <Text size="md" weight={600}>
            {`₺ ${request.budget}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
          </Text>
          <Text size="xs" weight={600} color="dimmed">
            {formatDate(new Date(request.start))} -{" "}
            {formatDate(new Date(request.end))}
          </Text>
        </Stack>
      </Group>
    </Paper>
  );
}
