import { ChevronDownIcon, PencilIcon } from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Avatar,
  Box,
  Group,
  Paper,
  Stack,
  Text,
  Collapse,
  Tooltip,
  Divider,
  Badge,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { orderBy } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { UserType } from "~utils/types/User";

export default function DepartmentCard(props: any) {
  const { department } = props;

  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { setDepartmentModal } = useStore(
    "temp",
    (state) => ({
      setDepartmentModal: state.setDepartmentModal!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );

  const { push } = useRouter();
  const { t } = useTranslation();

  const [openCollapse, setOpenCollapse] = useState(false);

  const editDepartment = useCallback(() => {
    setDepartmentModal({
      isOpen: true,
      type: "edit",
      data: department,
    });
  }, [department, setDepartmentModal]);

  return (
    <Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
        position: "relative",
      })}
    >
      <Stack
        spacing="sm"
        align="flex-start"
        sx={{
          overflow: "hidden",
          position: "relative",
        }}
      >
        <Group
          position="apart"
          align="flex-start"
          noWrap
          sx={{ width: "100%", position: "relative" }}
        >
          <Stack
            spacing={2}
            align="flex-start"
            sx={{ width: "calc(100% - 46px)", position: "relative" }}
          >
            <Tooltip label={department.label}>
              <Badge
                size="lg"
                variant="light"
                styles={{
                  root: {
                    maxWidth: "100%",
                    background: hexToRGBA(department.color, 0.1),
                    color: department.color,
                    textTransform: "capitalize",
                  },
                }}
              >
                {department.label}
              </Badge>
            </Tooltip>

            <Text size="sm" color="dimmed">
              {department.description}
            </Text>
          </Stack>
          <ActionIcon onClick={() => editDepartment()}>
            <PencilIcon style={{ width: 16, height: 16 }} />
          </ActionIcon>
        </Group>
        {department.userIds.length > 0 && (
          <>
            <Box
              onClick={() => setOpenCollapse(!openCollapse)}
              sx={(theme) => ({
                position: "relative",
                borderRadius: theme.radius.md,
                cursor: "pointer",
                transition: "background-color .2s ease",
                overflow: "hidden",
                ":hover": {
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[7]
                      : theme.colors.gray[3],
                },
              })}
              px={8}
              py={4}
            >
              <Group
                spacing="xs"
                noWrap
                sx={{
                  width: "100%",
                  position: "relative",
                }}
              >
                <ChevronDownIcon
                  style={{
                    width: 16,
                    height: 16,
                    transform: openCollapse ? "rotate(180deg)" : "rotate(0deg)",
                    transition: "transform .2s ease",
                  }}
                />
                <Text size="xs" weight={600} color="dimmed">
                  {t("Users")}
                </Text>
                <Avatar.Group
                  spacing="sm"
                  sx={{
                    overflow: "hidden",
                  }}
                >
                  {orderBy(
                    users.filter((e) => department.userIds.includes(e.id)),
                    "id"
                  )
                    .splice(0, 7)
                    .map((user: UserType, i: number) => {
                      return (
                        <ActiveAvatar
                          userId={user.id}
                          key={user.id}
                          size="sm"
                          radius="xl"
                        />
                      );
                    })}
                  {users.filter((e) => department.userIds.includes(e.id))
                    .length > 7 && (
                    <Avatar size="sm" radius="xl">
                      +
                      {users.filter((e) => department.userIds.includes(e.id))
                        .length - 7}
                    </Avatar>
                  )}
                </Avatar.Group>
              </Group>
            </Box>
            <Collapse in={openCollapse} sx={{ width: "100%" }}>
              <Stack sx={{ width: "100%" }}>
                <Divider />
                <Group spacing={10}>
                  {orderBy(
                    users.filter((e) => department.userIds.includes(e.id)),
                    "id"
                  ).map((user: UserType, i: number) => {
                    return (
                      <Tooltip
                        key={user.id}
                        label={`${user.name} ${user.surname}`}
                      >
                        <ActionIcon
                          size="lg"
                          radius="xl"
                          onClick={() => push(`/profile/user/${user.id}`)}
                        >
                          <ActiveAvatar
                            userId={user.id}
                            size={34}
                            radius="xl"
                          />
                        </ActionIcon>
                      </Tooltip>
                    );
                  })}
                </Group>
              </Stack>
            </Collapse>
          </>
        )}
      </Stack>
    </Paper>
  );
}
