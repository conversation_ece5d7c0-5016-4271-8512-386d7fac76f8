import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Table,
  SegmentedControl,
  Center,
  Stack,
  Group,
  Divider,
  Button,
  useMantineTheme,
} from "@mantine/core";
import { useCallback } from "react";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { RoleType } from "~utils/types/Roles";
import { orderBy } from "lodash";

export default function RoleSelectorModal() {
  const {
    departmentModal,
    setDepartmentModal,
    roleSelectorModal,
    setRoleSelectorModal,
  } = useStore(
    "temp",
    (state) => ({
      departmentModal: state.departmentModal!,
      setDepartmentModal: state.setDepartmentModal!,
      roleSelectorModal: state.roleSelectorModal!,
      setRoleSelectorModal: state.setRoleSelectorModal!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const theme = useMantineTheme();

  const closeModal = useCallback(() => {
    setRoleSelectorModal({
      isOpen: false,
      userId: 0,
    });
  }, [setRoleSelectorModal]);

  return (
    <Modal
      opened={roleSelectorModal.isOpen}
      onClose={() => closeModal()}
      centered
      title={t("roleAccesses")}
      transition={roleSelectorModal.isOpen ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
        modal: {
          overflow: "hidden",
        },
      }}
      size={500}
    >
      <Stack>
        <Table>
          <thead>
            <tr>
              <th>{t("role")}</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {orderBy(
              roles.filter(
                (e) =>
                  departmentModal.data.roleIds.includes(e.id as number) &&
                  e.departmentId === departmentModal.data.id
              ),
              "id"
            )
              .concat(
                !!departmentModal.changes.roles
                  ? departmentModal.changes.roles.filter(
                      (e: RoleType) => e.id < 0
                    )
                  : []
              )
              .map((role: RoleType, i: number) => {
                const userRoleValues = users.find(
                  (user) => user.id === roleSelectorModal.userId
                )?.roleValues as Record<string, Record<string, string>>;

                if (userRoleValues === undefined) {
                  return;
                }

                let activeDepartment =
                  departmentModal.data.id >= 0
                    ? userRoleValues[`${departmentModal.data.id}`]
                    : {};

                if (
                  !!departmentModal.changes &&
                  !!departmentModal.changes.users &&
                  departmentModal.changes.users.some(
                    (e: any) => e.id === roleSelectorModal.userId
                  )
                ) {
                  activeDepartment = {
                    ...activeDepartment,
                    ...departmentModal.changes.users.find(
                      (e: any) => e.id === roleSelectorModal.userId
                    ).roleValues[departmentModal.data.id],
                  };
                }

                let activeRoleValue = "null";

                if (
                  !!activeDepartment &&
                  Object.keys(activeDepartment).includes(
                    role.id > 0 ? `${role.id}` : `$roles.${role.id}.id`
                  )
                ) {
                  activeRoleValue =
                    activeDepartment[
                      role.id > 0 ? role.id : `$roles.${role.id}.id`
                    ];
                }

                return (
                  <tr key={`roletab-${i}`}>
                    <td>{role.label}</td>
                    <td align="right">
                      <SegmentedControl
                        size="xs"
                        value={activeRoleValue}
                        data={[
                          {
                            label: (
                              <Center sx={{ width: 16, height: 22 }}>
                                <XMarkIcon
                                  style={{
                                    width: 16,
                                    height: 16,
                                    color: theme.colors.red[9],
                                  }}
                                />
                              </Center>
                            ),
                            value: "false",
                          },
                          {
                            label: (
                              <Center sx={{ width: 16, height: 22 }}>/</Center>
                            ),
                            value: "null",
                          },
                          {
                            label: (
                              <Center sx={{ width: 16, height: 22 }}>
                                <CheckIcon
                                  style={{
                                    width: 16,
                                    height: 16,
                                    color: theme.colors.green[9],
                                  }}
                                />
                              </Center>
                            ),
                            value: "true",
                          },
                        ]}
                        onChange={(value) => {
                          setDepartmentModal({
                            changes: {
                              users: [
                                {
                                  id: roleSelectorModal.userId,
                                  roleValues: {
                                    [`${departmentModal.data.id}`]: {
                                      [role.id > 0
                                        ? role.id
                                        : `$roles.${role.id}.id`]: value,
                                    },
                                  },
                                },
                              ],
                            },
                          });
                        }}
                      />
                    </td>
                  </tr>
                );
              })}
          </tbody>
        </Table>
        <Divider />
        <Group position="right">
          <Button variant="light" color="gray" onClick={() => closeModal()}>
            {t("close")}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
