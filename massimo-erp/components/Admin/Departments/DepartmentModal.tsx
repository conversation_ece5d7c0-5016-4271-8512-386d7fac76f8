import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Stack,
  Button,
  Input,
  Text,
  Textarea,
  Grid,
  Group,
  Avatar,
  Divider,
  SimpleGrid,
  Paper,
  Box,
  ActionIcon,
  ColorInput,
  NumberInput,
  Switch,
  LoadingOverlay,
  Tooltip,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import {
  ArrowPathIcon,
  PlusIcon,
  PlusSmallIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { ChangeEvent, useCallback, useEffect, useState } from "react";
import { openConfirmModal } from "@mantine/modals";
import { findLast, isEqual, omit, orderBy } from "lodash";
import { UserType } from "~utils/types/User";
import { showNotification } from "@mantine/notifications";
import SelectUserModal from "~components/Modals/SelectUserModal";
import { randomColor } from "~utils/tools";
import RoleSelectorModal from "./RoleSelectorModal";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { RoleType } from "~utils/types/Roles";
import ActiveAvatar from "~components/ActiveAvatar";

const DepartmentModalContent: CC<{
  saveData: () => void;
  deleteDepartment: () => void;
  closeModal: () => void;
}> = function ({ saveData, deleteDepartment, closeModal }) {
  const { deleteData } = useStore(
    "global",
    (state) => ({
      deleteData: state.deleteData!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const {
    setIsLoading,
    setLoadingLevel,
    isLoading,
    loadingLevel,
    departmentModal,
    setDepartmentModal,
    setRoleSelectorModal,
    setIsOpenSelectUserModal,
  } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      departmentModal: state.departmentModal!,
      setDepartmentModal: state.setDepartmentModal!,
      setRoleSelectorModal: state.setRoleSelectorModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const [newRoleLabel, setNewRoleLabel] = useState("");

  const handleColorChange = useCallback(
    (value: string) => {
      setDepartmentModal({
        data: {
          color: value,
        },
      });
    },
    [setDepartmentModal]
  );

  const addRoleToDepartment = useCallback(async () => {
    if (newRoleLabel === "") {
      return;
    }

    const newRole: RoleType = {
      id:
        ((!!departmentModal.changes.roles
          ? departmentModal.changes.roles.length
          : 0) +
          1) *
        -1,
      label: newRoleLabel,
      weight: 0,
      isDefaultRole: false,
      isDepartmentRole: true,
      departmentId:
        departmentModal.type === "new"
          ? `$.${departmentModal.data.id}.id`
          : departmentModal.data.id,
    };

    setDepartmentModal({
      data: {
        roleIds: [newRole.id as number],
      },
      changes: {
        roles: [newRole],
      },
    });

    setNewRoleLabel("");
  }, [
    departmentModal.changes.roles,
    departmentModal.data.id,
    departmentModal.type,
    newRoleLabel,
    setDepartmentModal,
  ]);

  const removeRoleFromDepartment = useCallback(
    async (value: number) => {
      setDepartmentModal({}, ["roleIds"], [value]);

      setIsLoading(true);
      setLoadingLevel(1);

      try {
        await deleteData("roles", value);
      } finally {
        setIsLoading(false);
      }
    },
    [deleteData, setDepartmentModal, setIsLoading, setLoadingLevel]
  );

  const removeUserFromDepartment = useCallback(
    (id: number) => {
      setDepartmentModal({}, ["userIds"], [id]);
    },
    [setDepartmentModal]
  );

  return (
    <Stack spacing="sm" ref={ref}>
      <Stack spacing="sm">
        <Grid>
          <Grid.Col span={width < 600 && width !== 0 ? 12 : 6}>
            <ColorInput
              withAsterisk
              placeholder="Color"
              label={t("departmentModal.color")}
              value={departmentModal.data.color}
              disabled={isLoading && loadingLevel === 1}
              onChange={(value: any) => handleColorChange(value)}
              rightSection={
                <ActionIcon onClick={() => handleColorChange(randomColor())}>
                  <ArrowPathIcon style={{ width: 16, height: 16 }} />
                </ActionIcon>
              }
              dropdownZIndex={2000}
            />
          </Grid.Col>
          <Grid.Col span={width < 600 && width > 0 ? 12 : 6}>
            <Input.Wrapper
              withAsterisk
              label={t("departmentModal.nameInput.label")}
            >
              <Input
                placeholder={t("departmentModal.nameInput.placeholder")}
                value={departmentModal.data.label}
                disabled={isLoading && loadingLevel === 1}
                onChange={(event: any) =>
                  setDepartmentModal({
                    data: {
                      label: event.target.value,
                    },
                  })
                }
              />
            </Input.Wrapper>
          </Grid.Col>
          <Grid.Col span={12}>
            <Textarea
              placeholder={t("departmentModal.descriptionInput.placeholder")}
              label={t("departmentModal.descriptionInput.label")}
              rows={2}
              disabled={isLoading && loadingLevel === 1}
              value={departmentModal.data.description}
              onChange={(event: any) =>
                setDepartmentModal({
                  data: {
                    description: event.target.value,
                  },
                })
              }
            />
          </Grid.Col>
        </Grid>
        <Divider
          label={t("departmentModal.rolesPermissions")}
          labelPosition="center"
          mt="xs"
        />
        <SimpleGrid
          cols={width < 600 && width > 0 ? 1 : 2}
          spacing="md"
          sx={{ alignItems: "flex-end" }}
          mb="xs"
        >
          <Group spacing={8} noWrap align="flex-end">
            <Input.Wrapper
              label={t("departmentModal.newRole.label")}
              sx={{ width: "100%" }}
            >
              <Input
                size="sm"
                placeholder={t("departmentModal.newRole.placeholder")}
                disabled={isLoading && loadingLevel === 1}
                value={newRoleLabel}
                onChange={(event: ChangeEvent<HTMLInputElement>) =>
                  setNewRoleLabel(event.target.value)
                }
              />
            </Input.Wrapper>
            <ActionIcon
              variant="light"
              size={36}
              onClick={() => addRoleToDepartment()}
            >
              <PlusSmallIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
          </Group>
        </SimpleGrid>
        {departmentModal.data.roleIds.length > 0 ? (
          <SimpleGrid cols={width < 600 && width > 0 ? 1 : 2}>
            {orderBy(roles, "id")
              .filter(
                (e) =>
                  departmentModal.data.roleIds.includes(e.id as number) &&
                  e.departmentId === departmentModal.data.id
              )
              .concat(
                !!departmentModal.changes.roles
                  ? departmentModal.changes.roles.filter(
                      (e: RoleType) => e.id < 0
                    )
                  : []
              )
              .map((role: RoleType, i: number) => {
                return (
                  <Paper
                    key={"key-" + i}
                    sx={(theme) => ({
                      background:
                        theme.colorScheme === "dark"
                          ? theme.colors.dark[8]
                          : theme.colors.gray[1],
                      padding: 8,
                      paddingLeft: 12,
                    })}
                  >
                    <Group
                      noWrap
                      sx={{
                        position: "relative",
                        width: "100%",
                      }}
                    >
                      <Group
                        sx={{
                          width: "calc(100% - 144px)",
                        }}
                      >
                        <Tooltip label={role.label}>
                          <Text
                            size="sm"
                            weight={600}
                            sx={{
                              maxWidth: "100%",
                              whiteSpace: "nowrap",
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                            }}
                          >
                            {role.label}
                          </Text>
                        </Tooltip>
                      </Group>
                      <NumberInput
                        size="xs"
                        placeholder={t("departmentModal.weight")}
                        disabled={isLoading && loadingLevel === 1}
                        min={0}
                        value={role.weight}
                        onChange={(value: number) => {
                          setDepartmentModal({
                            changes: {
                              roles: [
                                {
                                  id: role.id,
                                  weight: value,
                                },
                              ],
                            },
                          });
                        }}
                        styles={{
                          root: {
                            flexShrink: 0,
                            width: 80,
                          },
                        }}
                      />
                      <ActionIcon
                        variant="subtle"
                        size={30}
                        onClick={() =>
                          removeRoleFromDepartment(role.id as number)
                        }
                      >
                        <XMarkIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                );
              })}
          </SimpleGrid>
        ) : (
          <Text size="xs" weight={600} color="dimmed" my="sm">
            {t("departmentModal.noRole")}
          </Text>
        )}

        <Text size="sm" weight={600} color="dimmed">
          {t("Users")}
        </Text>
        <SimpleGrid cols={width < 600 && width > 0 ? 1 : 3}>
          {orderBy(
            users.filter((e) => departmentModal.data.userIds?.includes(e.id)),
            "id"
          ).map((user: UserType, i: number) => {
            return (
              <Paper
                key={`user-` + i}
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[1],
                  padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                })}
              >
                <Group
                  noWrap
                  align="flex-start"
                  sx={{ position: "relative", width: "100%", height: "100%" }}
                >
                  <ActiveAvatar userId={user.id} radius="xl" />
                  <Stack
                    spacing={8}
                    align="start"
                    justify="space-between"
                    sx={{ width: "calc(100% - 54px)", height: "100%" }}
                  >
                    <Group sx={{ position: "relative", width: "100%" }} noWrap>
                      <Tooltip label={`${user.name} ${user.surname}`}>
                        <Text
                          size="sm"
                          weight={600}
                          sx={{
                            width: "100%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                        >
                          {user.name} {user.surname}
                        </Text>
                      </Tooltip>
                      <ActionIcon
                        size="sm"
                        onClick={() => removeUserFromDepartment(user.id)}
                      >
                        <XMarkIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Group>

                    <Button
                      size="xs"
                      compact
                      onClick={() => {
                        setRoleSelectorModal({
                          isOpen: true,
                          userId: user.id,
                        });
                      }}
                    >
                      {t("roleAccesses")}
                    </Button>
                  </Stack>
                </Group>
              </Paper>
            );
          })}
          {/* {departmentModal.data.users.length !== users.length && ( */}
          <Box
            sx={(theme) => ({
              minHeight: 72,
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              borderRadius: theme.radius.md,
              border: `2px dashed ${
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[2]
              }`,
              cursor: "pointer",
              transition: "filter .2s ease",
              ":hover": {
                filter: "brightness(.9)",
              },
            })}
            onClick={() => setIsOpenSelectUserModal(true)}
          >
            <PlusIcon style={{ width: 24, height: 24 }} />
          </Box>
          {/* )} */}
        </SimpleGrid>
      </Stack>

      <Divider orientation="horizontal" />
      <Group position="apart">
        <Group>
          <Button
            color="red"
            leftIcon={<TrashIcon width={16} height={16} />}
            onClick={() => deleteDepartment()}
          >
            {t("delete")}
          </Button>
          <Switch
            mt={-8}
            label={t("departmentModal.isVisible")}
            checked={departmentModal.data.isVisible}
            onChange={(event) =>
              setDepartmentModal({
                data: {
                  isVisible: event.target.checked,
                },
              })
            }
            styles={{
              body: {
                alignItems: "center",
              },
            }}
          />
        </Group>

        <Group>
          <Button variant="light" color="gray" onClick={() => closeModal()}>
            {t("cancel")}
          </Button>
          <Button onClick={() => saveData()}>{t("save")}</Button>
        </Group>
      </Group>
    </Stack>
  );
};

const DepartmentModal: CC<{
  updateDepartments: () => void;
}> = function ({ updateDepartments }) {
  const { departments, filteredDepartments, actionUpdateDepartment } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
      filteredDepartments: state.filteredDepartments!,
      setDepartment: state.setDepartment!,
      addDepartment: state.addDepartment!,
      actionUpdateDepartment: state.actionUpdateDepartment!,
    }),
    shallow
  );
  const { deleteData } = useStore(
    "global",
    (state) => ({
      deleteData: state.deleteData!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    departmentModal,
    setDepartmentModal,
    blankDepartment,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      departmentModal: state.departmentModal!,
      setDepartmentModal: state.setDepartmentModal!,
      blankDepartment: state.computed!.blankDepartment!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const addUserToDepartment = useCallback(
    (userId: number) => {
      setDepartmentModal({
        data: {
          userIds: [userId],
        },
      });
    },
    [setDepartmentModal]
  );
  const removeUserFromDepartment = useCallback(
    (id: number) => {
      setDepartmentModal({}, ["userIds"], [id]);
    },
    [setDepartmentModal]
  );

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setDepartmentModal({ isOpen: false }, ["data", "changes"]);
  }, [isLoading, loadingLevel, setDepartmentModal]);

  const saveData = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      if (departmentModal.type === "new") {
        await actionUpdateDepartment({
          data: {
            ...omit(departmentModal.data, "id"),
          },
          changes: departmentModal.changes,
        });

        // await addDepartment(departmentModal.data);

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.departmentCreate"),
          autoClose: 3000,
        });
      } else if (departmentModal.type === "edit") {
        await actionUpdateDepartment({
          data: departmentModal.data,
          changes: departmentModal.changes,
        });
        // await setDepartment(departmentModal.data);

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.departmentUpdate"),
          autoClose: 3000,
        });
      }
    } catch (err) {
      console.debug(err);
    } finally {
      closeModal();
      setIsLoading(false);
      await updateDepartments();
      // await actionGetRoles();
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    departmentModal,
    actionUpdateDepartment,
    t,
    closeModal,
    updateDepartments,
  ]);

  const deleteDepartment = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await deleteData("departments", departmentModal.data.id);
    } finally {
      closeModal();
      setIsLoading(false);
      updateDepartments();
    }
  }, [
    closeModal,
    deleteData,
    departmentModal.data.id,
    setIsLoading,
    setLoadingLevel,
    updateDepartments,
  ]);

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });

  return (
    <Modal
      opened={departmentModal.isOpen}
      onClose={() => {
        if (isLoading && loadingLevel === 1) {
          return;
        }

        if (
          departmentModal.type === "edit"
            ? !isEqual(
                departmentModal.data,
                filteredDepartments.filter(
                  (e: any) => e.id === departmentModal.data.id
                )[0]
              )
            : !isEqual(blankDepartment, departmentModal.data)
        ) {
          confirmModal();
        } else {
          closeModal();
        }
      }}
      centered
      title={t(`departmentModal.title.${departmentModal.type}`)}
      transition={departmentModal.isOpen ? "slide-down" : "slide-up"}
      size={800}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <SelectUserModal
        onesList={departmentModal.data.userIds}
        handleSelect={(id: number) => addUserToDepartment(id)}
        handleRemove={(id: number) => removeUserFromDepartment(id)}
      />
      <RoleSelectorModal />

      <DepartmentModalContent
        saveData={saveData}
        deleteDepartment={deleteDepartment}
        closeModal={closeModal}
      />
    </Modal>
  );
};

export default DepartmentModal;
