import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Box,
  Text,
  Grid,
  Modal,
  Stack,
  Input,
  Group,
  Select,
  Button,
  Tooltip,
  Divider,
  Textarea,
  ThemeIcon,
  ActionIcon,
  SimpleGrid,
  LoadingOverlay,
} from "@mantine/core";
import { useElementSize } from "@mantine/hooks";
import { forwardRef, useCallback } from "react";
import {
  PlusSmallIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import InputMask from "react-input-mask";
import { UserType } from "~utils/types/User";
import { openConfirmModal } from "@mantine/modals";
import { cloneDeep, findLast, isEqual, omit, orderBy } from "lodash";
import { showNotification } from "@mantine/notifications";
import SelectUserModal from "~components/Modals/SelectUserModal";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import ActiveAvatar from "~components/ActiveAvatar";

// const blankNewLink = {
//   id: "",
//   name: "",
//   position: "",
//   phone: "",
//   email: "",
// };

const locations = [
  {
    image: () => (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="30"
        height="20"
        viewBox="0 -30000 90000 60000"
      >
        <title>Flag of Turkey</title>
        <path fill="#e30a17" d="m0-30000h90000v60000H0z" />
        <path
          fill="#fff"
          d="m41750 0 13568-4408-8386 11541V-7133l8386 11541zm925 8021a15000 15000 0 1 1 0-16042 12000 12000 0 1 0 0 16042z"
        />
      </svg>
    ),
    label: "Turkiye",
    value: "tr",
  },
];

const taxNumberMasks: {
  [key: string]: string;
} = {
  tr: "9999999999",
};

interface ItemProps extends React.ComponentPropsWithoutRef<"div"> {
  image: string;
  label: string;
  value: string;
}

const LocationSelectItem = forwardRef<HTMLDivElement, ItemProps>(
  function SelectItem({ image, label, value, ...others }: ItemProps, ref) {
    const Logo = image;
    return (
      <div ref={ref} {...others}>
        <Group spacing="sm" noWrap>
          <Logo />

          <div>
            <Text size="sm">{label}</Text>
          </div>
        </Group>
      </div>
    );
  }
);

const CustomerModalContent: CC<{
  saveCustomer: () => void;
  closeModal: () => void;
  deleteCustomer: () => void;
}> = function ({ saveCustomer, closeModal, deleteCustomer }) {
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );

  const {
    isLoading,
    loadingLevel,
    customerModal,
    setCustomerModal,
    setIsOpenSelectUserModal,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      customerModal: state.customerModal!,
      setCustomerModal: state.setCustomerModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  const addManagerToCustomer = useCallback(
    (userId: number) => {
      setCustomerModal({
        data: {
          ...customerModal.data,
          personIds: [...customerModal.data.personIds, userId],
        },
      });
    },
    [customerModal.data, setCustomerModal]
  );

  const removeManagerFromCustomer = useCallback(
    (id: number) => {
      let newBlankCustomerpersons = customerModal.data.personIds.filter(
        (e) => e !== id
      );

      setCustomerModal({
        data: {
          ...customerModal.data,
          personIds: newBlankCustomerpersons,
        },
      });
    },
    [customerModal.data, setCustomerModal]
  );

  return (
    <Stack ref={ref}>
      <SimpleGrid cols={width <= 550 && width > 0 ? 1 : 2}>
        <Select
          label={t("customerModal.locationInput.label")}
          placeholder={t("customerModal.locationInput.placeholder")}
          itemComponent={LocationSelectItem}
          data={locations}
          icon={
            customerModal.data.location !== ""
              ? locations
                  .filter((e) => e.value === customerModal.data.location)[0]
                  .image()
              : undefined
          }
          value={customerModal.data.location}
          onChange={(value: string) =>
            setCustomerModal({
              data: {
                ...customerModal.data,
                location: value,
              },
            })
          }
          disabled={isLoading && loadingLevel === 1}
          searchable
          maxDropdownHeight={400}
          withAsterisk
          nothingFound={t("noData")}
          filter={(value, item) =>
            item.label!.toLowerCase().includes(value.toLowerCase().trim())
          }
          styles={{
            icon: {
              width: 44,
            },
            input: {
              paddingLeft:
                customerModal.data.location !== "" ? "44px !important" : "",
            },
          }}
          transition="fade"
          transitionDuration={200}
        />
      </SimpleGrid>

      <Stack align="flex-start">
        <Divider
          sx={{
            width: "100%",
          }}
          label={t("customerModal.companyInformations")}
          labelPosition="center"
        />
        <SimpleGrid
          cols={width <= 550 && width > 0 ? 1 : 2}
          spacing="sm"
          sx={{ width: "100%" }}
        >
          <Input.Wrapper
            id="input-demo"
            label={t("customerModal.shortName.label")}
            withAsterisk
          >
            <Input
              id="input-demo"
              placeholder={t("customerModal.shortName.placeholder")}
              disabled={isLoading && loadingLevel === 1}
              value={customerModal.data.shortName}
              onChange={(event: any) =>
                setCustomerModal({
                  data: {
                    ...customerModal.data,
                    shortName: event.target.value,
                  },
                })
              }
            />
          </Input.Wrapper>
          <Input.Wrapper
            id="input-demo"
            label={t("customerModal.fullName.label")}
            withAsterisk
          >
            <Input
              id="input-demo"
              placeholder={t("customerModal.fullName.placeholder")}
              disabled={isLoading && loadingLevel === 1}
              value={customerModal.data.fullName}
              onChange={(event: any) =>
                setCustomerModal({
                  data: {
                    ...customerModal.data,
                    fullName: event.target.value,
                  },
                })
              }
            />
          </Input.Wrapper>
          <Input.Wrapper
            id="input-demo"
            label={t("customerModal.phone.label")}
            withAsterisk
          >
            <Input
              id="input-demo"
              placeholder={t("customerModal.phone.placeholder")}
              component={InputMask}
              mask="+99 (999) 999 99 99"
              disabled={isLoading && loadingLevel === 1}
              value={customerModal.data.phone}
              onChange={(event: any) =>
                setCustomerModal({
                  data: {
                    ...customerModal.data,
                    phone: event.target.value,
                  },
                })
              }
            />
          </Input.Wrapper>
          <Input.Wrapper
            id="input-demo"
            label={t("customerModal.email.label")}
            withAsterisk
          >
            <Input
              id="input-demo"
              placeholder={t("customerModal.email.placeholder")}
              disabled={isLoading && loadingLevel === 1}
              value={customerModal.data.email}
              onChange={(event: any) =>
                setCustomerModal({
                  data: {
                    ...customerModal.data,
                    email: event.target.value,
                  },
                })
              }
            />
          </Input.Wrapper>
        </SimpleGrid>
      </Stack>

      {/* <Stack>
          <Text size="sm" weight={600}>
            Links
          </Text>
          <ScrollArea
            styles={{
              viewport: {
                maxWidth: width > 670 ? "auto" : "calc(100vw - 76px)",
              },
            }}
          >
            <Table
              withBorder
              verticalSpacing="sm"
              horizontalSpacing="md"
              sx={{
                minWidth: 600,
              }}
            >
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Position</th>
                  <th>Phone</th>
                  <th>E-Mail</th>
                  <th></th>
                </tr>
              </thead>
              <tbody>
                {rows}
                <tr>
                  <td>
                    <Input
                      size="xs"
                      placeholder="Name"
                      disabled={isLoading && loadingLevel === 1}
                      value={newLink.name}
                      onChange={(event: any) =>
                        setNewLink({ ...newLink, name: event.target.value })
                      }
                    />
                  </td>
                  <td>
                    <Input
                      size="xs"
                      placeholder="Position"
                      disabled={isLoading && loadingLevel === 1}
                      value={newLink.position}
                      onChange={(event: any) =>
                        setNewLink({ ...newLink, position: event.target.value })
                      }
                    />
                  </td>
                  <td>
                    <Input.Wrapper required>
                      <Input
                        size="xs"
                        component={InputMask}
                        mask="+99 (999) 999-99-99"
                        placeholder="Phone"
                        disabled={isLoading && loadingLevel === 1}
                        value={newLink.phone}
                        onChange={(event: any) =>
                          setNewLink({ ...newLink, phone: event.target.value })
                        }
                      />
                    </Input.Wrapper>
                  </td>
                  <td>
                    <Input
                      size="xs"
                      placeholder="E-Mail"
                      type="email"
                      disabled={isLoading && loadingLevel === 1}
                      value={newLink.email}
                      onChange={(event: any) =>
                        setNewLink({ ...newLink, email: event.target.value })
                      }
                    />
                  </td>
                  <td>
                    <ActionIcon
                      variant="light"
                      color="blue"
                      onClick={() => addLinkToCustomer()}
                    >
                      <PlusSmallIcon style={{ width: 20, height: 20 }} />
                    </ActionIcon>
                  </td>
                </tr>
              </tbody>
            </Table>
          </ScrollArea>
        </Stack> */}

      <Stack>
        <Divider
          sx={{
            width: "100%",
          }}
          label={t("customerModal.billingInformations")}
          labelPosition="center"
        />
        <Grid gutter="sm">
          <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
            <Input.Wrapper
              id="input-demo"
              label={t("customerModal.taxDepartment.label")}
              withAsterisk
            >
              <Input
                id="input-demo"
                placeholder={t("customerModal.taxDepartment.label")}
                disabled={isLoading && loadingLevel === 1}
                value={customerModal.data.taxDepartment}
                onChange={(event: any) =>
                  setCustomerModal({
                    data: {
                      ...customerModal.data,
                      taxDepartment: event.target.value,
                    },
                  })
                }
              />
            </Input.Wrapper>
          </Grid.Col>
          <Grid.Col span={width <= 550 && width > 0 ? 12 : 6}>
            <Input.Wrapper
              id="input-demo"
              label={t("customerModal.taxNumber.label")}
              error={
                customerModal.data.location === ""
                  ? t("customerModal.pleaseSelectLocation")
                  : ""
              }
              withAsterisk
            >
              <Input
                id="input-demo"
                placeholder={t("customerModal.taxNumber.placeholder")}
                component={InputMask}
                mask={
                  customerModal.data.location === ""
                    ? "9"
                    : taxNumberMasks[customerModal.data.location]
                }
                disabled={
                  customerModal.data.location === "" ||
                  (isLoading && loadingLevel === 1)
                }
                value={customerModal.data.taxNumber}
                onChange={(event: any) =>
                  setCustomerModal({
                    data: {
                      ...customerModal.data,
                      taxNumber: event.target.value,
                    },
                  })
                }
              />
            </Input.Wrapper>
          </Grid.Col>
          <Grid.Col span={12}>
            <Textarea
              placeholder={t("customerModal.address.placeholder")}
              label={t("customerModal.address.label")}
              rows={2}
              withAsterisk
              disabled={isLoading && loadingLevel === 1}
              value={customerModal.data.address}
              onChange={(event: any) =>
                setCustomerModal({
                  data: {
                    ...customerModal.data,
                    address: event.target.value,
                  },
                })
              }
            />
          </Grid.Col>
        </Grid>
      </Stack>

      <Stack>
        <Divider
          sx={{
            width: "100%",
          }}
          label={t("customerModal.customerpersons")}
          labelPosition="center"
        />
        <Group spacing={8}>
          {customerModal.data.personIds.length === 0 && (
            <Text size="xs" weight={600} color="dimmed">
              {t("noOne")}
            </Text>
          )}
          {orderBy(
            users.filter((e) => customerModal.data.personIds.includes(e.id)),
            "id"
          ).map((manager: UserType, i: number) => {
            return (
              <Tooltip
                key={"user-" + i}
                label={`${manager.name} ${manager.surname}`}
              >
                <Box
                  sx={{
                    position: "relative",
                    borderRadius: "50%",
                    overflow: "hidden",
                  }}
                >
                  <ActiveAvatar userId={manager.id} size={34} radius="xl" />
                  <ThemeIcon
                    sx={{
                      width: "100%",
                      height: "100%",
                      position: "absolute",
                      top: 0,
                      left: 0,
                      cursor: "pointer",
                      transition: "opacity .1s ease",
                      opacity: 0,
                      ":hover": {
                        opacity: 0.7,
                      },
                    }}
                    onClick={() =>
                      removeManagerFromCustomer(manager.id as number)
                    }
                    color="red"
                  >
                    <XMarkIcon style={{ width: 20, height: 20 }} />
                  </ThemeIcon>
                </Box>
              </Tooltip>
            );
          })}
          <SelectUserModal
            onesList={customerModal.data.personIds}
            handleSelect={(id: number) => addManagerToCustomer(id)}
            handleRemove={(id: number) => removeManagerFromCustomer(id)}
          />
          <ActionIcon
            size="lg"
            radius="xl"
            variant="light"
            color="gray"
            onClick={() => setIsOpenSelectUserModal(true)}
          >
            <PlusSmallIcon style={{ width: 20, height: 20 }} />
          </ActionIcon>
        </Group>
      </Stack>

      <Divider orientation="horizontal" />

      <Group position="apart">
        {customerModal.type === "edit" ? (
          <Button
            color="red"
            leftIcon={<TrashIcon width={16} height={16} />}
            onClick={() => deleteCustomer()}
          >
            {t("delete")}
          </Button>
        ) : (
          <Group></Group>
        )}
        <Group>
          <Button variant="light" color="gray" onClick={() => closeModal()}>
            {t("cancel")}
          </Button>
          <Button onClick={() => saveCustomer()}>{t("save")}</Button>
        </Group>
      </Group>
    </Stack>
  );
};

const CustomerModal: CC<{
  updateCustomers: () => Promise<void>;
}> = function ({ updateCustomers }) {
  const {
    customers,
    filteredCustomers,
    addCustomer,
    setCustomer,
    actionUpdateCustomer,
  } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
      filteredCustomers: state.filteredCustomers!,
      addCustomer: state.addCustomer!,
      setCustomer: state.setCustomer!,
      actionUpdateCustomer: state.actionUpdateCustomer!,
    }),
    shallow
  );
  const { deleteData } = useStore(
    "global",
    (state) => ({
      deleteData: state.deleteData!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    customerModal,
    setCustomerModal,
    blankCustomer,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      customerModal: state.customerModal!,
      setCustomerModal: state.setCustomerModal!,
      blankCustomer: state.computed!.blankCustomer!,
    }),
    shallow
  );

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const activeUser = users.find((userData) => userData.id === activeUserId);

  const { t } = useTranslation();

  // const addLinkToCustomer = useCallback(async () => {
  //   if (
  //     newLink.name === "" ||
  //     newLink.position === "" ||
  //     newLink.phone === "" ||
  //     newLink.email === ""
  //   ) {
  //     return;
  //   }

  //   const a = { ...newLink, id: "link-" + customerModal.data.links.length };

  //   setCustomerModal({
  //     data: {
  //       ...customerModal.data,
  //       links: [...customerModal.data.links, a],
  //     },
  //   });

  //   setNewLink(blankNewLink);
  // }, [customerModal.data, newLink, setCustomerModal]);

  // const removeLinkFromCustomer = useCallback(
  //   (id: string) => {
  //     let newBlankCustomerLinks = customerModal.data.links.filter(
  //       (e) => e.id !== id
  //     );

  //     setCustomerModal({
  //       data: {
  //         ...customerModal.data,
  //         links: newBlankCustomerLinks,
  //       },
  //     });
  //   },
  //   [customerModal, setCustomerModal]
  // );

  // const rows = customerModal.data.links.map((element: any) => (
  //   <tr key={element.id}>
  //     <td>{element.name}</td>
  //     <td>{element.position}</td>
  //     <td>{element.phone}</td>
  //     <td>{element.email}</td>
  //     <td>
  //       <ActionIcon
  //         variant="light"
  //         color="red"
  //         onClick={() => removeLinkFromCustomer(element.id)}
  //       >
  //         <XMarkIcon style={{ width: 16, height: 16 }} />
  //       </ActionIcon>
  //     </td>
  //   </tr>
  // ));

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setCustomerModal({
      isOpen: false,
    });
    setTimeout(() => {
      setCustomerModal({
        data: blankCustomer,
      });
    }, 300);
  }, [isLoading, loadingLevel, setCustomerModal, blankCustomer]);

  const deleteCustomer = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await deleteData("customers", customerModal.data.id);
    } finally {
      closeModal();
      setIsLoading(false);
      updateCustomers();
    }
  }, [
    closeModal,
    customerModal.data.id,
    deleteData,
    setIsLoading,
    setLoadingLevel,
    updateCustomers,
  ]);

  const saveCustomer = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    const newCustomerModal = cloneDeep(customerModal);

    if (!newCustomerModal.data.chatId) {
      newCustomerModal.changes["chats"] = [
        {
          type: "organization",
          ownerId: activeUserId,
          organizationId:
            newCustomerModal.type === "new"
              ? "$customers.-1.id"
              : newCustomerModal.data.id,
        },
      ];
    }

    try {
      await actionUpdateCustomer(newCustomerModal);

      if (newCustomerModal.type === "new") {
        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.customerCreate"),
          autoClose: 3000,
        });
      } else if (
        !isEqual(
          newCustomerModal.data,
          customers.filter((e) => e.id === newCustomerModal.data.id)[0]
        )
      ) {
        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.customerUpdate"),
          autoClose: 3000,
        });
      }
    } finally {
      closeModal();
      setIsLoading(false);
      await updateCustomers();
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    customerModal,
    activeUserId,
    actionUpdateCustomer,
    customers,
    t,
    closeModal,
    updateCustomers,
  ]);

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });

  return (
    <Modal
      opened={customerModal.isOpen}
      onClose={() => {
        if (isLoading && loadingLevel === 1) {
          return;
        }

        if (
          customerModal.type === "edit"
            ? !isEqual(
                customerModal.data,
                filteredCustomers.filter(
                  (e) => e.id === customerModal.data.id
                )[0]
              )
            : !isEqual(blankCustomer, customerModal.data)
        ) {
          confirmModal();
        } else {
          closeModal();
        }
      }}
      title={t(`customerModal.title.${customerModal.type}`)}
      centered
      transition={customerModal.isOpen ? "slide-down" : "slide-up"}
      size={800}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <CustomerModalContent
        saveCustomer={saveCustomer}
        closeModal={closeModal}
        deleteCustomer={deleteCustomer}
      />
    </Modal>
  );
};

export default CustomerModal;
