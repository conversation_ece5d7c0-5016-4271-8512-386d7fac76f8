import { MoonIcon, SunIcon } from "@heroicons/react/24/outline";
import { ActionIcon } from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

const ColorSchemeToggler = function (props: any) {
  const { colorScheme, toggleColorScheme } = useStore(
    "global",
    (state) => ({
      colorScheme: state.colorScheme!,
      toggleColorScheme: state.toggleColorScheme!,
    }),
    shallow
  );

  return (
    <ActionIcon
      onClick={() => toggleColorScheme()}
      variant="subtle"
      sx={(theme) => ({
        color:
          theme.colorScheme === "dark"
            ? theme.colors.yellow[4]
            : theme.colors.blue[6],
      })}
      size="lg"
    >
      {colorScheme === "dark" ? (
        <MoonIcon style={{ width: 22, height: 22 }} />
      ) : (
        <SunIcon style={{ width: 22, height: 22 }} />
      )}
    </ActionIcon>
  );
};

export default ColorSchemeToggler;
