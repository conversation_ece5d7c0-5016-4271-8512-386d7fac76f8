import { XMarkIcon } from "@heroicons/react/24/outline";
import {
  Stack,
  Button,
  Divider,
  Text,
  Checkbox,
  Group,
  ActionIcon,
} from "@mantine/core";
import { useClickOutside, useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { TaskStatusTypes } from "~utils/types/Task";

const CalendarSidebar = function (props: any) {
  const { sidebarOpen, setSidebarOpen } = props;
  const ref = useClickOutside(() => setSidebarOpen(false));
  const matches = useMediaQuery("(max-width: 992px)");
  const { t } = useTranslation();

  const { taskStatusList, statusColors } = useStore(
    "tasks",
    (state) => ({
      taskStatusList: state.taskStatusList!,
      statusColors: state.statusColors!,
    }),
    shallow
  );
  const {
    setIsOpenNewTaskModal,
    activeCalendarFilter,
    setActiveCalendarFilter,
  } = useStore(
    "temp",
    (state) => ({
      setIsOpenNewTaskModal: state.setIsOpenNewTaskModal!,
      activeCalendarFilter: state.activeCalendarFilter!,
      setActiveCalendarFilter: state.setActiveCalendarFilter!,
    }),
    shallow
  );

  const allChecked = activeCalendarFilter.length === taskStatusList.length;

  return (
    <Stack
      ref={ref}
      p="md"
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        borderRadius: theme.radius.md,
        width: 220,
        flexShrink: 0,
        transition: "all .2s ease",
        zIndex: 10,
        [`@media (max-width: ${theme.breakpoints.md}px)`]: {
          position: "absolute",
          top: 0,
          bottom: 0,
          left: sidebarOpen ? 0 : "-100%",
        },
      })}
    >
      <Group spacing="sm" sx={{ flexWrap: "nowrap" }}>
        <Button
          size="sm"
          fullWidth
          variant="light"
          onClick={() => setIsOpenNewTaskModal(true)}
        >
          {t("calendar.createTask")}
        </Button>
        {matches && (
          <ActionIcon>
            <XMarkIcon
              style={{ width: 20, height: 20 }}
              onClick={() => setSidebarOpen(false)}
            />
          </ActionIcon>
        )}
      </Group>
      <Divider />
      <Stack spacing={4}>
        <Text color="dimmed" weight={600} size="sm">
          {t("calendar.status")}
        </Text>
        <Checkbox
          color="teal"
          label={t("calendar.viewAll")}
          checked={allChecked}
          indeterminate={activeCalendarFilter.length > 0 && !allChecked}
          onChange={(event) => {
            if (event.target.checked) {
              setActiveCalendarFilter(taskStatusList);
            } else {
              setActiveCalendarFilter([]);
            }
          }}
        />
        {taskStatusList.map((status: TaskStatusTypes, i: number) => (
          <Checkbox
            key={"filter-" + i}
            label={t(`status.${status}`)}
            color={statusColors[status]}
            checked={activeCalendarFilter.includes(status)}
            onChange={(event) => {
              if (event.target.checked) {
                setActiveCalendarFilter([...activeCalendarFilter, status]);
              } else {
                setActiveCalendarFilter(
                  activeCalendarFilter.filter((e) => e !== status)
                );
              }
            }}
          />
        ))}
      </Stack>
    </Stack>
  );
};

export default CalendarSidebar;
