import { Box } from "@mantine/core";
import { hexToRGBA } from "~utils/tools";

const CalendarWrapper = function (props: any) {
  const { children } = props;

  return (
    <Box
      sx={(theme: any) => ({
        width: "100%",
        height: "100%",
        display: "flex",
        position: "relative",
        "& .fc": {
          width: "100%",
          height: "100%",
          zIndex: 1,

          "& .fc-view": {
            height: "100%",
          },
          "& table": {
            height: "100%",
          },
          "& .fc-scroller-harness": {
            height: "100%",
          },
          "& .fc-scroller": {
            height: "100%",
          },
          "& .fc-daygrid-body": {
            height: "100%",
          },
          "& .fc-scrollgrid-sync-table": {
            height: "100%",
          },

          "& .fc-h-event": {
            border: "none !important",
          },

          // ** Toolbar
          "& .fc-toolbar": {
            flexWrap: "wrap",
            flexDirection: "row",
            alignItems: "center",
            "&.fc-header-toolbar": {
              marginBottom: theme.spacing.md,
              flexWrap: "nowrap",
            },
            ".fc-prev-button, & .fc-next-button": {
              display: "inline-block",
              borderColor: "transparent",
              backgroundColor:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[1],
              padding: `8px !important`,
              "& .fc-icon": {
                fontSize: "1.15rem",
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[2]
                    : theme.colors.dark[8],
              },
              "&:hover, &:active, &:focus": {
                boxShadow: "none !important",
                borderColor: "transparent !important",
                backgroundColor:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[5]
                    : theme.colors.gray[2],
              },
            },
            "& .fc-toolbar-chunk:first-of-type": {
              display: "flex",
              flexWrap: "wrap",
              alignItems: "center",
              [theme.breakpoints.md]: {
                "& div:first-of-type": {
                  display: "flex",
                  alignItems: "center",
                },
              },
            },
            "& .fc-button": {
              padding: theme.spacing.md,
              "&:active, .&:focus": {
                boxShadow: "none",
              },
            },
            "& .fc-button-group": {
              borderRadius: theme.radius.md,
              overflow: "hidden",
              "& .fc-button": {
                textTransform: "capitalize",
                "&:focus": {
                  boxShadow: "none",
                },
              },
              "& .fc-button-primary": {
                fontWeight: 500,
                fontSize: ".875rem",
                letterSpacing: ".4px",
                textTransform: "uppercase",

                "&:not(.fc-prev-button):not(.fc-next-button)": {
                  backgroundColor: "transparent",
                  padding: "8px 12px",
                  color: theme.colors.blue[9],
                  borderColor: hexToRGBA(theme.colors.blue[9], 0.5),
                  "&.fc-button-active, &:hover": {
                    borderColor: hexToRGBA(theme.colors.blue[9], 0.5),
                    backgroundColor: hexToRGBA(theme.colors.blue[9], 0.05),
                  },
                },
              },
              "& .fc-sidebarToggle-button": {
                border: 0,
                borderColor: "transparent",
                paddingBottom: "0 !important",
                backgroundColor: "transparent",
                padding: "0px 8px !important",
                marginRight: 8,
                color: `${
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[1]
                    : theme.colors.dark[7]
                } !important`,
                "&:focus": {
                  outline: 0,
                  boxShadow: "none",
                },
                "&:not(.fc-prev-button):not(.fc-next-button):hover": {
                  backgroundColor: "transparent !important",
                },
                "& + div": {
                  marginLeft: 0,
                },
              },
              ".fc-dayGridMonth-button, .fc-timeGridWeek-button, .fc-timeGridDay-button, & .fc-listMonth-button":
                {
                  padding: `8px 12px`,

                  "&:last-of-type, &:first-of-type": {
                    borderRadius: theme.radius.md,
                  },
                  "&:first-of-type": {
                    borderTopRightRadius: 0,
                    borderBottomRightRadius: 0,
                  },
                  "&:last-of-type": {
                    borderTopLeftRadius: 0,
                    borderBottomLeftRadius: 0,
                  },
                },
            },
            "& > * > :not(:first-of-type)": {
              marginLeft: 0,
            },
            "& .fc-toolbar-title": {
              fontWeight: 500,
              lineHeight: "2rem",
              marginRight: theme.spacing.lg,
              marginLeft: theme.spacing.md,
              fontSize: theme.fontSizes.lg,
            },
            ".fc-button:empty, & .fc-toolbar-chunk:empty": {
              display: "none",
            },
          },

          // ** Calendar head & body common
          "& tbody td, & thead th": {
            borderColor:
              theme.colorScheme === "dark"
                ? theme.colors.gray[8]
                : theme.colors.gray[3],
            "&.fc-col-header-cell": {
              borderLeft: 0,
              borderRight: 0,
            },
          },

          // ** Event Colors
          "& .fc-event": {
            borderRadius: 4,
            "&:not(.fc-list-event)": {
              "&.bg-primary": {
                borderColor: "transparent",
                color: theme.colors.blue[9],
                backgroundColor: hexToRGBA(theme.colors.blue[9], 0.12),
                "& .fc-event-title, & .fc-event-time": {
                  color: theme.colors.blue[9],
                },
              },
              "&.bg-success": {
                borderColor: "transparent",
                color: theme.colors.green[9],
                backgroundColor: hexToRGBA(theme.colors.green[9], 0.12),
                "& .fc-event-title, & .fc-event-time": {
                  color: theme.colors.green[9],
                },
              },
              "&.bg-error": {
                borderColor: "transparent",
                color: theme.colors.red[9],
                backgroundColor: hexToRGBA(theme.colors.red[9], 0.12),
                "& .fc-event-title, & .fc-event-time": {
                  color: theme.colors.red[9],
                },
              },
              "&.bg-warning": {
                borderColor: "transparent",
                color: theme.colors.orange[9],
                backgroundColor: hexToRGBA(theme.colors.orange[9], 0.12),
                "& .fc-event-title, & .fc-event-time": {
                  color: theme.colors.orange[9],
                },
              },
              "&.bg-info": {
                borderColor: "transparent",
                color: theme.colors.gray[7],
                backgroundColor: hexToRGBA(theme.colors.gray[7], 0.12),
                "& .fc-event-title, & .fc-event-time": {
                  color: theme.colors.gray[7],
                },
              },
            },
            "&.bg-primary": {
              "& .fc-list-event-dot": {
                borderColor: theme.colors.blue[9],
                backgroundColor: theme.colors.blue[9],
              },
              "&:hover td": {
                backgroundColor: hexToRGBA(theme.colors.blue[7], 0.1),
              },
            },
            "&.bg-success": {
              "& .fc-list-event-dot": {
                borderColor: theme.colors.green[9],
                backgroundColor: theme.colors.green[9],
              },
              "&:hover td": {
                backgroundColor: hexToRGBA(theme.colors.green[7], 0.1),
              },
            },
            "&.bg-error": {
              "& .fc-list-event-dot": {
                borderColor: theme.colors.red[9],
                backgroundColor: theme.colors.red[9],
              },
              "&:hover td": {
                backgroundColor: hexToRGBA(theme.colors.red[7], 0.1),
              },
            },
            "&.bg-warning": {
              "& .fc-list-event-dot": {
                borderColor: theme.colors.orange[9],
                backgroundColor: theme.colors.orange[9],
              },
              "&:hover td": {
                backgroundColor: hexToRGBA(theme.colors.orange[7], 0.1),
              },
            },
            "&.bg-info": {
              "& .fc-list-event-dot": {
                borderColor: theme.colors.gray[7],
                backgroundColor: theme.colors.gray[7],
              },
              "&:hover td": {
                backgroundColor: hexToRGBA(theme.colors.gray[7], 0.1),
              },
            },
            "&.fc-daygrid-event": {
              marginLeft: "4px",
              marginRight: "4px",
            },
          },

          "& .fc-scrollgrid-section-sticky > *": {
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[8]
                : theme.colors.gray[1],
          },

          // ** Calendar Head
          "& .fc-col-header": {
            "& .fc-col-header-cell": {
              fontWeight: 600,
              fontSize: ".875rem",
              letterSpacing: ".15px",
              color:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[2]
                  : theme.colors.dark[8],
              "& .fc-col-header-cell-cushion": {
                padding: theme.spacing.md,
                textDecoration: "none !important",
              },
            },
          },

          // ** Daygrid
          "& .fc-scrollgrid-section-liquid > td": {
            borderBottom: 0,
          },
          "& .fc-daygrid-event-harness": {
            lineHeight: 1.25,
            "& .fc-event": {
              fontWeight: 500,
              fontSize: "0.75rem",
              padding: "4px 8px",
              "& .fc-event-time": {
                fontWeight: 500,
              },
            },
            "&:not(:last-of-type)": {
              marginBottom: theme.spacing.sm,
            },
          },
          "& .fc-daygrid-day-bottom": {
            marginTop: theme.spacing.sm,
          },
          "& .fc-daygrid-day": {
            "& .fc-daygrid-day-top": {
              flexDirection: "row",
            },
            "&.fc-day-other": {
              "& .fc-daygrid-day-top": {
                opacity: 1,
                "& .fc-daygrid-day-number": {
                  color: `${
                    theme.colorScheme === "dark"
                      ? theme.colors.gray[8]
                      : theme.colors.dark[1]
                  } !important`,
                },
              },
            },
            "&.fc-day-past:not(.fc-day-other)": {
              "& .fc-daygrid-day-number": {
                color: `${
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[2]
                    : theme.colors.dark[8]
                } !important`,
              },
            },
          },
          "& .fc-scrollgrid": {
            borderRadius: 8,
            borderColor:
              theme.colorScheme === "dark"
                ? theme.colors.gray[8]
                : theme.colors.gray[3],
            overflow: "hidden",
          },
          "& .fc-day-past, & .fc-day-future": {
            "&.fc-daygrid-day-number": {
              color: theme.colors.gray[7],
            },
          },

          // ** All Views Event
          "& .fc-daygrid-day-number": {
            fontSize: "1rem",
            paddingTop: 0,
            paddingLeft: theme.spacing.md,
          },
          "& .fc-daygrid-day-number, & .fc-timegrid-slot-label-cushion, & .fc-list-event-time":
            {
              textDecoration: "none !important",
              color: `${
                theme.colorScheme === "dark"
                  ? theme.colors.gray[2]
                  : theme.colors.dark[8]
              } !important`,
            },
          "& .fc-day-today": {
            "&:not(.fc-col-header-cell)": {
              background: `${
                theme.colorScheme === "dark" ? theme.colors.dark[6] : "#fdfdfd"
              } !important`,
            },
          },

          // ** WeekView
          "& .fc-timegrid": {
            "& .fc-scrollgrid-section": {
              "& .fc-col-header-cell, & .fc-timegrid-axis": {
                borderLeft: 0,
                borderRight: 0,
                borderColor: theme.colors.gray[7],
              },
              "& .fc-timegrid-axis": {
                borderColor: theme.colors.gray[7],
              },
            },
            "& .fc-timegrid-axis": {
              "&.fc-scrollgrid-shrink": {
                "& .fc-timegrid-axis-cushion": {
                  fontSize: ".75rem",
                  lineHeight: "15px",
                  letterSpacing: "0.4px",
                  textTransform: "capitalize",
                  color: theme.colors.gray[7],
                },
              },
            },
            "& .fc-timegrid-slots": {
              "& .fc-timegrid-slot": {
                height: "3rem",
                borderColor: theme.colors.gray[7],
                "& .fc-timegrid-slot-label-frame": {
                  textAlign: "center",
                  "& .fc-timegrid-slot-label-cushion": {
                    fontSize: ".75rem",
                    lineHeight: "15px",
                    letterSpacing: "0.4px",
                    textTransform: "uppercase",
                  },
                },
              },
            },
            "& .fc-timegrid-divider": {
              display: "none",
            },
            "& .fc-timegrid-event": {
              borderRadius: 0,
              boxShadow: "none",
              paddingTop: theme.spacing.md,
              paddingLeft: theme.spacing.md,
              "& .fc-event-time": {
                marginBottom: "2px",
              },
              "& .fc-event-time, & .fc-event-title": {
                fontSize: ".75rem",
                lineHeight: "15px",
                letterSpacing: "0.4px",
              },
            },
          },

          // ** List View
          "& .fc-list": {
            width: "100%",
            border: "none",
            '& th[colspan="3"]': {
              position: "relative",
            },
            "& .fc-list-day-cushion": {
              paddingLeft: theme.spacing.lg,
              paddingRight: theme.spacing.lg,
              background:
                theme.colorScheme === "dark" ? theme.colors.dark[6] : "#fdfdfd",
              "& .fc-list-day-text, & .fc-list-day-side-text": {
                fontWeight: 600,
              },
            },
            ".fc-list-event": {
              cursor: "pointer",
              "&:hover": {
                "& td": {
                  backgroundColor: theme.colors.gray[7],
                },
              },
              "& td": {
                borderColor: theme.colors.gray[7],
              },
            },
            "& .fc-list-day": {
              backgroundColor: "#fdfdfd",

              "& .fc-list-day-text, & .fc-list-day-side-text": {
                fontSize: ".875rem",
                textDecoration: "none",
              },

              "&  >  *": {
                background: "none",
                borderColor: theme.colors.gray[7],
              },
            },
            "& .fc-list-event-title": {
              fontSize: ".875rem",
              verticalAlign: "middle",
              paddingLeft: theme.spacing.md,
              color: theme.colors.gray[9],
            },
            "& .fc-list-event-time": {
              fontSize: ".875rem",
              paddingLeft: theme.spacing.lg,
              color: `${theme.colors.gray[9]} !important`,
            },
          },

          // ** Popover
          "& .fc-popover": {
            boxShadow: "1",
            borderColor: theme.colors.gray[7],
            background: theme.colors.dark[8],
            "& .fc-popover-header": {
              padding: theme.spacing.md,
              background: theme.colors.gray[7],
              "& .fc-popover-title, & .fc-popover-close": {
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[2]
                    : theme.colors.dark[8],
              },
            },
            "& .fc-popover-body": {
              "& *:not(:last-of-type)": {
                marginBottom: theme.spacing.sm,
              },
            },
          },

          [`@media (max-width: ${theme.breakpoints.md}px)`]: {
            "& .fc-scrollgrid": {
              minWidth: 1000,
            },
          },

          [`@media (min-width: ${theme.breakpoints.md}px)`]: {
            "& .fc-sidebarToggle-button": {
              display: "none",
            },
          },
        },
      })}
    >
      {children}
    </Box>
  );
};

export default CalendarWrapper;
