import { Group, ScrollArea } from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { KanbanColumn } from "./Column";

import { DragDropContext } from "react-beautiful-dnd";
import { useMediaQuery } from "@mantine/hooks";
import { useCallback, useEffect, useRef } from "react";
import { useTranslation } from "next-i18next";
import { showNotification } from "@mantine/notifications";
import { uniq } from "lodash";

export const KanbanBoard = function () {
  const {
    groupByStatus,
    tasks,
    actionUpdateMultipleTask,
    actionGetTasks,
    actionResetTasks,
  } = useStore(
    "tasks",
    (state) => ({
      groupByStatus: state.computed!.groupByStatus!,
      tasks: state.tasks!,
      actionGetTasks: state.actionGetTasks!,
      actionResetTasks: state.actionResetTasks!,
      actionUpdateMultipleTask: state.actionUpdateMultipleTask!,
    }),
    shallow
  );
  const { isNavbarMinimized, isNavbarHover, activeUserId, selectedProjectId } =
    useStore(
      "global",
      (state) => ({
        isNavbarMinimized: state.isNavbarMinimized!,
        isNavbarHover: state.isNavbarHover!,
        activeUserId: state.activeUserId!,
        selectedProjectId: state.selectedProjectId,
      }),
      shallow
    );
  const { setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 992px)");
  const { t } = useTranslation();

  const updateTask = useCallback(
    async (data: any) => {
      setIsLoading(true);
      setLoadingLevel(0);

      try {
        await actionUpdateMultipleTask([
          {
            ...data,
            id: +data.id,
            contributorIds: uniq([...data.contributorIds, activeUserId]),
          },
        ]);

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.taskUpdate"),
          autoClose: 3000,
        });
      } finally {
        setIsLoading(false);
      }
    },
    [actionUpdateMultipleTask, activeUserId, setIsLoading, setLoadingLevel, t]
  );

  const updateTasks = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    if (!selectedProjectId) {
      showNotification({
        color: "red",
        title: t("task.project.fail.title"),
        message: t("task.project.fail.message"),
        autoClose: 3000,
      });

      setIsLoading(false);
    }

    try {
      await actionGetTasks({
        userId: activeUserId,
        projectId: selectedProjectId,
      });
    } finally {
      setIsLoading(false);
    }
  }, [
    actionGetTasks,
    activeUserId,
    selectedProjectId,
    setIsLoading,
    setLoadingLevel,
    t,
  ]);

  useEffect(() => {
    updateTasks();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProjectId]);

  const onDragEnd = async (result: any) => {
    if (!result.destination) {
      return;
    }

    if (!result.source) {
      return;
    }

    const matchGroup = groupByStatus.find(
      (group) => group[0][0] === result.source.droppableId
    );

    if (!matchGroup) {
      return;
    }

    const list = matchGroup[1];
    const data = list[0];

    await updateTask({
      ...data,
      status: result.destination.droppableId,
    });

    await updateTasks();
  };

  useEffect(() => {
    actionResetTasks();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ScrollArea
      sx={(theme) => ({
        height: "calc(100vh - 70px)",
        maxWidth: matches
          ? "100vw"
          : !isNavbarMinimized || isNavbarHover
          ? "calc(100vw - 298px)"
          : "calc(100vw - 78px)",
      })}
      offsetScrollbars
    >
      <Group
        align="start"
        id="test"
        sx={{
          flexWrap: "nowrap",
          overflow: "auto",
          overflowX: "hidden",
        }}
        p={32}
      >
        <DragDropContext onDragEnd={onDragEnd}>
          {groupByStatus.map(([[columnName, color], cards]) => {
            return (
              <KanbanColumn
                key={columnName}
                prefix={columnName}
                cards={cards}
                color={color}
              />
            );
          })}
        </DragDropContext>
      </Group>
    </ScrollArea>
  );
};
