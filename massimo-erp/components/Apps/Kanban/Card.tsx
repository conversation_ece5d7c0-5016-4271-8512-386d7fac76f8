import {
  Box,
  Group,
  Text,
  Paper,
  Space,
  Stack,
  Divider,
  Badge,
  Avatar,
  Tooltip,
} from "@mantine/core";
import { Draggable } from "react-beautiful-dnd";
import { DocumentCheckIcon, CalendarIcon } from "@heroicons/react/24/outline";

import { CC } from "~types";
import { TaskSubtaskType, TaskType } from "~utils/types/Task";
import { formatDate } from "~utils/tools";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useCallback, useEffect, useState } from "react";
import { findLast } from "lodash";
import ActiveAvatar from "~components/ActiveAvatar";
import { MessageType } from "~utils/types/Chat";

export const KanbanCard: CC<{
  index: number;
  color: string;
  card: TaskType;
  prefix: string;
}> = function (props) {
  const { card, prefix, index } = props;

  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { chats, messages } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      messages: state.messages!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { setIsOpenTaskViewModal, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );

  const openTask = useCallback(async () => {
    await setTaskViewModalData(card);
    setIsOpenTaskViewModal(true);
  }, [card, setIsOpenTaskViewModal, setTaskViewModalData]);

  const activeChat = chats.find(
    (e) => e.taskId === card.id && e.type === "task"
  );
  const subtaskMessages = messages.filter(
    (e) => e.chatId === activeChat?.id && e.content.type === "subtask"
  );

  const color = priorityColors[card.priority];
  const [totalProgress, setTotalProgress] = useState("0/0");

  useEffect(() => {
    let totalSubtask = 0;
    let completedSubtask = 0;

    subtaskMessages.map((message: MessageType) => {
      message.content.subtasks.map((st: TaskSubtaskType) => {
        totalSubtask++;
        if (st.completed) {
          completedSubtask++;
        }
      });
    });

    setTotalProgress(`${completedSubtask}/${totalSubtask}`);
  }, [subtaskMessages]);

  return (
    <>
      <Draggable draggableId={`${card.id}`} index={index}>
        {(provided, snapshot) => {
          return (
            <div
              ref={provided.innerRef}
              //@ts-ignore
              snapshot={snapshot}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
            >
              <Paper
                p="sm"
                sx={(theme: any) => ({
                  backgroundColor:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[2],
                  display: "flex",
                  alignItems: "stretch",
                  userSelect: "none",
                  "&:hover": {
                    backgroundColor:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[5]
                        : theme.colors.gray[3],
                  },
                })}
                onClick={() => openTask()}
              >
                <Box
                  sx={(theme: any) => ({
                    height: "auto",
                    width: 4,
                    background: theme.colors[color][7],
                    borderRadius: 8,
                  })}
                />
                <Space w="sm" />
                <Stack spacing={8} sx={{ width: "100%" }}>
                  <Group position="apart">
                    <Stack spacing={8}>
                      <Text weight={600}> {card.title} </Text>
                      {/* <Group spacing={8}>
                        {card.tags?.map((tag: any, i: number) => (
                          <Badge
                            key={"cardTag-" + i}
                            color={tag.color}
                            size="sm"
                            variant="light"
                            p="xs"
                            sx={{ textTransform: "capitalize" }}
                          >
                            {tag.label}
                          </Badge>
                        ))}
                      </Group> */}
                    </Stack>
                    <Avatar.Group spacing="sm">
                      {card.assigneeIds
                        .slice(0, 5)
                        .map((assigneeId: number, i: number) => {
                          const activeUser = users.find(
                            (e) => e.id === assigneeId
                          )!;

                          if (!activeUser) {
                            return null;
                          }

                          return (
                            <Tooltip
                              key={activeUser.id}
                              label={`${activeUser.name} ${activeUser.surname}`}
                            >
                              <ActiveAvatar
                                userId={assigneeId}
                                size={32}
                                radius="xl"
                              />
                            </Tooltip>
                          );
                        })}
                    </Avatar.Group>
                  </Group>
                  <Divider />
                  <Group position="apart">
                    <Group spacing={8} color="red">
                      <DocumentCheckIcon
                        style={{ width: 20, height: 20, color: "currentcolor" }}
                      />
                      <Text size="sm" weight={600} color="dimmed">
                        {totalProgress}
                      </Text>
                    </Group>
                    <Group spacing={8} color="red">
                      <CalendarIcon
                        style={{ width: 20, height: 20, color: "currentcolor" }}
                      />
                      <Text size="sm" weight={600} color="dimmed">
                        {formatDate(card.end)}
                      </Text>
                    </Group>
                  </Group>
                </Stack>
              </Paper>
            </div>
          );
        }}
      </Draggable>
    </>
  );
};
/**
 *<Paper
        p="sm"
        draggable
        ref={ref}
        sx={(theme: any) => ({
          backgroundColor:
            theme.colorScheme === "dark" ? theme.colors.dark[6] : theme.light,
          display: "flex",
          alignItems: "stretch",
          userSelect: "none",
          "&:hover": {
            backgroundColor:
              theme.colorScheme === "dark"
                ? theme.colors.dark[5]
                : theme.colors.gray[1],
          },
          opacity: opacity,
        })}
        data-handler-id={handlerId}
      >
        <Box
          sx={(theme: any) => ({
            height: "auto",
            width: 4,
            background: theme.colors[color][7],
            borderRadius: 8,
          })}
        />
        <Space w="sm" />
        <Stack spacing={8} sx={{ width: "100%" }}>
          <Group position="apart">
            <Stack spacing={8}>
              <Text weight={600}> {card.title} </Text>
              {card.tags.map((tag: any, i: number) => (
                <Badge
                  key={"cardTag-" + i}
                  color={tag.color}
                  size="sm"
                  variant="light"
                  p="xs"
                  sx={{ textTransform: "capitalize" }}
                >
                  {tag.label}
                </Badge>
              ))}
            </Stack>
            <Avatar.Group spacing="sm">
              {card.contributors.map((contributor: any, i: number) => (
                <Tooltip key={contributor.id} label={contributor.name}>
                  <Avatar size="md" src={contributor.avatar} radius="xl" />
                </Tooltip>
              ))}
            </Avatar.Group>
          </Group>
          <Divider />
          <Group position="apart">
            <Group spacing={8} color="red">
              <DocumentCheckIcon
                style={{ width: 20, height: 20, color: "currentcolor" }}
              />
              <Text size="sm" weight={600} color="dimmed">
                {card.completedSubTasks}/{card.totalSubTasks}
              </Text>
            </Group>
            <Group spacing={8} color="red">
              <CalendarIcon
                style={{ width: 20, height: 20, color: "currentcolor" }}
              />
              <Text size="sm" weight={600} color="dimmed">
                {formatDate(card.endDate)}
              </Text>
            </Group>
          </Group>
        </Stack>
      </Paper>
 */
