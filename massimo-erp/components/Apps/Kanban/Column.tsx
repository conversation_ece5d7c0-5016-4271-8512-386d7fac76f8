import { Group, Paper, Space, Stack, Text } from "@mantine/core";
import { CC } from "~types";
import { TaskType } from "~utils/types/Task";
import { KanbanCard } from "./Card";

import { Droppable } from "react-beautiful-dnd";

import { useEffect, useState } from "react";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useTranslation } from "next-i18next";

export const useStrictDroppable = (loading: boolean) => {
  const [enabled, setEnabled] = useState(false);

  useEffect(() => {
    let animation: any;

    if (!loading) {
      animation = requestAnimationFrame(() => setEnabled(true));
    }

    return () => {
      cancelAnimationFrame(animation);
      setEnabled(false);
    };
  }, [loading]);

  return [enabled];
};

export const KanbanColumn: CC<{
  color: string;
  prefix: string;
  cards: TaskType[];
}> = function (props: any) {
  const { prefix, color, cards } = props;
  const { initialized } = useStore(
    "temp",
    (state) => ({
      initialized: state.initialized!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <Paper
      shadow="sm"
      radius="md"
      p="md"
      withBorder
      sx={(theme) => ({
        width: 320,
        flexShrink: 0,
        backgroundColor:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[0],
      })}
    >
      <Group>
        <Text weight={600} sx={{ userSelect: "none" }}>
          {t(`status.${prefix}`)}
        </Text>
      </Group>
      <Space h="xs" />
      <Droppable droppableId={`${prefix}`}>
        {(provided) => (
          <Stack
            {...provided.droppableProps}
            ref={provided.innerRef}
            sx={{
              minHeight: "50px",
            }}
          >
            {cards.map((card: any, index: number) => (
              <KanbanCard
                key={index}
                color={color}
                card={card}
                index={index}
                prefix={prefix}
              />
            ))}
            {provided.placeholder}
          </Stack>
        )}
      </Droppable>
    </Paper>
  );
};
