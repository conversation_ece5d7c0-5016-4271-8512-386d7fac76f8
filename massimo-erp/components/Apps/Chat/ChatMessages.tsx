import { ReactNode, useCallback, useEffect, useRef, useState } from "react";
import { useStore } from "~utils/store";
import { MessageType } from "~utils/types/Chat";

import {
  ChevronDownIcon,
  DocumentIcon,
  EllipsisHorizontalIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
} from "@heroicons/react/24/outline";
import {
  Box,
  Paper,
  Textarea,
  ActionIcon,
  Group,
  ScrollArea,
  Text,
  Stack,
  Menu,
  Button,
  useMantineTheme,
  LoadingOverlay,
  FileButton,
  UnstyledButton,
  SimpleGrid,
  Image,
  CloseButton,
  Space,
  ThemeIcon,
  Loader,
  Center,
  Progress,
} from "@mantine/core";
import {
  getHotkeyHandler,
  useDebouncedState,
  useElementSize,
  useFocusTrap,
  useIntersection,
  useMediaQuery,
} from "@mantine/hooks";
import shallow from "zustand/shallow";

import FileMessage from "./MessageType/File";
import ImageMessage from "./MessageType/Image";
import TaskMessage from "./MessageType/Task";
import TextMessage from "./MessageType/Text";
import { formatDate, formatTime, urlify } from "~utils/tools";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import { format } from "path";
import { showNotification } from "@mantine/notifications";
import { CC } from "~types";
import themes from "~utils/themes";
import { isNull, isNumber, isObject, orderBy } from "lodash";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { NotificationType } from "~utils/types/Notification";

const MessageTypes = {
  text: TextMessage,
  image: ImageMessage,
  file: FileMessage,
  task: TaskMessage,
};

const MessageButton: CC<{
  children: ReactNode;
  [key: string]: any;
}> = function ({ children, ...props }) {
  return (
    <Button
      fullWidth
      color="gray"
      variant="subtle"
      styles={{
        inner: {
          justifyContent: "flex-start",
          alignItems: "flex-start",
          fontWeight: 400,
        },
      }}
      {...props}
    >
      {children}
    </Button>
  );
};

const ChatMessages: CC<{
  isMobile: boolean;
}> = ({ isMobile }) => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const {
    chats,
    messages,
    activeChat,
    actionInitChat,
    actionDeleteMessage,
    actionSendMessage,
    actionGetMessages,
  } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      messages: state.messages!,
      activeChat: state.activeChat!,
      actionSendMessage: state.actionSendMessage!,
      actionDeleteMessage: state.actionDeleteMessage!,
      actionInitChat: state.actionInitChat!,
      actionGetMessages: state.actionGetMessages!,
    }),
    shallow
  );
  const { users, connections } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      connections: state.connections!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { actionUploadFiles } = useStore(
    "files",
    (state) => ({
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );
  const { uploadProgress } = useStore(
    "temp",
    (state) => ({
      uploadProgress: state.uploadProgress!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { notifications, actionSendNotifications } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
      actionSendNotifications: state.actionSendNotifications!,
    }),
    shallow
  );

  const viewport = useRef<HTMLDivElement>();
  const inputRef = useRef<HTMLTextAreaElement | null>(null);

  const activeUser = users.find((e: UserType) => e.id === activeUserId);

  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(max-width: 992px)");
  const { t } = useTranslation();

  const [scrollPosition, setScrollPositionChange] = useDebouncedState(
    { x: 0, y: 0 },
    400
  );

  const [scrollBottomButton, setScrollBottomButton] = useState(false);

  const [isChange, setIsChange] = useState(false);

  const [files, setFiles] = useState<File[]>([]);
  const [images, setImages] = useState<File[]>([]);

  const [message, setMessage] = useDebouncedState("", 200, {
    leading: true,
  });
  const [isSending, setIsSending] = useState(false);

  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);

  const focusInput = useCallback(() => {
    (inputRef.current as any).focus();
  }, []);

  const ownMessages = orderBy(messages, "id").filter(
    (e) =>
      !isNull(e) &&
      e.chatId === activeChat &&
      e.content.type !== "price" &&
      e.content.type !== "subtask"
  );

  const fetchMoreMessages = useCallback(async () => {
    let currentPage: number = 0;

    await setPage((p) => {
      currentPage = p + 1;
      return currentPage;
    });

    try {
      const hasMoreData = await actionGetMessages(activeChat, currentPage!);

      setHasMore(hasMoreData);
    } catch (err) {
      throw err;
    }
  }, [actionGetMessages, activeChat]);

  const scrollToBottomSmooth = useCallback(() => {
    viewport.current?.scrollTo({
      top: viewport.current!.scrollHeight + 100,
      behavior: "smooth",
    });
  }, []);

  const scrollToBottomAuto = useCallback(() => {
    viewport.current?.scrollTo({
      top: viewport.current!.scrollHeight + 100,
      behavior: "auto",
    });
  }, []);

  const handleDelete = useCallback(
    async (messageData: MessageType) => {
      if (!isObject(messageData)) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });

        return;
      }

      setIsSending(true);

      try {
        await actionDeleteMessage(messageData);
      } catch (err) {
        console.error(err);

        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        setIsSending(false);
      }
    },
    [actionDeleteMessage, t]
  );

  const handleSendMessage = useCallback(async () => {
    if (message === "" && images.length === 0 && files.length === 0) {
      return;
    }

    if (!isSending) {
      setIsSending(true);

      let newMessage: Partial<MessageType> = {
        type: "text",
        ownerId: activeUserId,
        chatId: activeChat,
        content: {
          text: message,
        },
      };

      try {
        if (images.length > 0) {
          const imageIds = await actionUploadFiles(
            images.map((imageFile) => ({
              userId: activeUserId,
              file: imageFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            imageIds.map((imageId) => {
              return actionSendMessage({
                type: "image",
                ownerId: activeUserId,
                chatId: activeChat,
                content: {
                  fileId: imageId,
                },
              });
            })
          );
        }

        if (files.length > 0) {
          const fileIds = await actionUploadFiles(
            files.map((fileFile) => ({
              userId: activeUserId,
              file: fileFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            fileIds.map((fileId) => {
              return actionSendMessage({
                type: "file",
                ownerId: activeUserId,
                chatId: activeChat,
                content: {
                  fileId: fileId,
                },
              });
            })
          );
        }

        if (message && message.length > 0) {
          await actionSendMessage(newMessage);
        }

        const activeChatData = chats.find((e) => e.id === activeChat);

        if (activeChatData?.type === "person") {
          await actionSendNotifications([
            {
              target: "messages",
              value: `${activeChatData.id}`,
              user: [activeChatData.personId, activeChatData.ownerId].filter(
                (e) => e !== activeUserId
              )[0],
            },
          ]);
        } else if (activeChatData?.type === "organization") {
          const activeOrganization = customers.find(
            (e) => e.id === activeChatData.organizationId
          );

          await actionSendNotifications(
            activeOrganization?.personIds
              .filter((e) => e !== activeUserId)
              .map((e) => ({
                target: "messages",
                value: `${activeChatData.id}`,
                user: e,
              })) || []
          );
        }
      } catch (error) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        scrollToBottomSmooth();
        if (inputRef.current) {
          inputRef.current.value = "";
        }
        setIsSending(false);
        setFiles([]);
        setImages([]);
        focusInput();
      }
    }
  }, [
    actionSendMessage,
    actionSendNotifications,
    actionUploadFiles,
    activeChat,
    activeUserId,
    chats,
    customers,
    files,
    focusInput,
    images,
    isSending,
    message,
    scrollToBottomSmooth,
    t,
  ]);

  const handleSelect = useCallback(
    (key: "files" | "images", data: File[]) => {
      const dataList: File[] = [];

      data.forEach((e) => {
        if (e.size >= 10485760) {
          showNotification({
            color: "red",
            title: t("largeFile.title"),
            message: t("largeFile.message"),
            autoClose: 3000,
            styles: {
              root: {
                zIndex: 400,
              },
            },
          });
        } else {
          dataList.push(e);
        }
      });

      if (key === "files") {
        setFiles([...files, ...dataList].slice(0, 20));
      } else {
        setImages([...images, ...dataList].slice(0, 20));
      }
    },
    [files, images, t]
  );

  useEffect(() => {
    let abort = false;

    if (viewport.current) {
      setTimeout(() => {
        if (!abort) {
          scrollToBottomAuto();
        }
      }, 10);
    }

    return () => {
      abort = true;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [viewport, activeChat]);

  useEffect(() => {
    if (activeChat > 0) {
      setIsChange(true);

      actionInitChat(activeChat).then(() => {
        if (viewport.current) {
          setTimeout(() => {
            scrollToBottomAuto();
            setTimeout(() => {
              setIsChange(false);
            }, 10);
          }, 100);
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeChat, actionInitChat]);

  useEffect(() => {
    if (
      viewport.current?.scrollHeight! - viewport.current?.offsetHeight! - 100 >
      scrollPosition.y
    ) {
      if (!scrollBottomButton) {
        setScrollBottomButton(true);
      }
    } else {
      if (scrollBottomButton) {
        setScrollBottomButton(false);
      }
    }
  }, [scrollPosition.y, activeChat, scrollBottomButton]);

  useEffect(() => {
    focusInput();
    setFiles([]);
    setImages([]);
    setPage(0);
    fetchMoreMessages();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeChat]);

  return (
    <Box
      sx={{
        height: "calc(100vh - 210px)",
        flexGrow: 1,
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-end",
        padding: matches ? 16 : 0,
      }}
      ref={ref}
    >
      <Box
        sx={{
          display: "flex",
          flexDirection: "column-reverse",
        }}
      >
        <CustomInfiniteScroll
          dataLength={ownMessages.length}
          hasMore={hasMore}
          height="calc(100vh - 245px)"
          inverse={true}
          next={fetchMoreMessages}
        >
          <Stack
            justify="flex-end"
            py="sm"
            pt={60}
            sx={{
              maxWidth: "calc(100vw - 40px)",
              opacity: 1,
              transition: "opacity .1s ease",
            }}
          >
            {orderBy(messages, "id")
              .filter(
                (e) =>
                  !isNull(e) &&
                  e.chatId === activeChat &&
                  e.content.type !== "price" &&
                  e.content.type !== "subtask"
              )
              .map((chatMessage: MessageType, i: number) => {
                const isMine = chatMessage.ownerId === activeUserId;
                const Message =
                  MessageTypes[chatMessage.type as keyof typeof MessageTypes];

                const owner = users.find(
                  (e) => e.id === chatMessage.ownerId
                ) as UserType;
                const defaultRoleWeight = roles.find(
                  (e) => activeUser?.defaultRoleId[0] === e.id
                )?.weight;

                if (
                  !owner ||
                  !Object.keys(MessageTypes).includes(chatMessage.type)
                ) {
                  return null;
                }

                return (
                  <Group
                    key={"message-" + chatMessage.id}
                    position={isMine ? "right" : "left"}
                  >
                    <Paper
                      p="sm"
                      sx={(theme) => ({
                        position: "relative",
                        maxWidth: isMobile ? "90%" : "80%",
                        minWidth: 180,
                        background:
                          theme.colorScheme === "dark"
                            ? theme.colors.dark[8]
                            : theme.colors.gray[2],
                      })}
                    >
                      <Stack spacing={0}>
                        <Group position="apart">
                          <Text size="sm" weight={600}>
                            {owner.name} {owner.surname}
                          </Text>
                          {isNumber(defaultRoleWeight) &&
                            (isMine || defaultRoleWeight < 0) && (
                              <Menu
                                shadow="md"
                                width={200}
                                offset={0}
                                position={"top-end"}
                                transition="fade"
                                withArrow
                              >
                                <Menu.Target>
                                  <ActionIcon size="sm">
                                    <EllipsisHorizontalIcon
                                      style={{ width: 16, height: 16 }}
                                    />
                                  </ActionIcon>
                                </Menu.Target>
                                <Menu.Dropdown>
                                  <Menu.Item
                                    onClick={() => handleDelete(chatMessage)}
                                  >
                                    {t("chat.delete")}
                                  </Menu.Item>
                                </Menu.Dropdown>
                              </Menu>
                            )}
                        </Group>
                        <Group position="apart" align="flex-end" spacing={8}>
                          <Message
                            content={chatMessage.content as any}
                            isMobile={isMobile}
                          />
                        </Group>
                        <Group position="right">
                          <Text size="xs" color="dimmed">
                            {formatTime(chatMessage.createdAt)}{" "}
                            {formatDate(chatMessage.createdAt)}
                          </Text>
                        </Group>
                      </Stack>
                    </Paper>
                  </Group>
                );
              })}
          </Stack>
        </CustomInfiniteScroll>
      </Box>

      {/* Send Message Area */}

      {scrollBottomButton && !isChange && (
        <Group
          position="right"
          sx={(theme) => ({
            position: "relative",
            width: "100%",
          })}
        >
          <Button
            compact
            variant="filled"
            color="blue"
            onClick={() => {
              scrollToBottomSmooth();
            }}
            sx={{
              position: "absolute",
              right: 7,
              bottom: 0,
              borderBottomLeftRadius: 0,
              borderBottomRightRadius: 0,
            }}
          >
            <ChevronDownIcon style={{ width: 12, height: 12 }} />
            <Text ml="xs" size="xs">
              {t("chat.scrollBottom")}
            </Text>
          </Button>
        </Group>
      )}

      {(images.length > 0 || files.length > 0) && (
        <Paper
          sx={(theme) => ({
            padding: 12,
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[8]
                : theme.colors.gray[2],
            boxShadow: `0 -1px 3px rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0px -20px 25px -5px, rgba(0, 0, 0, 0.04) 0px -10px 10px -5px`,
            zIndex: 10,
          })}
        >
          <ScrollArea
            offsetScrollbars
            scrollbarSize={5}
            styles={{
              viewport: {
                maxHeight: 170,
              },
            }}
          >
            {images.length > 0 && (
              <Text size="xs" weight={600} color="dimmed" mb={8}>
                {t("images")}
              </Text>
            )}
            <SimpleGrid
              cols={
                width > 1200
                  ? 5
                  : width > 1000
                  ? 4
                  : width > 800
                  ? 3
                  : width > 600
                  ? 2
                  : 1
              }
              sx={{ position: "relative" }}
            >
              {images.map((image: File, i: number) => {
                const blob = new Blob([image], { type: "image/jpeg" });
                const blobURL = URL.createObjectURL(blob);

                return (
                  <Paper key={`image-${i}`} p={8}>
                    <Group noWrap spacing={8}>
                      <Image
                        height={36}
                        width={36}
                        src={blobURL}
                        radius={8}
                        alt="preview"
                        withPlaceholder
                      />
                      <Text
                        size="xs"
                        sx={{
                          width: "100%",
                          whiteSpace: "nowrap",
                          textOverflow: "ellipsis",
                          overflow: "hidden",
                        }}
                      >
                        {image.name}
                      </Text>
                      <CloseButton
                        size="sm"
                        onClick={() =>
                          setImages(images.filter((e, index) => index !== i))
                        }
                      />
                    </Group>
                  </Paper>
                );
              })}
            </SimpleGrid>
            <Space h="xs" />
            {files.length > 0 && (
              <Text size="xs" weight={600} color="dimmed" mb={8}>
                {t("files")}
              </Text>
            )}
            <SimpleGrid
              cols={
                width > 1200
                  ? 5
                  : width > 1000
                  ? 4
                  : width > 800
                  ? 3
                  : width > 600
                  ? 2
                  : 1
              }
              sx={{ position: "relative" }}
            >
              {files.map((image: File, i: number) => {
                return (
                  <Paper key={`image-${i}`} p={8}>
                    <Group noWrap spacing={8}>
                      <ThemeIcon variant="light" size={36}>
                        <DocumentIcon style={{ width: 20, height: 20 }} />
                      </ThemeIcon>
                      <Text
                        size="xs"
                        sx={{
                          width: "100%",
                          whiteSpace: "nowrap",
                          textOverflow: "ellipsis",
                          overflow: "hidden",
                        }}
                      >
                        {image.name}
                      </Text>
                      <CloseButton
                        size="sm"
                        onClick={() =>
                          setFiles(files.filter((e, index) => index !== i))
                        }
                      />
                    </Group>
                  </Paper>
                );
              })}
            </SimpleGrid>
          </ScrollArea>
          {isNumber(uploadProgress) && isSending && (
            <Center>
              <Group spacing="sm">
                <Progress
                  striped
                  animate
                  size="sm"
                  sx={{ minWidth: 120 }}
                  value={uploadProgress * 100}
                />
                <Text size="xs" weight={600}>
                  {Math.min(Math.ceil(uploadProgress * 100), 100)}%
                </Text>
              </Group>
            </Center>
          )}
        </Paper>
      )}

      <Paper
        sx={(theme) => ({
          display: "flex",
          alignItems: "flex-start",
          background:
            theme.colorScheme === "dark"
              ? theme.colors.dark[6]
              : theme.colors.gray[2],
        })}
      >
        <Textarea
          ref={inputRef}
          placeholder={t("chat.inputPlaceholder")}
          autosize
          minRows={1}
          maxRows={6}
          defaultValue={message}
          onChange={(event) => setMessage(event.target.value)}
          onKeyDown={getHotkeyHandler([["Enter", handleSendMessage]])}
          sx={{
            width: "100%",
            height: "100%",
            border: "none",
            textarea: {
              background: "transparent",
              border: "none",
            },
            boxShadow: `0 -1px 3px rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0px -20px 25px -5px, rgba(0, 0, 0, 0.04) 0px -10px 10px -5px`,
          }}
        />
        <Menu
          shadow="md"
          width={200}
          offset={0}
          position="top-end"
          transition="fade"
          withArrow
        >
          <Menu.Target>
            <ActionIcon size="xl">
              <PaperClipIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
          </Menu.Target>

          <Menu.Dropdown>
            {images.length <= 20 && (
              <FileButton
                multiple
                onChange={(images) => handleSelect("images", images)}
                accept="image/png,image/jpeg,image/webp"
              >
                {(props) => (
                  <MessageButton {...props}>
                    <Group align="baseline" spacing={8}>
                      <Text>{t("chat.image")}</Text>
                      <Text size={10} weight={600} color="dimmed">
                        Max 20
                      </Text>
                    </Group>
                  </MessageButton>
                )}
              </FileButton>
            )}
            {files.length <= 20 && (
              <FileButton
                multiple
                onChange={(files) => handleSelect("files", files)}
                accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.zip,.rar"
              >
                {(props) => (
                  <MessageButton {...props}>
                    <Group align="baseline" spacing={8}>
                      <Text>{t("chat.file")}</Text>
                      <Text size={10} weight={600} color="dimmed">
                        Max 20
                      </Text>
                    </Group>
                  </MessageButton>
                )}
              </FileButton>
            )}
          </Menu.Dropdown>
        </Menu>
        <ActionIcon
          size="xl"
          onClick={() => handleSendMessage()}
          loading={isSending}
          loaderProps={{
            size: "xs",
          }}
        >
          <PaperAirplaneIcon style={{ width: 20, height: 20 }} />
        </ActionIcon>
      </Paper>
    </Box>
  );
};

export default ChatMessages;
