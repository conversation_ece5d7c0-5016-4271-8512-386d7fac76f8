import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  EllipsisHorizontalIcon,
  EnvelopeIcon,
  MagnifyingGlassIcon,
  PencilSquareIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Box,
  Group,
  Input,
  Paper,
  ScrollArea,
  SegmentedControl,
  Stack,
  Text,
  MediaQuery,
  useMantineTheme,
  Menu,
  Collapse,
} from "@mantine/core";
import MenuUser from "./MenuUser";
import { useCallback, useEffect, useState } from "react";
import { useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { orderBy, uniqBy } from "lodash";

type SegmentedControlItemType = {
  value: string;
  label: any;
  disabled?: boolean;
};

interface PropType {
  isOpenLeftMenu: boolean;
  closeLeftMenu: () => void;
  openCloseButton: boolean;
}

export default function LeftMenu(props: PropType) {
  const { isOpenLeftMenu, closeLeftMenu, openCloseButton } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();

  const matches = useMediaQuery("(max-width: 992px)");

  const { subscribedChats, activeChat, setActiveChat } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
      activeChat: state.activeChat!,
      setActiveChat: state.setActiveChat!,
    }),
    shallow
  );

  const [segmentedControlItems, setSegmentedControlItems] = useState<
    SegmentedControlItemType[]
  >([
    {
      value: "organizations",
      label: <Text size="xs">Organizations</Text>,
      disabled: true,
    },
    ...orderBy(subscribedChats, "id")
      .filter((e) => e.type === "person")
      .map((item) => ({
        value: `${item.id}`,
        label: <MenuUser chat={item} />,
      })),
    {
      value: "users",
      label: "Users",
      disabled: true,
    },
    ...orderBy(subscribedChats, "id")
      .filter((e) => e.type === "organization")
      .map((item) => ({
        value: `${item.id}`,
        label: <MenuUser chat={item} />,
      })),
  ]);

  const [isOpenSearchBar, toggleSearchbar] = useState(false);
  const chatsLength = subscribedChats.filter((e) =>
    ["person", "organization"].includes(e.type)
  ).length;

  const handleChange = useCallback(
    (value: string) => {
      push(
        {
          pathname: `/apps/chat/${value}`,
        },
        undefined,
        { shallow: true }
      );
      setActiveChat(+value);
      closeLeftMenu();
    },
    [closeLeftMenu, push, setActiveChat]
  );

  useEffect(() => {
    const organizations = subscribedChats.filter(
      (e) => e.type === "organization"
    );
    const persons = subscribedChats.filter((e) => e.type === "person");
    setSegmentedControlItems([
      ...(organizations.length > 0
        ? [
            {
              value: "organizations",
              label: (
                <Text size="xs" weight={600} align="left">
                  {t("organizations")}
                </Text>
              ),
              disabled: true,
            },
          ]
        : []),
      ...organizations.map((item) => ({
        value: `${item.id}`,
        label: <MenuUser chat={item} />,
      })),
      ...(persons.length > 0
        ? [
            {
              value: "users",
              label: (
                <Text size="xs" weight={600} align="left">
                  {t("users")}
                </Text>
              ),
              disabled: true,
            },
          ]
        : []),
      ...persons.map((item) => ({
        value: `${item.id}`,
        label: <MenuUser chat={item} />,
      })),
    ]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscribedChats]);

  return (
    <Paper
      sx={(theme) => ({
        display: "flex",
        alignItems: "stretch",
        flexShrink: 0,
        width: "100%",
        height: matches ? "calc(100vh - 70px)" : "calc(100vh - 140px)",
        overflow: "hidden",
        transition: "width .2s ease",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
      })}
    >
      <Stack align="stretch" sx={{ width: "100%" }} spacing={0}>
        <Group
          position="apart"
          px="md"
          pt="md"
          pb="md"
          sx={{ flexWrap: "nowrap" }}
        >
          <Group spacing={8} sx={{ flexWrap: "nowrap" }}>
            {openCloseButton && (
              <ActionIcon onClick={() => closeLeftMenu()}>
                <XMarkIcon
                  style={{
                    width: 20,
                    height: 20,
                    color:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[2]
                        : theme.colors.gray[8],
                  }}
                />
              </ActionIcon>
            )}
            <Text size="md" weight={600}>
              {t("chat.messages")}
            </Text>
            {chatsLength > 0 && (
              <Text size="md" weight={600} color="blue">
                ({chatsLength})
              </Text>
            )}
          </Group>

          <ActionIcon onClick={() => toggleSearchbar(!isOpenSearchBar)}>
            <MagnifyingGlassIcon style={{ width: 18, height: 18 }} />
          </ActionIcon>
        </Group>

        <Box
          px="md"
          sx={{
            width: "100%",
            height: isOpenSearchBar ? 30 : 0,
            transform: isOpenSearchBar ? "scale(1)" : "scale(.75)",
            overflow: "hidden",
            transition: "all .2s ease",
          }}
        >
          <Input
            icon={<MagnifyingGlassIcon style={{ width: 18, height: 18 }} />}
            placeholder="Search"
            size="xs"
          />
        </Box>

        <ScrollArea scrollbarSize={5} pt={isOpenSearchBar ? "sm" : 0}>
          <Box
            px="md"
            sx={{
              maxHeight: "100%",
            }}
          >
            <Stack spacing={0} pb="md">
              {/* <Group spacing={4} px={8}>
                <EnvelopeIcon style={{ width: 16, height: 16 }} />
                <Text size="xs" weight={600} color="dimmed">
                  {t("chat.messages")}
                </Text>
              </Group> */}
              <SegmentedControl
                fullWidth
                orientation="vertical"
                value={`${activeChat}`}
                onChange={(value: string) => handleChange(value)}
                data={segmentedControlItems}
                styles={{
                  root: {
                    background: "transparent",
                  },
                }}
              />
            </Stack>
          </Box>
        </ScrollArea>
      </Stack>
    </Paper>
  );
}
