import {
  BellSlashIcon,
  FireIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import {
  Box,
  Space,
  Group,
  Avatar,
  Stack,
  Text,
  Indicator,
  Badge,
  ThemeIcon,
  useMantineTheme,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { findLast, isNull } from "lodash";
import { useTranslation } from "next-i18next";
import { useEffect, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { useStore } from "~utils/store";
import { formatTime } from "~utils/tools";
import { ChatType } from "~utils/types/Chat";
import { UserType } from "~utils/types/User";

interface PropType {
  chat: ChatType;
}

export default function MenuUser(props: PropType) {
  const { chat } = props;
  const theme = useMantineTheme();
  const matches1 = useMediaQuery("(max-width: 500px)");
  const matches2 = useMediaQuery("(max-width: 200px)");
  const { t } = useTranslation();

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { userStatusColors } = useStore(
    "data",
    (state) => ({
      userStatusColors: state.userStatusColors!,
    }),
    shallow
  );
  const { messages } = useStore(
    "chat",
    (state) => ({
      messages: state.messages!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { userStatus } = useStore(
    "sockets",
    (state) => ({
      userStatus: state.userStatus!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const activePerson = users.find(
    (e) =>
      e.id ===
      (chat?.personId === activeUserId ? chat?.ownerId : chat?.personId)
  );
  const activeOrganization = customers.find(
    (e) => e.id === chat.organizationId
  );

  const lastMessage = findLast(
    messages,
    (e) => !isNull(e) && e.chatId === chat.id
  )!;
  let d = "";

  if (!!lastMessage) {
    switch (lastMessage.type) {
      case "text":
        d = lastMessage.content.text;
        break;
      case "task":
        d = t("chat.task");
        break;
      case "image":
        d = t("chat.image");
        break;
      case "file":
        d = t("chat.file");
      default:
        break;
    }
  }

  return (
    <Box
      py={4}
      sx={{
        display: "flex",
        alignItems: "center",
      }}
    >
      {chat.type === "person" && !!chat.personId && activePerson ? (
        // <Indicator
        //   dot
        //   inline
        //   size={12}
        //   offset={5}
        //   position="bottom-end"
        //   withBorder
        //   color={userStatusColors![userStatus[activePerson?.id]]}
        // >
        <ActiveAvatar userId={activePerson?.id} size="md" radius="xl" />
      ) : (
        // </Indicator>
        <ThemeIcon size={38} variant="light" color="blue" radius="xl">
          <UserGroupIcon style={{ width: 20, height: 20 }} />
        </ThemeIcon>
      )}
      <Space w="sm" />
      <Stack spacing={0} sx={{ width: "100%" }}>
        <Group position="apart" noWrap>
          <Text
            size="sm"
            weight={600}
            align="left"
            sx={{
              maxWidth: matches1 && matches2 ? "auto" : 110,
              lineHeight: 1,
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
            }}
          >
            {chat.type === "person" && !!activePerson
              ? `${activePerson.name} ${activePerson.surname}`
              : chat.type === "organization" && !!activeOrganization
              ? activeOrganization.fullName
              : ""}
          </Text>
          <Group spacing={8} align="center" sx={{ flexWrap: "nowrap" }}>
            {/* {!chat.options.notification && (
              <BellSlashIcon style={{ width: 14, height: 14 }} />
            )}
            {chat.options.pinned && (
              <FireIcon style={{ width: 14, height: 14 }} />
            )} */}
            <Text size="xs" color="dimmed">
              {!!lastMessage ? formatTime(lastMessage.createdAt) : ""}
            </Text>
          </Group>
        </Group>
        <Group position="apart">
          <Group
            spacing={2}
            sx={{
              flexWrap: "nowrap",
              maxWidth: matches1 ? "calc(100vw - 120px)" : 160,
            }}
          >
            {!!lastMessage && lastMessage.ownerId === activeUserId && (
              <Text
                size="xs"
                weight={600}
                color={
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[3]
                    : theme.colors.gray[5]
                }
              >
                {t("chat.you")}:
              </Text>
            )}
            <Text
              size="xs"
              color="dimmed"
              align="left"
              sx={{
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                overflow: "hidden",
                pointerEvents: "none",
              }}
            >
              {d}
            </Text>
          </Group>
          {/* <Badge color="red" variant="filled" p={4} size="sm">
            {messages.filter((e) => e.chatId === chat.id).length}
          </Badge> */}
        </Group>
      </Stack>
    </Box>
  );
}
