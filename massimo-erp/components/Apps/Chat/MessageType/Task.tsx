import React from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import {
  Group,
  Box,
  Title,
  Paper,
  Tooltip,
  Text,
  Space,
  Progress,
  Badge,
} from "@mantine/core";
import { calculateProgress, formatDate } from "~utils/tools";
import { useTranslation } from "next-i18next";
import { TaskType } from "~utils/types/Task";

interface PropType {
  content: {
    taskId: number;
  };
}

export default function TaskMessage(props: PropType) {
  const { content } = props;
  const { tasks, priorityColors, statusColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      priorityColors: state.priorityColors!,
      statusColors: state.statusColors!,
    }),
    shallow
  );
  const { setTaskViewModalData, setIsOpenTaskViewModal, blankTask } = useStore(
    "temp",
    (state) => ({
      setTaskViewModalData: state.setTaskViewModalData!,
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      blankTask: state.computed!.blankTask!,
    })
  );

  const { t } = useTranslation();
  const active = tasks.find((e) => e.id === content.taskId) as
    | TaskType
    | undefined;

  return (
    <Paper
      sx={(theme) => ({
        width: 370,
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[7]
            : theme.colors.gray[3],
        padding: theme.spacing.md,
        marginTop: 8,
      })}
    >
      <Group align="center">
        <Tooltip
          label={
            `${t("newTask.priority.label")}: ` +
            t(`priority.${active?.priority}`)
          }
        >
          <Box
            sx={(theme) => ({
              width: 10,
              height: 10,
              borderRadius: "50%",
              background:
                theme.colors[priorityColors[active?.priority || "Medium"]][9],
            })}
          />
        </Tooltip>
        <Badge color={statusColors[active?.status || "To Do"]}>
          {t(`status.${active?.status}`)}
        </Badge>
        <Title
          order={5}
          sx={(theme) => ({
            cursor: "pointer",
            transition: "color .2s ease",
            ":hover": { color: theme.colors.gray[6] },
          })}
          onClick={() => {
            setTaskViewModalData(active || blankTask);
            setIsOpenTaskViewModal(true);
          }}
        >
          {active?.title}
        </Title>
        <Text color="dimmed" size="xs" weight={600} sx={{ lineHeight: 1 }}>
          {formatDate(active?.end || new Date())}
        </Text>
      </Group>
    </Paper>
  );
}
