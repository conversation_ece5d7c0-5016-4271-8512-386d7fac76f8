import { Box } from "@mantine/core";
import Image from "next/image";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

interface PropType {
  content: {
    fileId: number;
  };
  isMobile?: boolean;
}

export default function ImageMessage(props: PropType) {
  const { content, isMobile } = props;

  const { setImageModal } = useStore(
    "temp",
    (state) => ({
      setImageModal: state.setImageModal!,
    }),
    shallow
  );

  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const file = files.find((e) => e[0] === content.fileId) || [];

  return (
    <Box
      sx={{
        position: "relative",
        width: isMobile ? "100%" : 250,
        minHeight: 150,
        cursor: "pointer",
        transition: "filter .2s ease",
        ":hover": {
          filter: "brightness(.8)",
        },
        img: {
          width: isMobile ? "100%" : 250,
          height: "100%",
          objectFit: "cover",
          borderRadius: 8,
        },
      }}
      my={8}
      onClick={() => setImageModal(file[2])}
    >
      {file[2] && (
        <Image fill src={file[2]} alt="img" draggable={false} loading="lazy" />
      )}
    </Box>
  );
}
