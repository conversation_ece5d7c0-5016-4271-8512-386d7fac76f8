import { ArrowDownTrayIcon, DocumentIcon } from "@heroicons/react/24/outline";
import {
  Group,
  ThemeIcon,
  Stack,
  Text,
  ActionIcon,
  useMantineTheme,
  Anchor,
  Tooltip,
} from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatBytes } from "~utils/tools";

interface PropType {
  content: {
    fileId: number;
  };
  isMobile: boolean;
}

export default function FileMessage(props: PropType) {
  const { content, isMobile } = props;
  const theme = useMantineTheme();

  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const file = files.find((e) => e[0] === content.fileId);
  let fileName = "";
  {
    const rawFileName = (file || [])[2];
    if (rawFileName) {
      const splittedFileName = rawFileName.split("/files/");
      fileName = splittedFileName[splittedFileName.length - 1]
        .split("-")
        .slice(1)
        .join("-");
    }
  }

  return (
    <>
      <Group align="center" position="apart" noWrap mt={8}>
        <Tooltip label={fileName}>
          <Group noWrap>
            <ThemeIcon variant="light" color="blue" size="xl">
              <DocumentIcon style={{ width: 20, height: 20 }} />
            </ThemeIcon>
            <Stack spacing={0}>
              <Text
                size="sm"
                weight={600}
                sx={{
                  maxWidth: isMobile ? 100 : 200,
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  lineClamp: 2,
                }}
              >
                {fileName}
              </Text>
            </Stack>
          </Group>
        </Tooltip>
        <Anchor href={(file || [])[2]} download>
          <ActionIcon>
            <ArrowDownTrayIcon
              style={{
                width: 20,
                height: 20,
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[3]
                    : theme.colors.gray[6],
              }}
            />
          </ActionIcon>
        </Anchor>
      </Group>
    </>
  );
}
