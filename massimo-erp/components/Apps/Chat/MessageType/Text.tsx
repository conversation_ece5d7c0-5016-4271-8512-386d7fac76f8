import { Text, Anchor } from "@mantine/core";
import { Fragment, useEffect, useState } from "react";

interface PropType {
  content: {
    text: string;
  };
}

const RegexURL =
  /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)/;

export default function TextMessage(props: PropType) {
  const { content } = props;
  const len = content.text.split(" ").length;

  return (
    <Text
      size="sm"
      sx={(theme) => ({
        overflowWrap: "anywhere",
      })}
    >
      {/* {content.split("\n").map((t, i) => {
        return (
          <Fragment key={t + i}>
            {t} <br />
          </Fragment>
        );
      })} */}
      {content.text.split(" ").map((t, it) => {
        if (RegexURL.test(t)) {
          return (
            <Anchor
              target="_blank"
              key={"chatWord-" + it}
              href={t}
              mr={it !== len - 1 ? 4 : 0}
            >
              {t}
            </Anchor>
          );
        } else {
          return t.split("\n").map((z, iz) => (
            <Fragment key={z + iz}>
              {z} {iz > 0 && <br />}
            </Fragment>
          ));
        }
      })}
    </Text>
  );
}
