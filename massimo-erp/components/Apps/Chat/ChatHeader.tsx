import {
  Bars3Icon,
  Cog6ToothIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Avatar,
  Group,
  Paper,
  Title,
  Text,
  Input,
  useMantineTheme,
  MediaQuery,
  Indicator,
  ScrollArea,
} from "@mantine/core";
import { findLast } from "lodash";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { UserType } from "~utils/types/User";
import { ChatType } from "~utils/types/Chat";
import ActiveAvatar from "~components/ActiveAvatar";
import { useRouter } from "next/router";

interface PropType {
  toggleSettings: () => void;
  setLeftMenu: (value: boolean) => void;
  menuButtonOpen: boolean;
  isMobile: boolean;
}

export default function ChatHeader(props: PropType) {
  const { toggleSettings, setLeftMenu, menuButtonOpen, isMobile } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { userStatusColors } = useStore(
    "data",
    (state) => ({
      userStatusColors: state.userStatusColors!,
    }),
    shallow
  );
  const { subscribedChats, activeChat } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
      activeChat: state.activeChat!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { userStatus } = useStore(
    "sockets",
    (state) => ({
      userStatus: state.userStatus!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const chat = subscribedChats.find((e: ChatType) => e.id === activeChat) as
    | ChatType
    | undefined;

  const activePerson = users.find(
    (e) =>
      e.id ===
      (chat?.personId === activeUserId ? chat?.ownerId : chat?.personId)
  );

  const activeOrganization = customers.find(
    (e) => e.id === chat?.organizationId
  );

  const [searchBar, setSearchBar] = useState(false);

  return (
    <Paper
      sx={(theme) => ({
        position: "relative",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
        height: 62,
        boxShadow: theme.shadows.md,
        zIndex: 10,
      })}
    >
      <Group noWrap sx={{ width: "100%", position: "relative" }}>
        {menuButtonOpen && (
          <ActionIcon onClick={() => setLeftMenu(true)}>
            <Bars3Icon
              style={{
                width: 20,
                height: 20,
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[2]
                    : theme.colors.gray[8],
              }}
            />
          </ActionIcon>
        )}

        {chat?.type === "organization" && !!activeOrganization ? (
          <Avatar size={32} radius={100} color="blue">
            <UserGroupIcon style={{ width: 16, height: 16 }} />
          </Avatar>
        ) : chat?.type === "person" && !!activePerson && !!chat?.personId ? (
          // <Indicator
          //   dot
          //   inline
          //   size={12}
          //   offset={3}
          //   position="bottom-end"
          //   withBorder
          //   color={userStatusColors[userStatus[activePerson.id]]}
          // >
          <ActiveAvatar userId={activePerson.id} size={32} radius="xl" />
        ) : (
          // </Indicator>
          <></>
        )}

        <Group
          spacing="sm"
          noWrap
          sx={{
            position: "relative",
            width: "100%",
          }}
        >
          <Text
            size="sm"
            weight={600}
            sx={() => ({
              maxWidth: "calc(100% - 10px)",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
              cursor: "pointer",
              ":hover": {
                textDecoration: "underline",
              },
            })}
            onClick={() =>
              push(
                chat?.type === "organization" && !!chat?.organizationId
                  ? `/profile/organization/${chat?.organizationId}`
                  : chat?.type === "person" && !!activePerson?.id
                  ? `/profile/user/${activePerson?.id}`
                  : ""
              )
            }
          >
            {chat?.type === "organization" && !!activeOrganization
              ? activeOrganization.fullName
              : chat?.type === "person" && !!activePerson
              ? `${activePerson.name} ${activePerson.surname}`
              : ""}
          </Text>
        </Group>

        {activeChat !== 0 && (
          <>
            <Group spacing={0} noWrap>
              <ActionIcon onClick={() => setSearchBar(!searchBar)}>
                <MagnifyingGlassIcon
                  style={{
                    width: 20,
                    height: 20,
                    color:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[2]
                        : theme.colors.gray[8],
                  }}
                />
              </ActionIcon>
              <Input
                placeholder={t("chat.search")}
                size="xs"
                ml={searchBar ? 4 : 0}
                sx={{
                  width: searchBar ? 150 : 0,
                  opacity: searchBar ? 1 : 0,
                  transition: "all .2s ease",
                }}
              />
            </Group>
            <ActionIcon onClick={() => toggleSettings()}>
              <Cog6ToothIcon
                style={{
                  width: 20,
                  height: 20,
                  color:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[2]
                      : theme.colors.gray[8],
                }}
              />
            </ActionIcon>
          </>
        )}
      </Group>
    </Paper>
  );
}
