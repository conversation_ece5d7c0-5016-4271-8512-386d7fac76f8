import {
  ArrowDownTrayIcon,
  DocumentIcon,
  EllipsisHorizontalIcon,
  PaperAirplaneIcon,
  PhotoIcon,
  UserGroupIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Avatar,
  Group,
  Indicator,
  Paper,
  Stack,
  Title,
  Switch,
  Text,
  useMantineTheme,
  Menu,
  ThemeIcon,
  ScrollArea,
  Image,
  Anchor,
  Tooltip,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { findLast, isNull } from "lodash";
import { useTranslation } from "next-i18next";
import Link from "next/link";
import { useRouter } from "next/router";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { useStore } from "~utils/store";
import { formatBytes } from "~utils/tools";
import { ChatType, MessageType } from "~utils/types/Chat";
import { UserType } from "~utils/types/User";
import ChatSettingsUser from "./ChatSettingsUser";

interface PropType {
  isOpenSettings: boolean;
  closeSettings: () => void;
  fullWidth: boolean;
}

export default function ChatSettings(props: PropType) {
  const { isOpenSettings, closeSettings, fullWidth } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();

  const { subscribedChats, activeChat, messages } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
      activeChat: state.activeChat!,
      messages: state.messages!,
    }),
    shallow
  );
  const { setImageModal, blankUser } = useStore(
    "temp",
    (state) => ({
      setImageModal: state.setImageModal!,
      blankUser: state.computed!.blankUser!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  // const active: ChatType = subscribedChats.filter(
  //   (e: ChatType) => e.id === activeChat
  // )[0];

  // const [activeUser, setActiveUser] = useState<UserType>(blankUser);

  // useEffect(() => {
  //   const c = subscribedChats.find((e) => e.id === activeChat)!;

  //   let a: MessageType[] = [];
  //   messages
  //     .filter((e) => e.chatId === activeChat)
  //     .map((ch) => {
  //       if (ch.type === "file" || ch.type === "image") {
  //         a.push(ch);
  //       }
  //     });
  //   setAttachments(a);

  //   if (c.target.length <= 1) {
  //     setActiveUser(users.find((e) => e.id === c.target[0])!);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [activeChat]);

  const chat: ChatType = subscribedChats.filter(
    (e: ChatType) => e.id === activeChat
  )[0];
  const attachments = messages.filter(
    (e) =>
      !isNull(e) &&
      e.chatId === activeChat &&
      (e.type === "file" || e.type === "image")
  );

  const members = customers.find(
    (e) => chat && e.id === chat?.organizationId
  )?.personIds;

  const activePerson = users.find(
    (e) =>
      e.id ===
      (chat?.personId === activeUserId ? chat?.ownerId : chat?.personId)
  );
  const activeOrganization = customers.find(
    (e) => e.id === chat?.organizationId
  );

  const activeLabel =
    chat?.type === "organization" && !!activeOrganization
      ? activeOrganization.fullName
      : chat?.type === "person" && !!activePerson
      ? `${activePerson.name} ${activePerson.surname}`
      : "";

  // if (!active || !activeUser) {
  //   return null;
  // }

  return (
    <Paper
      sx={(theme) => ({
        flexShrink: 0,
        width: fullWidth ? "100%" : 270,
        height: "100%",
        marginLeft: isOpenSettings && !fullWidth ? theme.spacing.md : 0,
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        transition: "all .2s ease",
        overflow: "hidden",
      })}
    >
      <Stack py="md">
        <Group position="right" px="md">
          <ActionIcon onClick={() => closeSettings()}>
            <XMarkIcon
              style={{
                width: 20,
                height: 20,
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[2]
                    : theme.colors.gray[8],
              }}
            />
          </ActionIcon>
        </Group>
        <Stack>
          <Stack
            align="center"
            px="md"
            sx={{
              width: "100%",
              position: "relative",
            }}
          >
            {chat?.type === "organization" && !!activeOrganization ? (
              <Avatar size="lg" radius={100} color="blue">
                <UserGroupIcon style={{ width: 20, height: 20 }} />
              </Avatar>
            ) : chat?.type === "person" &&
              !!activePerson &&
              !!chat?.personId ? (
              // <Indicator
              //   dot
              //   inline
              //   size={12}
              //   offset={3}
              //   position="bottom-end"
              //   withBorder
              //   color={userStatusColors[userStatus[activePerson.id]]}
              // >
              <ActiveAvatar userId={activePerson.id} size="lg" radius="xl" />
            ) : (
              // </Indicator>
              <></>
            )}
            {/* <Link
              href={
                chat?.type === "organization" && !!chat?.organizationId
                  ? `/profile/organization/${chat?.organizationId}`
                  : chat?.type === "person" && !!activePerson?.id
                  ? `/profile/user/${activePerson?.id}`
                  : ""
              }
            > */}
            <Tooltip label={activeLabel}>
              <Text
                size="lg"
                weight={600}
                sx={(theme) => ({
                  maxWidth: "calc(100% - 32px)",
                  cursor: "pointer",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  ":hover": {
                    textDecoration: "underline",
                  },
                })}
                onClick={() =>
                  push(
                    chat?.type === "organization" && !!chat?.organizationId
                      ? `/profile/organization/${chat?.organizationId}`
                      : chat?.type === "person" && !!activePerson?.id
                      ? `/profile/user/${activePerson?.id}`
                      : ""
                  )
                }
              >
                {activeLabel}
              </Text>
            </Tooltip>
            {/* </Link> */}
          </Stack>

          <ScrollArea
            scrollbarSize={5}
            styles={{
              viewport: {
                position: "relative",
                "& > div": {
                  display: "unset !important",
                  overflow: "visible",
                },
                overflow: "visible",
              },
              root: {
                position: "relative",
                overflow: "visible",
              },
            }}
          >
            <Stack
              sx={{
                maxHeight: "calc(100vh - 344px)",
              }}
              px="md"
            >
              {chat?.type === "organization" && (
                <>
                  <Group position="apart" sx={{ flexWrap: "nowrap" }}>
                    <Group spacing={8} sx={{ flexWrap: "nowrap" }}>
                      <Text size="sm" weight={600}>
                        {t("chat.members")}
                      </Text>
                      <Text size="sm" weight={600} color="blue">
                        ({members?.length})
                      </Text>
                    </Group>

                    <Menu
                      shadow="md"
                      width={150}
                      position="bottom-end"
                      transition="fade"
                    >
                      <Menu.Target>
                        <ActionIcon>
                          <EllipsisHorizontalIcon
                            style={{ width: 20, height: 20 }}
                          />
                        </ActionIcon>
                      </Menu.Target>
                      <Menu.Dropdown>
                        <Menu.Item>{t("chat.addMember")}</Menu.Item>
                      </Menu.Dropdown>
                    </Menu>
                  </Group>

                  <Stack>
                    {(members || []).map((memberId: number, i: number) => (
                      <ChatSettingsUser
                        key={"groupMember-" + i}
                        memberId={memberId}
                      />
                    ))}
                  </Stack>
                </>
              )}

              {attachments.length > 0 && (
                <>
                  <Group position="apart" sx={{ flexWrap: "nowrap" }}>
                    <Group spacing={8} sx={{ flexWrap: "nowrap" }}>
                      <Text size="sm" weight={600}>
                        {t("chat.attachments")}
                      </Text>
                      <Text size="sm" weight={600} color="blue">
                        ({attachments.length})
                      </Text>
                    </Group>
                  </Group>
                  <Stack pb="md">
                    {(attachments || []).map(
                      (attachment: MessageType, i: number) => {
                        const file = files.find(
                          (e) => e[0] === attachment.content.fileId
                        );
                        let fileName = "";
                        {
                          const rawFileName = (file || [])[2];
                          if (rawFileName) {
                            const splittedFileName =
                              rawFileName.split("/files/");
                            fileName = splittedFileName[
                              splittedFileName.length - 1
                            ]
                              .split("-")
                              .slice(1)
                              .join("-");
                          }
                        }

                        return (
                          <Group
                            key={"attachment-" + i}
                            position="apart"
                            sx={{ flexWrap: "nowrap" }}
                          >
                            <Tooltip label={fileName}>
                              <Group sx={{ flexWrap: "nowrap" }}>
                                {attachment.type === "image" ? (
                                  <Image
                                    src={(file || [])[2]}
                                    alt={fileName}
                                    width={40}
                                    height={40}
                                    radius="md"
                                    sx={{
                                      cursor: "pointer",
                                    }}
                                    onClick={() =>
                                      setImageModal((file || [])[2])
                                    }
                                    withPlaceholder
                                  />
                                ) : (
                                  <ThemeIcon
                                    variant="light"
                                    color="blue"
                                    size="xl"
                                  >
                                    <DocumentIcon
                                      style={{ width: 20, height: 20 }}
                                    />
                                  </ThemeIcon>
                                )}
                                <Stack spacing={0}>
                                  <Text
                                    size="sm"
                                    weight={600}
                                    sx={{
                                      maxWidth: 130,
                                      whiteSpace: "nowrap",
                                      textOverflow: "ellipsis",
                                      overflow: "hidden",
                                    }}
                                  >
                                    {fileName}
                                  </Text>
                                </Stack>
                              </Group>
                            </Tooltip>

                            <Anchor href={(file || [])[2]} download>
                              <ActionIcon>
                                <ArrowDownTrayIcon
                                  style={{
                                    width: 20,
                                    height: 20,
                                    color:
                                      theme.colorScheme === "dark"
                                        ? theme.colors.dark[3]
                                        : theme.colors.gray[6],
                                  }}
                                />
                              </ActionIcon>
                            </Anchor>
                          </Group>
                        );
                      }
                    )}
                  </Stack>
                </>
              )}
            </Stack>
          </ScrollArea>
        </Stack>
      </Stack>
    </Paper>
  );
}
