import {
  EllipsisHorizontalIcon,
  PaperAirplaneIcon,
} from "@heroicons/react/24/outline";
import {
  Group,
  Avatar,
  Text,
  ActionIcon,
  Menu,
  useMantineTheme,
  Tooltip,
} from "@mantine/core";
import { findLast } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { useStore } from "~utils/store";
import { ChatType } from "~utils/types/Chat";
import { UserType } from "~utils/types/User";

interface PropType {
  memberId: number;
}

export default function ChatSettingsUser(props: PropType) {
  const { memberId } = props;
  const theme = useMantineTheme();
  const { t } = useTranslation();

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { subscribedChats } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
    }),
    shallow
  );

  const { push } = useRouter();
  const person = users.find((e) => e.id === memberId) as UserType | undefined;
  const chatId = subscribedChats.find(
    (e) => e.type === "person" && [e.personId, e.ownerId].includes(memberId)
  )?.id;

  return (
    <Group noWrap sx={{ position: "relative" }}>
      <Group spacing={12} noWrap sx={{ width: "100%", position: "relative" }}>
        <ActiveAvatar userId={person?.id} size="md" radius="xl" />
        <Tooltip label={`${person?.name} ${person?.surname}`}>
          <Text
            size="sm"
            weight={600}
            sx={{
              maxWidth: "100%",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
            }}
          >
            {person?.name} {person?.surname}
          </Text>
        </Tooltip>
      </Group>

      {activeUserId !== memberId && (
        <Group spacing={4} noWrap>
          {!!chatId && (
            <ActionIcon onClick={() => push(`/apps/chat/${chatId}`)}>
              <PaperAirplaneIcon
                style={{
                  width: 20,
                  height: 20,
                  color:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[3]
                      : theme.colors.gray[6],
                }}
              />
            </ActionIcon>
          )}
          {/* <Menu shadow="md" width={150} position="bottom-end" transition="fade">
            <Menu.Target>
              <ActionIcon>
                <EllipsisHorizontalIcon
                  style={{
                    width: 20,
                    height: 20,
                    color:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[3]
                        : theme.colors.gray[6],
                  }}
                />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <Menu.Item>{t("chat.kick")}</Menu.Item>
            </Menu.Dropdown>
          </Menu> */}
        </Group>
      )}
    </Group>
  );
}
