import { Button, Paper, Text, Group, CloseButton, Dialog } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

export default function Cookies() {
  const { isAcceptCookies, setCookies } = useStore(
    "global",
    (state) => ({
      isAcceptCookies: state.isAcceptCookies!,
      setCookies: state.setCookies!,
    }),
    shallow
  );

  const mathces = useMediaQuery("(max-width: 500px)");

  return (
    <Dialog
      opened={!isAcceptCookies}
      size={mathces ? "calc(100vw - 48px)" : "lg"}
      radius="md"
      styles={(theme) => ({
        root: {
          boxShadow: `0px 0px 25px 7px ${
            theme.colorScheme === "dark"
              ? theme.colors.dark[9]
              : theme.colors.gray[0]
          }`,
        },
      })}
    >
      <Text size="md" weight={500} mb="xs">
        Allow cookies
      </Text>
      <Text color="dimmed" size="xs">
        So the deal is, we want to spy on you. We would like to know what did
        you have for todays breakfast, where do you live, how much do you earn
        and like 50 other things. To view our landing page you will have to
        accept all cookies. That&apos;s all, and remember that we are
        watching...
      </Text>
      <Group position="right" mt="xs">
        <Button variant="outline" size="xs" onClick={() => setCookies(true)}>
          Accept all
        </Button>
      </Group>
    </Dialog>
  );
}
