import { Anchor, Group, Modal, Stack, Text } from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { useStore } from "~utils/store";

const ViewsModal = () => {
  const { storyViewsModal, setStoryViewsModal } = useStore(
    "temp",
    (state) => ({
      storyViewsModal: state.storyViewsModal!,
      setStoryViewsModal: state.setStoryViewsModal!,
    }),
    shallow
  );
  const { views, actionGetViews, resetLikes } = useStore(
    "posts",
    (state) => ({
      views: state.views!,
      actionGetViews: state.actionGetViews!,
      resetLikes: state.resetLikes!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { push } = useRouter();
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(0);

  const getViews = useCallback(async () => {
    try {
      const hasMoreData = await actionGetViews(storyViewsModal.storyId, page);
      setHasMore(hasMoreData);
    } finally {
      setPage((p) => p + 1);
    }
  }, [actionGetViews, page, storyViewsModal.storyId]);

  const closeModal = useCallback(() => {
    setStoryViewsModal({
      isOpen: false,
      storyId: 0,
    });
    setPage(0);
    setHasMore(false);
    resetLikes();
  }, [setStoryViewsModal, resetLikes]);

  useEffect(() => {
    if (storyViewsModal.isOpen) {
      setPage(0);
      getViews();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storyViewsModal.isOpen]);

  return (
    <Modal
      title={t("viewers")}
      opened={storyViewsModal.isOpen}
      onClose={closeModal}
      centered
      transition={
        storyViewsModal.isOpen !== undefined ? "slide-down" : "slide-up"
      }
      size={320}
      zIndex={100000000}
      overflow="outside"
    >
      <CustomInfiniteScroll
        dataLength={views.length}
        hasMore={hasMore}
        height={400}
        next={getViews}
      >
        <Stack sx={{ width: "100%", maxWidth: 550 }} mx="auto" spacing="sm">
          {views.map((userId: number, i: number) => {
            const user = users.find((e) => e.id === userId);

            return (
              <Group key={`user-${userId}`} spacing="sm">
                <ActiveAvatar userId={userId} size={32} radius="xl" />
                <Anchor
                  size="sm"
                  weight={600}
                  sx={(theme) => ({
                    color:
                      theme.colorScheme === "dark"
                        ? theme.colors.gray[4]
                        : theme.colors.dark[8],
                    textDecoration: "none !important",
                  })}
                  onClick={() => {
                    push(`/profile/user/${userId}`);
                    closeModal();
                  }}
                >
                  {user?.name} {user?.surname}
                </Anchor>
              </Group>
            );
          })}
        </Stack>
      </CustomInfiniteScroll>
    </Modal>
  );
};

export default ViewsModal;
