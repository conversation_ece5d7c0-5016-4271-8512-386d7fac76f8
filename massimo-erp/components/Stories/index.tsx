import { PlusSmallIcon } from "@heroicons/react/24/outline";
import {
  Avatar,
  Box,
  Group,
  Paper,
  ScrollArea,
  Stack,
  Text,
  Tooltip,
} from "@mantine/core";
import { isUndefined, uniq } from "lodash";
import Image from "next/image";
import { useCallback, useEffect } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { PostType } from "~utils/types/Post";

const Stories: CC<{
  userId?: number;
  customerId?: number;
  withName?: boolean;
  feed?: boolean;
  group?: boolean;
}> = ({ userId, customerId, withName, feed = false, group = false }) => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );
  const { stories, actionGetStories, actionGetFeed } = useStore(
    "posts",
    (state) => ({
      stories: state.stories!,
      actionGetStories: state.actionGetStories!,
      actionGetFeed: state.actionGetFeed!,
    }),
    shallow
  );
  const { setStoryViewModal, setPostModal } = useStore(
    "temp",
    (state) => ({
      setStoryViewModal: state.setStoryViewModal!,
      setPostModal: state.setPostModal!,
    }),
    shallow
  );

  const getStories = useCallback(async () => {
    if (feed) {
      await actionGetFeed(0, true);
    } else {
      await actionGetStories(userId as number);
    }
  }, [actionGetFeed, actionGetStories, feed, userId]);

  useEffect(() => {
    getStories();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, customerId]);

  useEffect(() => {
    getStories();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  let groupedStories = stories.reduce((prev: PostType[], curr: PostType) => {
    return [
      ...prev,
      ...(prev.some((e) => e.userId === curr.userId) ? [] : [curr]),
    ];
  }, []);

  return !(!feed && userId !== activeUserId && stories.length === 0) ? (
    <Box
      sx={() => ({
        display: "grid",
        position: "relative",
      })}
      mb={feed ? 0 : "sm"}
    >
      <ScrollArea scrollbarSize={4} offsetScrollbars>
        <Group noWrap mb={feed ? 0 : 8} align="flex-start">
          {(userId === activeUserId || feed) && (
            <Box
              sx={(theme) => ({
                width: 68,
                height: 68,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                border: `3px dashed ${
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[7]
                    : theme.colors.dark[1]
                }`,
                color:
                  theme.colorScheme === "dark"
                    ? theme.colors.gray[7]
                    : theme.colors.dark[1],
                borderRadius: "50%",
                cursor: "pointer",
                transition: "background-color .2s ease",
                ":hover": {
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.gray[9]
                      : theme.colors.gray[0],
                },
              })}
              onClick={() =>
                setPostModal({
                  isOpen: true,
                  type: "story",
                })
              }
            >
              <PlusSmallIcon width={32} height={32} strokeWidth={2} />
            </Box>
          )}

          {(group ? groupedStories : stories).map((story, i) => {
            const user = users.find((e) => e.id === story.userId);

            const file = files.find((e) => e[0] === story.fileIds[0]) || [];
            const isViewed = group
              ? stories
                  .filter((e) => e.userId === story.userId)
                  .every((e) => e.isViewed !== null)
              : story.isViewed !== null;

            return (
              <Stack key={`story-${i}`} spacing={4}>
                <Box
                  sx={(theme) => ({
                    width: 68,
                    height: 68,
                    borderRadius: "50%",
                    border: !isViewed
                      ? `2px solid ${theme.colors.green[9]}`
                      : "none",
                    padding: !isViewed ? 2 : 0,
                    cursor: "pointer",
                    userSelect: "none",

                    ":hover": {
                      img: {
                        filter: "brightness(.75)",
                      },
                    },

                    img: {
                      borderRadius: "50%",
                      transition: "filter .2s ease",
                      userSelect: "none",
                      pointerEvents: "none",
                    },
                  })}
                  onClick={() =>
                    setStoryViewModal({
                      isOpen: true,
                      storyId: story.id,
                      group,
                    })
                  }
                >
                  {file[2] && (
                    <Image
                      src={file[2]}
                      width={!isViewed ? 60 : 68}
                      height={!isViewed ? 60 : 68}
                      alt="Story"
                      loading="lazy"
                    />
                  )}
                </Box>

                {withName && (
                  <Stack spacing={4} align="center">
                    <Text
                      size="xs"
                      weight={600}
                      sx={{
                        width: 70,
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                        userSelect: "none",
                      }}
                      align="center"
                    >
                      {user?.name} {user?.surname}
                    </Text>
                  </Stack>
                )}
              </Stack>
            );
          })}
        </Group>
      </ScrollArea>
    </Box>
  ) : (<></>);
};

export default Stories;
