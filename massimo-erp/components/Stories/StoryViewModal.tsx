import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  AspectRatio,
  Box,
  Group,
  ActionIcon,
  CloseButton,
  Avatar,
  Stack,
  Text,
  useMantineTheme,
  Spoiler,
  Anchor,
  ThemeIcon,
  Menu,
  Progress,
} from "@mantine/core";
import { useHotkeys, useMediaQuery } from "@mantine/hooks";
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  EllipsisVerticalIcon,
  EyeIcon,
  HeartIcon,
  PaperAirplaneIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import HeartIconFilled from "@heroicons/react/24/solid/HeartIcon";
import { useCallback, useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import ActiveAvatar from "~components/ActiveAvatar";
import { UserType } from "~utils/types/User";
import { CustomerType } from "~utils/types/Customer";
import { useTranslation } from "next-i18next";
import { formatDate, formatTime } from "~utils/tools";
import Image from "next/image";

const StoryViewModal = function () {
  const {
    storyViewModal,
    setStoryViewModal,
    setViewLikesModal,
    setStoryViewsModal,
    setPostModal,
  } = useStore(
    "temp",
    (state) => ({
      storyViewModal: state.storyViewModal!,
      setStoryViewModal: state.setStoryViewModal!,
      setViewLikesModal: state.setViewLikesModal!,
      setStoryViewsModal: state.setStoryViewsModal!,
      setPostModal: state.setPostModal!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { stories, actionUpdatePostMeta, actionUpdatePosts } = useStore(
    "posts",
    (state) => ({
      stories: state.stories!,
      actionUpdatePostMeta: state.actionUpdatePostMeta!,
      actionUpdatePosts: state.actionUpdatePosts!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { chats } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
    }),
    shallow
  );
  const { files } = useStore(
    "files",
    (state) => ({
      files: state.files!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 560px)");
  const theme = useMantineTheme();
  const { push } = useRouter();
  const { t } = useTranslation();

  const activeStory = stories.find(
    (e) => e.type === "story" && e.id === storyViewModal.storyId
  );

  const owner = users.find((e: UserType) => e.id === activeStory?.userId);

  let storiesList = stories
    .filter((e) =>
      !!activeStory?.userId
        ? e.userId === activeStory?.userId
        : e.organizationId === activeStory?.organizationId
    )
    .map((e) => e.id);

  if (storyViewModal.group) {
    storiesList.reverse();
  }

  const [activeIndex, setActiveIndex] = useState(0);

  const file = files.find((e) => e[0] === activeStory?.fileIds[0]) || [];

  const chatId = chats.find(
    (e) =>
      (e.type === "person" || e.type === "organization") &&
      activeStory?.userId !== activeUserId &&
      (!!activeStory?.userId
        ? e.personId === activeStory?.userId
        : e.organizationId === activeStory?.organizationId)
  )?.id;

  const viewThisStory = useCallback(async () => {
    if (activeStory?.isViewed === null) {
      await actionUpdatePostMeta([
        {
          isLike: false,
          isView: true,
          post: storyViewModal.storyId,
          user: activeUserId,
        },
      ]);
    }
  }, [
    actionUpdatePostMeta,
    activeStory?.isViewed,
    activeUserId,
    storyViewModal.storyId,
  ]);

  const handleLike = useCallback(async () => {
    const isLiked = activeStory?.isLiked !== null;

    let rawData = isLiked
      ? {
          id: activeStory?.isLiked,
          isLike: false,
          isView: false,
          post: storyViewModal.storyId,
        }
      : {
          isLike: true,
          isView: false,
          user: activeUserId,
          post: storyViewModal.storyId,
        };

    await actionUpdatePostMeta([rawData], isLiked);
  }, [
    actionUpdatePostMeta,
    activeStory?.isLiked,
    activeUserId,
    storyViewModal.storyId,
  ]);

  const setActiveStory = useCallback(
    (type: "back" | "next") => {
      if (
        (activeIndex === 0 && type === "back") ||
        (activeIndex === storiesList.length - 1 && type === "next")
      ) {
        return;
      }

      setActiveIndex((value) => value + (type === "back" ? -1 : 1));

      setStoryViewModal({
        storyId:
          storiesList[
            (storyViewModal.group
              ? activeIndex
              : storiesList.findIndex((e) => e === storyViewModal.storyId)) +
              (type === "back" ? -1 : 1)
          ],
      });
    },
    [
      activeIndex,
      storiesList,
      setStoryViewModal,
      storyViewModal.group,
      storyViewModal.storyId,
    ]
  );

  const closeModal = useCallback(() => {
    setStoryViewModal({
      isOpen: false,
      storyId: 0,
    });
    setActiveIndex(0);
  }, [setStoryViewModal]);

  const handleDelete = useCallback(async () => {
    await actionUpdatePosts(
      [
        {
          id: storyViewModal.storyId,
          type: "story",
        },
      ],
      true
    );
    closeModal();
  }, [actionUpdatePosts, closeModal, storyViewModal.storyId]);

  useHotkeys([
    ["ArrowLeft", () => setActiveStory("back")],
    ["ArrowRight", () => setActiveStory("next")],
  ]);

  useEffect(() => {
    if (storyViewModal.storyId > 0) {
      viewThisStory();

      if (!storyViewModal.group) {
        setActiveIndex(
          storiesList.findIndex((e) => e === storyViewModal.storyId)
        );
      } else {
        setStoryViewModal({
          storyId: storiesList[0],
        });
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [storyViewModal.isOpen]);

  return (
    <Modal
      opened={storyViewModal.isOpen}
      onClose={closeModal}
      centered
      transition={
        storyViewModal.isOpen !== undefined ? "slide-down" : "slide-up"
      }
      withCloseButton={false}
      sx={{
        ".mantine-Modal-modal": {
          padding: 0,
        },
        zIndex: 100000000,
      }}
      size={450}
      styles={{
        body: {
          position: "relative",
          maxHeight: "calc(100vh - 120px)",
        },
        modal: {
          position: "relative",
          margin: 0,
        },
        inner: {
          overflow: "hidden",
        },
      }}
    >
      <Stack
        sx={(theme) => ({
          position: "absolute",
          top: 0,
          left: 0,
          right: 0,
          zIndex: 200,
          userSelect: "none",
          borderRadius: theme.radius.md,
          overflow: "hidden",
        })}
        spacing={0}
      >
        {storyViewModal.group && (
          // <Progress
          //   color="blue"
          //   size="xs"
          //   value={((activeIndex + 1) / storiesList.length) * 100}
          // />
          <Group grow spacing={4} sx={{ width: "100%", height: 2 }} mt={1}>
            {Array(storiesList.length)
              .fill("")
              .map((e, i) => {
                return (
                  <Box
                    key={`story-${i}`}
                    sx={(theme) => ({
                      height: 2,
                      background:
                        i <= activeIndex
                          ? theme.colors.blue[9]
                          : theme.colors.gray[9],
                      transition: "background-color .1s linear",
                    })}
                  />
                );
              })}
          </Group>
        )}
        <Group p={12} position="apart" noWrap>
          <Group
            spacing={12}
            sx={{ position: "relative", width: "calc(100% - 50px)" }}
            noWrap
          >
            <ActiveAvatar userId={activeStory?.userId} size={32} radius="lg" />
            {
              <Stack spacing={0}>
                {!!owner && (
                  <Anchor
                    size={14}
                    weight={600}
                    sx={{
                      color: theme.colors.gray[5],
                      textDecoration: "none !important",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                    onClick={() => {
                      push(`/profile/user/${activeStory?.userId}`);
                      setStoryViewModal({
                        isOpen: false,
                        storyId: 0,
                      });
                    }}
                  >
                    {!!activeStory?.userId &&
                      `${(owner as UserType).name} ${
                        (owner as UserType).surname
                      }`}
                  </Anchor>
                )}
                {!!activeStory?.updatedAt && (
                  <Text size="xs" weight={600}>
                    {formatTime(activeStory?.updatedAt)}{" "}
                    {formatDate(activeStory?.updatedAt)}
                  </Text>
                )}
              </Stack>
            }
          </Group>
          <CloseButton size="lg" variant="transparent" onClick={closeModal} />
        </Group>
      </Stack>

      {activeIndex !== 0 && (
        <ActionIcon
          size="lg"
          variant="transparent"
          sx={{
            position: "absolute",
            top: "50%",
            transform: `${
              matches ? "translate(20%, -50%)" : "translate(-100%, -50%)"
            } !important`,
            zIndex: 100,
          }}
          onClick={() => setActiveStory("back")}
        >
          <ChevronLeftIcon width={24} height={24} />
        </ActionIcon>
      )}

      {activeIndex !== storiesList.length - 1 && (
        <ActionIcon
          size="lg"
          variant="transparent"
          sx={{
            position: "absolute",
            top: "50%",
            right: 0,
            transform: `${
              matches ? "translate(-20%, -50%)" : "translate(100%, -50%)"
            } !important`,
            zIndex: 100,
          }}
          onClick={() => setActiveStory("next")}
        >
          <ChevronRightIcon width={24} height={24} />
        </ActionIcon>
      )}

      <Box
        sx={(theme) => ({
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          overflow: "hidden",
          borderRadius: theme.radius.md,
        })}
      >
        <Box
          sx={{
            position: "absolute",
            top: -1,
            left: 0,
            right: 0,
            width: "100%",
            height: 1,
            zIndex: 100,
            boxShadow: "0px 0px 80px 45px black",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            bottom: -1,
            left: 0,
            right: 0,
            width: "100%",
            height: 1,
            zIndex: 100,
            boxShadow: "0px 0px 80px 60px black",
          }}
        />
      </Box>

      <Stack
        sx={(theme) => ({
          position: "absolute",
          bottom: 0,
          left: 0,
          right: 0,
          width: "100%",
          zIndex: 200,

          "::before": {
            content: '""',
            position: "absolute",
            width: "100%",
            height: "100%",
            zIndex: -1,
            left: 0,
            bottom: 0,
            background: "#00000066",
            borderBottomLeftRadius: theme.radius.md,
            borderBottomRightRadius: theme.radius.md,
          },
        })}
        p={12}
        spacing={0}
      >
        <Spoiler
          maxHeight={30}
          showLabel={t("showMore")}
          hideLabel={t("hide")}
          styles={(theme) => ({
            control: {
              fontSize: theme.fontSizes.xs,
              color: theme.colors.gray[6],
              fontWeight: 600,
              textDecoration: "none !important",
              zIndex: 110,
              marginBottom: 8,
            },
          })}
        >
          <Text size="sm" weight={600} sx={{ color: "white" }} mb={8}>
            {activeStory?.description}
          </Text>
        </Spoiler>

        <Group position="apart">
          <Group spacing={4}>
            <ActionIcon
              size="sm"
              variant="transparent"
              color="pink"
              onClick={handleLike}
            >
              {activeStory?.isLiked !== null ? (
                <HeartIconFilled style={{ width: 20, height: 20 }} />
              ) : (
                <HeartIcon style={{ width: 20, height: 20 }} />
              )}
            </ActionIcon>
            {activeStory?.userId === activeUserId && (
              <Anchor
                size="sm"
                weight={600}
                sx={(theme) => ({
                  color:
                    theme.colorScheme === "dark"
                      ? theme.colors.gray[4]
                      : theme.colors.dark[6],
                  textDecoration: "none !important",
                })}
                onClick={() =>
                  setViewLikesModal({
                    isOpen: true,
                    postId: activeStory.id,
                  })
                }
              >
                {activeStory?.likeCount}
              </Anchor>
            )}
          </Group>
          {activeStory?.userId === activeUserId && (
            <Group spacing={8}>
              <Group spacing={4}>
                <EyeIcon width={20} height={20} />
                {activeStory?.userId === activeUserId && (
                  <Anchor
                    size="sm"
                    weight={600}
                    sx={(theme) => ({
                      color:
                        theme.colorScheme === "dark"
                          ? theme.colors.gray[4]
                          : theme.colors.dark[6],
                      textDecoration: "none !important",
                    })}
                    onClick={() =>
                      setStoryViewsModal({
                        isOpen: true,
                        storyId: activeStory.id,
                      })
                    }
                  >
                    {activeStory?.viewCount}
                  </Anchor>
                )}
              </Group>
              <Menu width={150} position="top-end">
                <Menu.Target>
                  <ActionIcon>
                    <EllipsisVerticalIcon width={20} height={20} />
                  </ActionIcon>
                </Menu.Target>
                <Menu.Dropdown>
                  <Menu.Item
                    onClick={() => {
                      closeModal();
                      setPostModal({
                        data: activeStory,
                        isOpen: true,
                        modalType: "edit",
                        type: "story",
                      });
                    }}
                  >
                    {t("post.edit")}
                  </Menu.Item>
                  <Menu.Item onClick={handleDelete}>{t("delete")}</Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Group>
          )}

          {!!chatId && (
            <ActionIcon
              size="lg"
              variant="transparent"
              onClick={() => {
                closeModal();
                push(`/apps/chat/${chatId}`);
              }}
            >
              <PaperAirplaneIcon width={24} height={24} />
            </ActionIcon>
          )}
        </Group>
      </Stack>

      <AspectRatio
        ratio={9 / 16}
        sx={(theme) => ({
          height: "100%",
          width: "100%",
          maxWidth: 450,
          borderRadius: theme.radius.md,
          img: {
            width: "100%",
            height: "100%",
            borderRadius: theme.radius.md,
          },
        })}
      >
        {file[2] && (
          <Image
            fill
            alt="story"
            src={file[2] as string}
            draggable={false}
            loading="lazy"
          />
        )}
      </AspectRatio>
    </Modal>
  );
};

export default StoryViewModal;
