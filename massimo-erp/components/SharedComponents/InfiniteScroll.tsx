import { ChevronUpIcon } from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Affix,
  Group,
  Loader,
  Text,
  Transition,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import React, { useCallback, useState } from "react";
import InfiniteScroll from "react-infinite-scroll-component";
import { CC } from "~types";

const CustomInfiniteScroll: CC<{
  dataLength: number;
  hasMore: boolean;
  height?: string | number;
  scrollableTarget?: string;
  inverse?: boolean;
  next: () => void;
}> = ({
  dataLength,
  hasMore,
  height = undefined,
  inverse,
  scrollableTarget,
  next,
  children,
}) => {
  const { t } = useTranslation();

  const [showAffix, setShowAffix] = useState(false);

  const infiniteScroll = document.querySelector(".infinite-scoll");

  infiniteScroll?.addEventListener("scroll", (e) => {
    if ((e.target as any).scrollTop > 10) {
      setShowAffix(true);
    } else {
      setShowAffix(false);
    }
  });

  const scrollToTop = useCallback(() => {
    infiniteScroll?.scrollTo(0, 0);
    setShowAffix(false);
  }, [infiniteScroll]);

  return (
    <InfiniteScroll
      dataLength={dataLength} //This is important field to render the next data
      next={next}
      hasMore={hasMore}
      loader={
        <Group position="center" py="sm">
          <Loader size="xs" variant="bars" />
          <Text size="sm" weight={600} color="dimmed">
            {t("infiniteScroll.loader")}...
          </Text>
        </Group>
      }
      inverse={inverse}
      pullDownToRefresh={false}
      height={height}
      className="infinite-scoll"
      style={{
        scrollBehavior: "smooth",
        overflowX: "hidden",
        ...(inverse
          ? { display: "flex", flexDirection: "column-reverse" }
          : {}),
      }}
      scrollableTarget={scrollableTarget || ""}
    >
      <Affix position={{ bottom: 20, right: 20 }}>
        <Transition transition="slide-up" mounted={showAffix}>
          {(transitionStyles) => (
            <ActionIcon
              size="xl"
              variant="light"
              color="blue"
              style={transitionStyles}
              onClick={scrollToTop}
            >
              <ChevronUpIcon width={20} height={20} />
            </ActionIcon>
          )}
        </Transition>
      </Affix>
      {children}
    </InfiniteScroll>
  );
};

export default CustomInfiniteScroll;
