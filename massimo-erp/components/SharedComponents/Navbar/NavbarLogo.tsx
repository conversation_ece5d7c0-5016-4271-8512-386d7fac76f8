import { Avatar, Group, Title, Stack, Text } from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

const NavbarLogo = function (props: any) {
  const { navbarIsOpen } = props;
  const { version } = useStore(
    "global",
    (state) => ({
      version: state.version!,
    }),
    shallow
  );

  return (
    <Group spacing="sm" px={4} noWrap>
      <Avatar src="/images/logo.png" size="md" />

      <Stack
        spacing={0}
        sx={{
          whiteSpace: "nowrap",
          overflow: "hidden",
          width: navbarIsOpen ? "100%" : 0,
          transition: "width .4s ease",
        }}
      >
        <Text
          size="md"
          weight={700}
          sx={{
            whiteSpace: "nowrap",
          }}
        >
          Massimo ERP
        </Text>
        <Text
          size="xs"
          weight={600}
          color="dimmed"
          sx={{
            whiteSpace: "nowrap",
          }}
        >
          {version}
        </Text>
      </Stack>
    </Group>
  );
};

export default NavbarLogo;
