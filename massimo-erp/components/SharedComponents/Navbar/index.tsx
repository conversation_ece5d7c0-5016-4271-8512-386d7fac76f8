import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "next-i18next";

import {
  ActionIcon,
  Divider,
  Group,
  Navbar,
  Space,
  Stack,
  Text,
  ScrollArea,
  MediaQuery,
  Box,
} from "@mantine/core";

import {
  ChevronDoubleRightIcon,
  CalendarDaysIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";

import NavbarLogo from "./NavbarLogo";
import Profile from "./Profile";
import NavItem from "./NavItem";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import ProjectSelector from "../Header/ProjectSelector";
import ColorSchemeToggler from "~components/ColorSchemeToggler";
import LanguageSelector from "../../LanguageSelector";
import Link from "next/link";
import { openSpotlight } from "@mantine/spotlight";
import { useMediaQuery } from "@mantine/hooks";
import { checkAccess } from "~utils/accessMap";
import { UserType } from "~utils/types/User";

const AppNavbar = function (props: any) {
  const { colorScheme, toggleColorScheme } = props;

  const {
    isNavbarMinimized,
    setNavbarMinimize,
    isNavbarHover,
    setNavbarHover,
    activeUserId,
  } = useStore(
    "global",
    (state) => ({
      isNavbarMinimized: state.isNavbarMinimized!,
      setNavbarMinimize: state.setNavbarMinimize!,
      isNavbarHover: state.isNavbarHover!,
      setNavbarHover: state.setNavbarHover!,
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { navigations: rawNavigations } = useStore(
    "data",
    (state) => ({
      navigations: state.navigations!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { mobileIsOpenNavbar, setMobileIsOpenNavbar } = useStore(
    "temp",
    (state) => ({
      mobileIsOpenNavbar: state.mobileIsOpenNavbar!,
      setMobileIsOpenNavbar: state.setMobileIsOpenNavbar!,
    }),
    shallow
  );

  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 992px)");
  const { t } = useTranslation("common");
  const [navigations, setNavigations] = useState<any[]>([]);

  useEffect(() => {
    const activeUser = users.find((user: UserType) => user.id === activeUserId);

    if (activeUser) {
      if (activeUser.isCustomer) {
        setNavigations(checkAccess(rawNavigations, "customer"));
      } else if (
        (roles.find((role) => role.id === activeUser.defaultRoleId[0])
          ?.weight || 0) < 0
      ) {
        setNavigations(checkAccess(rawNavigations, "admin"));
      } else {
        setNavigations(checkAccess(rawNavigations, "employee"));
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeUserId, rawNavigations, users]);

  return (
    <MediaQuery
      smallerThan="md"
      styles={{
        position: "absolute",
        width: "100vw",
        height: "100vh",
        top: 0,
        left: mobileIsOpenNavbar ? 0 : "-100%",
        zIndex: 500,
        overflow: "visible",
      }}
    >
      <Navbar
        width={{ base: isNavbarMinimized && !isNavbarHover ? 80 : 300 }}
        height={"100%"}
        sx={{
          flexShrink: 0,
          transition: "width .4s ease, left .4s ease",
          overflow: "hidden",
        }}
        onMouseEnter={() => setNavbarHover(true)}
        onMouseLeave={() => setNavbarHover(false)}
        zIndex={500}
      >
        <Group position="apart" sx={{ flexWrap: "nowrap" }} p="md" pb={0}>
          <NavbarLogo
            navbarIsOpen={matches ? true : !isNavbarMinimized || isNavbarHover}
          />

          <MediaQuery
            smallerThan="md"
            styles={{
              display: "none",
            }}
          >
            <ActionIcon
              size="sm"
              onClick={() => setNavbarMinimize(!isNavbarMinimized)}
              mb={4}
            >
              <ChevronDoubleRightIcon
                style={{
                  transform: `rotate(${
                    isNavbarMinimized ? "0deg" : "-180deg"
                  })`,
                  transition: "transform .3s ease",
                }}
              />
            </ActionIcon>
          </MediaQuery>

          <MediaQuery
            largerThan="md"
            styles={{
              display: "none",
            }}
          >
            <ActionIcon size="sm" onClick={() => setMobileIsOpenNavbar(false)}>
              <XMarkIcon />
            </ActionIcon>
          </MediaQuery>
        </Group>
        <MediaQuery largerThan="md" styles={{ display: "none" }}>
          <Group position="apart" px="lg" mt="xl">
            <Group>
              <ActionIcon onClick={() => openSpotlight()}>
                <MagnifyingGlassIcon />
              </ActionIcon>
              <Link href="/apps/calendar">
                <ActionIcon>
                  <CalendarDaysIcon />
                </ActionIcon>
              </Link>
              <Space />
            </Group>
            <Group>
              <ColorSchemeToggler
                colorScheme={colorScheme}
                toggleColorScheme={() => toggleColorScheme()}
              />
              <LanguageSelector />
            </Group>
          </Group>
        </MediaQuery>
        <Space h="lg" />
        <ScrollArea
          scrollbarSize={7}
          px="md"
          styles={(theme) => ({
            viewport: {
              position: "relative",
              "& > div": {
                display: "unset !important",
                overflow: "visible",
              },
              overflow: "visible",
            },
            root: {
              position: "relative",
            },
            scrollbar: {
              '&[data-orientation="horizontal"] .mantine-ScrollArea-thumb': {
                display: "none",
              },
            },
          })}
        >
          <Space h={4} />
          <Profile
            navbarIsOpen={matches ? true : !isNavbarMinimized || isNavbarHover}
          />
          <Space h="xs" />

          <MediaQuery largerThan="md" styles={{ display: "none" }}>
            <Group mt="sm">
              <ProjectSelector />
              <Space w={8}></Space>
            </Group>
          </MediaQuery>

          <Space h="sm" />
          <Stack spacing={4}>
            {navigations.map((navItem: any, i: number) =>
              !!navItem.label ? (
                <Box
                  key={"navSeperator-" + i}
                  sx={{
                    width: (
                      matches ? true : !isNavbarMinimized || isNavbarHover
                    )
                      ? "100%"
                      : 46,
                    height: 30,
                    transition: "width .5s ease",
                  }}
                >
                  {matches || !isNavbarMinimized || isNavbarHover ? (
                    <Text
                      key={"navLabel-" + i}
                      weight={600}
                      ml={8}
                      mt={8}
                      size={14}
                      color="dimmed"
                    >
                      {t(`navbar.${navItem.label}`)}
                    </Text>
                  ) : (
                    <Divider key={"navDivider-" + i} my={15} mx={12} />
                  )}
                </Box>
              ) : (
                <NavItem
                  key={"navItem-" + i}
                  navItem={navItem}
                  navbarIsOpen={
                    matches ? true : !isNavbarMinimized || isNavbarHover
                  }
                />
              )
            )}
          </Stack>

          <Space h="sm" />
        </ScrollArea>
      </Navbar>
    </MediaQuery>
  );
};

export default AppNavbar;
