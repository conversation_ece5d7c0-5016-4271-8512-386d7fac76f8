import React, { useState } from "react";
import Link from "next/link";
import { Router, useRouter } from "next/router";
import { useTranslation } from "next-i18next";

import { Avatar, NavLink, Stack, Box, ThemeIcon, Badge } from "@mantine/core";
import {
  CalendarDaysIcon,
  ChatBubbleLeftIcon,
  CodeBracketIcon,
  LockClosedIcon,
  PresentationChartBarIcon,
  ChartPieIcon,
  ChartBarIcon,
  ChevronRightIcon,
  UserIcon,
  UsersIcon,
  CommandLineIcon,
  BuildingStorefrontIcon,
  BuildingOffice2Icon,
  ClipboardDocumentListIcon,
  DocumentTextIcon,
  QuestionMarkCircleIcon,
  ExclamationCircleIcon,
  PhoneIcon,
  Cog6ToothIcon,
  UserGroupIcon,
  LifebuoyIcon,
  Square2StackIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

const navigationIcons = {
  Dashboard: PresentationChartBarIcon,
  CRM: ChartPieIcon,
  Analytics: ChartBarIcon,
  Profile: UserIcon,
  Explore: MagnifyingGlassIcon,
  Chat: ChatBubbleLeftIcon,
  Calendar: CalendarDaysIcon,
  Kanban: Square2StackIcon,
  Invoices: DocumentTextIcon,
  Support: LifebuoyIcon,
  FAQ: QuestionMarkCircleIcon,
  Privacy: ExclamationCircleIcon,
  "Contact Us": PhoneIcon,
  Requests: ClipboardDocumentListIcon,
  Departments: BuildingOffice2Icon,
  Users: UsersIcon,
  Customers: BuildingStorefrontIcon,
  Projects: CommandLineIcon,
  Permissions: LockClosedIcon,
  Settings: Cog6ToothIcon,
};

const NavItem = function (props: any) {
  const { notifications } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
    }),
    shallow
  );
  const { chats } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
    }),
    shallow
  );
  const { connectionRequests } = useStore(
    "users",
    (state) => ({
      connectionRequests: state.connectionRequests!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  const { navItem, navbarIsOpen } = props;
  const router = useRouter();
  const { t } = useTranslation("common");

  const [itemIsOpen, setItemIsOpen] = useState(false);

  const checkChildrens =
    navItem.childrens?.filter((e: any) => e.path === router.pathname).length >
    0;

  const NavItemIcon =
    navigationIcons[navItem.title as keyof typeof navigationIcons];

  let notificationLength = 0;

  if (navItem.title === "Chat") {
    notificationLength = notifications.filter(
      (e) =>
        !!e &&
        e.target === "messages" &&
        ["person", "organization"].includes(
          chats.find((c) => c.id === +e.value)?.type as string
        )
    ).length;
  } else if (navItem.title === "Kanban") {
    notificationLength = notifications.filter(
      (e) =>
        !!e &&
        (e.target === "tasks" ||
          (e.target === "messages" &&
            chats.find((c) => c.id === +e.value)?.type === "task"))
    ).length;
  } else if (navItem.title === "Profile") {
    notificationLength = connectionRequests.filter(
      (e) =>
        (e.target1Id === activeUserId && e.isAccepted2) ||
        (e.target2Id === activeUserId && e.isAccepted1)
    ).length;
  }

  return !!navItem.childrens ? (
    <NavLink
      sx={(theme) => ({
        borderRadius: "8px",
        width: navbarIsOpen ? "100%" : 46,
        transition: "width .4s ease",
        whiteSpace: "nowrap",
      })}
      color={navItem.color}
      label={t(`navbar.${navItem.title}`)}
      icon={
        <ThemeIcon variant="light" size={30} color={navItem.color}>
          <NavItemIcon style={{ width: 20, height: 20 }} />
        </ThemeIcon>
      }
      rightSection={
        navbarIsOpen ? (
          <ChevronRightIcon style={{ width: 14, height: 14 }} />
        ) : (
          <Box></Box>
        )
      }
      childrenOffset={0}
      p={8}
      opened={itemIsOpen}
      onChange={(isOpen: boolean) => setItemIsOpen(isOpen)}
      active={!itemIsOpen && checkChildrens}
    >
      <Stack spacing={4}>
        {!!navItem.childrens &&
          navItem.childrens.map((nav: any, i: number) => {
            const NavItemChildIcon =
              navigationIcons[
                navItem.childrens[i].title as keyof typeof navigationIcons
              ];

            return (
              <Link href={nav.path} key={"navItemChild-" + i}>
                <NavLink
                  color={navItem.color}
                  label={t(`navbar.${nav.title}`)}
                  active={router.pathname === nav.path}
                  sx={{
                    borderRadius: "8px",
                    transition: "width .4s ease",
                    width: navbarIsOpen ? "100%" : 46,
                  }}
                  icon={
                    <ThemeIcon variant="light" size={30} color={navItem.color}>
                      <NavItemChildIcon style={{ width: 20, height: 20 }} />
                    </ThemeIcon>
                  }
                  p={8}
                />
              </Link>
            );
          })}
      </Stack>
    </NavLink>
  ) : (
    <Link href={navItem.path}>
      <NavLink
        color={navItem.color}
        active={
          router.pathname.split("/").slice(0, 3).join("/") === navItem.path
        }
        label={t(`navbar.${navItem.title}`)}
        icon={
          <ThemeIcon variant="light" size={30} color={navItem.color}>
            <NavItemIcon style={{ width: 20, height: 20 }} />
          </ThemeIcon>
        }
        p={8}
        sx={{
          borderRadius: "8px",
          width: navbarIsOpen ? "100%" : 46,
          transition: "width .4s ease",
          whiteSpace: "nowrap",
        }}
        rightSection={
          notificationLength > 0 && (
            // <Box
            //   sx={(theme) => ({
            //     display: "flex",
            //     alignItems: "center",
            //     justifyContent: "center",
            //     width: 20,
            //     height: 20,
            //     background: theme.colors.red[9],
            //     color: "#fff",
            //     borderRadius: "50%",
            //     fontSize: "12px",
            //   })}
            // >
            //   {notificationLength}
            // </Box>
            <Badge px={6} color={navItem.color}>
              {notificationLength}
            </Badge>
          )
        }
      />
    </Link>
  );
};

export default NavItem;
