import { useState } from "react";

import {
  Ava<PERSON>,
  Menu,
  Box,
  Group,
  Stack,
  Text,
  Title,
  Indicator,
  Tooltip,
} from "@mantine/core";
import { ChevronRightIcon } from "@heroicons/react/24/outline";

import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useRouter } from "next/router";
import { UserType } from "~utils/types/User";
import { RoleType } from "~utils/types/Roles";
import { findLast } from "lodash";
import ActiveAvatar from "~components/ActiveAvatar";
import { useTranslation } from "next-i18next";

const Profile = function (props: any) {
  const { navbarIsOpen } = props;
  const router = useRouter();
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  const { userStatusColors } = useStore(
    "data",
    (state) => ({
      userStatusColors: state.userStatusColors!,
    }),
    shallow
  );
  const { userStatus } = useStore(
    "sockets",
    (state) => ({
      userStatus: state.userStatus!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const activeProfile = users.find((e) => e.id === activeUserId) as
    | UserType
    | undefined;
  const [status, setStatus] = useState("online");

  return (
    <Group
      align="center"
      spacing={12}
      px={4}
      noWrap
      sx={{ position: "relative" }}
    >
      {/* <Menu shadow="md" width={200} position="bottom-start">
        <Menu.Target>
          
        </Menu.Target>

        <Menu.Dropdown>
          <Menu.Label>Status</Menu.Label>
          <Menu.Item
            color={status === "online" ? "green" : ""}
            onClick={() => setStatus("online")}
          >
            <Group>
              <Box
                sx={(theme) => ({
                  width: 10,
                  height: 10,
                  background: theme.colors.green[7],
                  borderRadius: "50%",
                })}
              />
              Online
            </Group>
          </Menu.Item>
          <Menu.Item
            color={status === "idle" ? "yellow" : ""}
            onClick={() => setStatus("idle")}
          >
            <Group>
              <Box
                sx={(theme) => ({
                  width: 10,
                  height: 10,
                  background: theme.colors.yellow[7],
                  borderRadius: "50%",
                })}
              />
              Idle
            </Group>
          </Menu.Item>
          <Menu.Item
            color={status === "doNotDisturb" ? "red" : ""}
            onClick={() => setStatus("doNotDisturb")}
          >
            <Group>
              <Box
                sx={(theme) => ({
                  width: 10,
                  height: 10,
                  background: theme.colors.red[7],
                  borderRadius: "50%",
                })}
              />
              Do Not Disturb
            </Group>
          </Menu.Item>
        </Menu.Dropdown>
      </Menu> */}
      {/* <Indicator
        dot
        inline
        size={12}
        offset={7}
        position="bottom-end"
        color={userStatusColors[userStatus[activeUserId]]}
        withBorder
      > */}
      <ActiveAvatar
        size={navbarIsOpen ? 48 : "md"}
        radius="xl"
        sx={{ transition: "all .5s ease" }}
      />
      {/* </Indicator> */}

      <Stack
        spacing={0}
        sx={() => ({
          width: "calc(100% - 60px)",
          position: "relative",
          cursor: "pointer",

          "#rightArrow": {
            position: "absolute",
            top: "50%",
            right: "50%",
            transform: "translate(-50%, -50%)",
            opacity: 0,
            transition: "all .2s ease",
            zIndex: 10,
          },
          "&:hover #rightArrow": {
            right: -3,
            opacity: 1,
          },
        })}
        onClick={() => router.push("/profile")}
      >
        <div id="rightArrow">
          <ChevronRightIcon style={{ width: 14, height: 14 }} />
        </div>

        <Tooltip
          label={`${activeProfile?.name} ${activeProfile?.surname}`}
          zIndex={10}
        >
          <Title
            order={4}
            sx={{
              maxWidth: "calc(100% - 24px)",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              overflow: "hidden",
            }}
          >
            {activeProfile?.name} {activeProfile?.surname}
          </Title>
        </Tooltip>

        <Text
          color="dimmed"
          size="sm"
          sx={{
            maxWidth: "calc(100% - 24px)",
            whiteSpace: "nowrap",
            textOverflow: "ellipsis",
            overflow: "hidden",
          }}
        >
          {t(
            `defaultRoles.${(
              roles.find((x) => x.id === activeProfile?.defaultRoleId[0]) as
                | RoleType
                | undefined
            )?.label?.toLowerCase()}`
          )}
        </Text>
      </Stack>
    </Group>
  );
};

export default Profile;
