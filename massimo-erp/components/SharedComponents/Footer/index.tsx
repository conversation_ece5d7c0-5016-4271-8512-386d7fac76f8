import { createStyles, Box, Group, Anchor, Text } from "@mantine/core";
import Link from "next/link";
import { useRouter } from "next/router";
import shallow from "zustand/shallow";
import MassimoLogo from "~components/MassimoLogo";
import { useStore } from "~utils/store";

const useStyles = createStyles((theme) => ({
  footer: {
    borderTop: `1px solid ${
      theme.colorScheme === "dark" ? theme.colors.dark[5] : theme.colors.gray[2]
    }`,
  },

  inner: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: theme.spacing.xl,
    paddingBottom: theme.spacing.xl,
    paddingLeft: 32,
    paddingRight: 32,

    [theme.fn.smallerThan("md")]: {
      flexDirection: "column",
    },
  },

  links: {
    [theme.fn.smallerThan("md")]: {
      marginTop: theme.spacing.md,
    },
  },
}));

interface FooterSimpleProps {
  links: { link: string; label: string }[];
}

export default function FooterSimple({ links }: FooterSimpleProps) {
  const { classes } = useStyles();
  const { push } = useRouter();

  const { version } = useStore(
    "global",
    (state) => ({
      version: state.version,
    }),
    shallow
  );

  const items = links.map((link) => (
    <Anchor
      key={link.label}
      color="dimmed"
      size="sm"
      onClick={() => push(link.link)}
    >
      {link.label}
    </Anchor>
  ));

  return (
    <div className={classes.footer}>
      <Box className={classes.inner}>
        <Group>
          <MassimoLogo />
          <Text size="sm" color="dimmed">
            {version}
          </Text>
        </Group>
        <Group className={classes.links}>{items}</Group>
      </Box>
    </div>
  );
}
