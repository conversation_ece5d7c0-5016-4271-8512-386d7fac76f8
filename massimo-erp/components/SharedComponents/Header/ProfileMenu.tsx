import {
  ArrowRightOnRectangleIcon,
  ChatBubbleLeftIcon,
  Cog6ToothIcon,
  UserIcon,
} from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Avatar,
  Divider,
  Group,
  Indicator,
  Menu,
  Stack,
  Text,
  Title,
  Tooltip,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { findLast } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import { useCallback } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { useStore } from "~utils/store";
import { RoleType } from "~utils/types/Roles";
import { UserType } from "~utils/types/User";

const ProfileMenu = function () {
  const { actionLogout, activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
      actionLogout: state.actionLogout!,
    }),
    shallow
  );
  const { userStatusColors } = useStore(
    "data",
    (state) => ({
      userStatusColors: state.userStatusColors!,
    }),
    shallow
  );
  const { userStatus } = useStore(
    "sockets",
    (state) => ({
      userStatus: state.userStatus!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { setUserModal } = useStore(
    "temp",
    (state) => ({
      setUserModal: state.setUserModal!,
    }),
    shallow
  );

  const activeProfile = users.find((e) => e.id === activeUserId) as
    | UserType
    | undefined;

  const { push } = useRouter();
  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 300px)");

  const logout = useCallback(async () => {
    try {
      await actionLogout();
    } finally {
      push("/");
    }
  }, [actionLogout, push]);

  return (
    <Menu
      shadow="md"
      width={matches ? "calc(100vw - 32px)" : 220}
      offset={8}
      position="bottom-end"
      transition="fade"
      withArrow
    >
      <Menu.Target>
        <ActionIcon ml={8} size={38} radius="xl">
          {/* <Indicator
            dot
            inline
            size={12}
            offset={4}
            position="bottom-end"
            color={userStatusColors[userStatus[activeUserId]]}
            withBorder
          > */}
          <ActiveAvatar radius="xl" size={34} />
          {/* </Indicator> */}
        </ActionIcon>
      </Menu.Target>

      <Menu.Dropdown pt={0}>
        <Group p={12} noWrap sx={{ overflow: "hidden" }}>
          {/* <Indicator
            dot
            inline
            size={12}
            offset={4}
            position="bottom-end"
            color={userStatusColors[userStatus[activeUserId]]}
            withBorder
          > */}
          <ActiveAvatar radius="xl" size={34} />
          {/* </Indicator> */}
          <Stack
            sx={{
              position: "relative",
              width: "100%",
            }}
            spacing={0}
          >
            <Tooltip label={`${activeProfile?.name} ${activeProfile?.surname}`}>
              <Title
                order={5}
                sx={{
                  maxWidth: 138,
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                }}
              >
                {activeProfile?.name} {activeProfile?.surname}
              </Title>
            </Tooltip>
            <Text size="xs">
              {t(
                `defaultRoles.${(
                  roles.find(
                    (x) => x.id === activeProfile?.defaultRoleId[0]
                  ) as RoleType | undefined
                )?.label?.toLowerCase()}`
              )}
            </Text>
          </Stack>
        </Group>
        <Divider my={4}></Divider>
        <Menu.Item
          onClick={() => push("/profile")}
          icon={<UserIcon style={{ width: 16, height: 16 }} />}
        >
          {t("profileMenu.profile")}
        </Menu.Item>
        <Menu.Item
          onClick={() => push("/apps/chat")}
          icon={<ChatBubbleLeftIcon style={{ width: 16, height: 16 }} />}
        >
          {t("profileMenu.chat")}
        </Menu.Item>
        <Menu.Item
          onClick={() =>
            setUserModal({
              isOpen: true,
              type: "edit",
              data: users.find((e) => e.id === activeUserId),
            })
          }
          icon={<Cog6ToothIcon style={{ width: 16, height: 16 }} />}
        >
          {t("profileMenu.settings")}
        </Menu.Item>
        <Divider my={4}></Divider>
        <Menu.Item
          color="red"
          icon={<ArrowRightOnRectangleIcon style={{ width: 16, height: 16 }} />}
          onClick={() => logout()}
        >
          {t("profileMenu.logout")}
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

export default ProfileMenu;
