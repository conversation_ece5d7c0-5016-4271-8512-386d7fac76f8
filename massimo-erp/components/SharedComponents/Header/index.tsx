import Link from "next/link";

import {
  ActionIcon,
  Group,
  Header,
  MediaQuery,
  Tooltip,
  Kbd,
  Input,
} from "@mantine/core";
import { openSpotlight } from "@mantine/spotlight";

import {
  MagnifyingGlassIcon,
  CalendarDaysIcon,
  Bars3Icon,
} from "@heroicons/react/24/outline";

import ProjectSelector from "./ProjectSelector";
import LanguageSelector from "~components/LanguageSelector";
import ColorSchemeToggler from "../../ColorSchemeToggler";
import ProfileMenu from "./ProfileMenu";
import NotificationMenu from "../NotificationMenu";
import Breadcrumbs from "./Breadcrumbs";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";

const AppHeader = function (props: any) {
  const { colorScheme, toggleColorScheme } = props;
  const matches = useMediaQuery("(min-width: 1300px)");
  const { asPath } = useRouter();
  const { t } = useTranslation();

  const { setMobileIsOpenNavbar, setProfileSearchModal } = useStore(
    "temp",
    (state) => ({
      setMobileIsOpenNavbar: state.setMobileIsOpenNavbar!,
      setProfileSearchModal: state.setProfileSearchModal!,
    }),
    shallow
  );

  return (
    <Header height={70} sx={{ position: "relative", zIndex: 410 }}>
      <Group position="apart" sx={{ height: "100%" }} p="md">
        <Group align="center">
          <MediaQuery largerThan="md" styles={{ display: "none" }}>
            <ActionIcon size="md" onClick={() => setMobileIsOpenNavbar(true)}>
              <Bars3Icon style={{ width: 22, height: 22 }} />
            </ActionIcon>
          </MediaQuery>

          <Group>
            <MediaQuery smallerThan="md" styles={{ display: "none" }}>
              <Group spacing={8}>
                <Tooltip
                  label={
                    <>
                      <Kbd>Ctrl</Kbd> + <Kbd>K</Kbd>
                    </>
                  }
                  pb="xs"
                >
                  <ActionIcon size="lg" onClick={() => openSpotlight()}>
                    <MagnifyingGlassIcon style={{ width: 22, height: 22 }} />
                  </ActionIcon>
                </Tooltip>
                <Link href="/apps/calendar">
                  <ActionIcon size="lg">
                    <CalendarDaysIcon style={{ width: 22, height: 22 }} />
                  </ActionIcon>
                </Link>
              </Group>
            </MediaQuery>
            {matches && <Breadcrumbs />}
            {matches && asPath.split("/").includes("explore") && (
              <Input
                ml="sm"
                size="xs"
                icon={<MagnifyingGlassIcon width={16} height={16} />}
                placeholder={t("profileSearch")}
                styles={{
                  input: {
                    width: 160,
                  },
                }}
                onClick={() => {
                  setProfileSearchModal({
                    isOpen: true,
                  });
                }}
              />
            )}
          </Group>
        </Group>

        <Group spacing={8} sx={{ position: "relative" }}>
          <MediaQuery smallerThan="md" styles={{ display: "none" }}>
            <Group spacing={8}>
              <Group noWrap>
                <ProjectSelector />
                <ColorSchemeToggler
                  colorScheme={colorScheme}
                  toggleColorScheme={() => toggleColorScheme()}
                />
              </Group>
              <LanguageSelector />
            </Group>
          </MediaQuery>
          <NotificationMenu />
          <ProfileMenu />
        </Group>
      </Group>
    </Header>
  );
};

export default AppHeader;
