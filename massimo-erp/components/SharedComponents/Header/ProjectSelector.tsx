import { ActionIcon, Group, Text, Box } from "@mantine/core";

import { ChevronUpDownIcon, PlusSmallIcon } from "@heroicons/react/24/outline";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";

const ProjectSelector = function (props: any) {
  const { selectedProjectId, activeUserId } = useStore(
    "global",
    (state) => ({
      selectedProjectId: state.selectedProjectId!,
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const {
    setMobileIsOpenNavbar,
    setSendRequestModal,
    setIsOpenProjectSelectorModal,
    setIsOpenNewTaskModal,
  } = useStore(
    "temp",
    (state) => ({
      setMobileIsOpenNavbar: state.setMobileIsOpenNavbar!,
      setSendRequestModal: state.setSendRequestModal!,
      setIsOpenProjectSelectorModal: state.setIsOpenProjectSelectorModal!,
      setIsOpenNewTaskModal: state.setIsOpenNewTaskModal!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );

  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { pathname } = useRouter();

  const isVisible =
    (pathname.split("/")[1] === "dashboard" ||
      pathname.split("/")[1] === "apps" ||
      pathname.split("/")[1] === "profile") &&
    !pathname.split("/").includes("organization") &&
    !pathname.split("/").includes("chat");

  const activeUser = users.find((user) => user.id === activeUserId);
  const isAdmin =
    (roles.find((role) => role.id === activeUser?.defaultRoleId[0])?.weight ||
      0) < 0;

  const disabled = isAdmin
    ? false
    : activeUser && activeUser.isCustomer && activeUser.organizationIds
    ? !customers.find(
        (customer) => customer.id === activeUser.organizationIds[0]
      )
    : true;

  return isVisible ? (
    <Group
      sx={(theme) => ({
        flexWrap: "nowrap",
        [`@media screen and (max-width: ${theme.breakpoints.md}px)`]: {
          width: "100%",
        },
      })}
      spacing={12}
    >
      {/* <Select
        value={selectedProjectId}
        onChange={(value: string) => setSelectedProject(value)}
        itemComponent={SelectItem}
        data={projects.map((e) => {
          let lastOffer = offers.filter(
            (c) => c.id === e.offerIds[e.offerIds.length - 1]
          )[0];
          return {
            value: e.id,
            label: lastOffer.title,
            customer: customers.filter(
              (c) => c.id === lastOffer.customerId
            )[0].fullName,
          };
        })}
        sx={(theme) => ({
          [`@media screen and (max-width: ${theme.breakpoints.md}px)`]: {
            width: "100%",
          },
        })}
        transition="fade"
        transitionDuration={200}
      /> */}

      <Box
        sx={(theme) => ({
          minWidth: 170,
          width: "100%",
          height: 36,
          background:
            theme.colorScheme === "dark"
              ? theme.colors.dark[8]
              : theme.colors.gray[0],
          borderWidth: 1,
          borderStyle: "solid",
          borderColor:
            theme.colorScheme === "dark"
              ? theme.colors.gray[8]
              : theme.colors.gray[3],
          padding: "8px 12px",
          borderRadius: 8,
          cursor: "pointer",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          transition: "filter .2s ease",
          ":hover": {
            filter: "brightness(.9)",
          },
        })}
        onClick={() => {
          setMobileIsOpenNavbar(false);
          setIsOpenProjectSelectorModal(true);
        }}
      >
        {projects.findIndex((e) => e.id === selectedProjectId) > -1 ? (
          <Text size="sm" weight={600}>
            {
              offers
                .filter((o) =>
                  projects
                    .filter((e) => e.id === selectedProjectId)[0]
                    .offerIds.includes(o.id)
                )
                .slice(-1)[0]?.name
            }
          </Text>
        ) : (
          <Text size="xs" weight={600} color="dimmed">
            {t("noOneSelect")}
          </Text>
        )}
        <ChevronUpDownIcon style={{ width: 16, height: 16 }} />
      </Box>

      {!disabled && (
        <ActionIcon
          onClick={() => setIsOpenNewTaskModal(true)}
          size={32}
          color="blue"
          variant="light"
        >
          <PlusSmallIcon style={{ width: 20, height: 20 }} />
        </ActionIcon>
      )}
    </Group>
  ) : (
    <></>
  );
};

export default ProjectSelector;
