import Link from "next/link";

import { Anchor, Breadcrumbs, Text } from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { useRouter } from "next/router";
import { useTranslation } from "next-i18next";

const HeaderBreadcrumbs = function () {
  const { route, query } = useRouter();
  const { t } = useTranslation();

  const pathArray = route.split("/");
  pathArray.shift();

  const items = pathArray
    .filter((e) => e !== "[id]")
    .map((item, index) => {
      // if (index < pathArray.length - 1) {
      //   return (
      //     <Anchor key={index} size="sm" onClick={() => push("/dashboard/crm")}>
      //       {item}
      //     </Anchor>
      //   );
      // } else {
      //   return (
      //     <Text key={index} color="dimmed" size="sm">
      //       {item}
      //     </Text>
      //   );
      // }

      return (
        <Text key={index} color="dimmed" size="sm">
          {t(`breadcrumbs.${item}`)}
        </Text>
      );
    });

  return <Breadcrumbs>{items}</Breadcrumbs>;
};

export default HeaderBreadcrumbs;
