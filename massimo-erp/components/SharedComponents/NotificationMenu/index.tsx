import { useCallback, useEffect, useState } from "react";

import {
  Popover,
  Text,
  ActionIcon,
  Badge,
  Group,
  Indicator,
  Avatar,
  Stack,
  Title,
  Center,
  Button,
  Card,
  Paper,
  ThemeIcon,
  ScrollArea,
  SegmentedControl,
  Tabs,
  Anchor,
  Box,
  Loader,
} from "@mantine/core";

import {
  BellIcon,
  ChatBubbleLeftIcon,
  CheckIcon,
  ChevronRightIcon,
  DocumentTextIcon,
  TrashIcon,
  UserPlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { NotificationType } from "~utils/types/Notification";
import ActiveAvatar from "~components/ActiveAvatar";
import { ConnectionType, UserType } from "~utils/types/User";
import { TaskType } from "~utils/types/Task";
import { MessageType } from "~utils/types/Chat";
import { findLast, isNull, uniq } from "lodash";
import { useRouter } from "next/router";
import { hexToRGBA } from "~utils/tools";
import { CC } from "~types";
import { useMediaQuery } from "@mantine/hooks";
import Notification from "./Notification";

const NotificationMenu = function () {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { chats } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { notifications, actionViewNotification } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
      actionViewNotification: state.actionViewNotification!,
    }),
    shallow
  );
  const {
    users,
    connectionRequests,
    actionGetConnections,
    actionGetConnectionRequests,
    actionConnectUser,
    actionRejectUser,
  } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      connectionRequests: state.connectionRequests!,
      actionGetConnections: state.actionGetConnections!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
      actionConnectUser: state.actionConnectUser!,
      actionRejectUser: state.actionRejectUser!,
    }),
    shallow
  );

  const { push } = useRouter();
  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 450px)");

  const [opened, setOpened] = useState(false);

  const [viewAll, setViewAll] = useState(false);
  const [viewTab, setViewTab] = useState(false);
  const [fetchingUsers, setFetchingUsers] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState<string | undefined>(undefined);

  const connectionRequestNotifications = connectionRequests.filter(
    (e) =>
      (e.target1Id === activeUserId && e.isAccepted2) ||
      (e.target2Id === activeUserId && e.isAccepted1)
  );
  const messageNotifications = notifications.filter(
    (e) =>
      e.target === "messages" &&
      chats.some(
        (e) =>
          (e.type === "person" &&
            [e.personId, e.ownerId].includes(activeUserId)) ||
          (e.type === "organization" &&
            customers
              .find((x) => x.id === e.organizationId)
              ?.personIds.includes(activeUserId))
      )
  );
  const taskNotifications = notifications.filter((e) => e.target === "tasks");

  const notificationsLength =
    connectionRequestNotifications.length + notifications.length;

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const tabs = [
    ...(connectionRequestNotifications.length > 0
      ? [
          {
            value: "connections",
            label: t("notificationTypes.connections"),
          },
        ]
      : []),
    ...(messageNotifications.length > 0
      ? [
          {
            value: "messages",
            label: t("notificationTypes.chats"),
          },
        ]
      : []),
    ...(taskNotifications.length > 0
      ? [
          {
            value: "tasks",
            label: t("notificationTypes.tasks"),
          },
        ]
      : []),
  ];

  const markAllAsRead = useCallback(async () => {
    setViewAll(true);

    try {
      if (notifications.length > 0) {
        await actionViewNotification(
          notifications.map((e: NotificationType) => ({ id: e.id }))
        );
      }
    } finally {
      setViewAll(false);
      setOpened(false);
      setActiveTab(undefined);
    }
  }, [actionViewNotification, notifications]);

  const markOnlyTab = useCallback(async () => {
    setViewTab(true);

    try {
      if (notifications.length > 0) {
        await actionViewNotification(
          notifications
            .filter((e) => e.target === activeTab)
            .map((e: NotificationType) => ({ id: e.id }))
        );
      }
    } finally {
      setViewTab(false);
      setActiveTab(undefined);
    }
  }, [actionViewNotification, activeTab, notifications]);

  const getConnections = useCallback(async () => {
    await actionGetConnections(0);
    await actionGetConnectionRequests();
  }, [actionGetConnectionRequests, actionGetConnections]);

  const handleConnect = useCallback(
    async (targetId: number) => {
      setFetchingUsers((v) => [...v, targetId]);

      try {
        await actionConnectUser(targetId);
        await getConnections();
      } finally {
        setFetchingUsers((v) => v.filter((e) => e !== targetId));
      }
    },
    [actionConnectUser, getConnections]
  );

  const handleReject = useCallback(
    async (targetId: number) => {
      setFetchingUsers((v) => [...v, targetId]);

      try {
        await actionRejectUser(targetId);
        await getConnections();
      } finally {
        setFetchingUsers((v) => v.filter((e) => e !== targetId));
      }
    },
    [actionRejectUser, getConnections]
  );

  useEffect(() => {
    if (!tabs.some((e) => e.value === activeTab)) {
      if (tabs.length > 0) {
        setActiveTab(tabs[0].value);
      } else {
        setActiveTab(undefined);
      }
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, tabs]);

  return (
    <Popover
      shadow="md"
      width={matches ? "calc(100vw - 32px)" : 320}
      offset={12}
      position="bottom-end"
      transition="fade"
      withArrow
      zIndex={150}
      opened={opened}
      onChange={setOpened}
    >
      <Popover.Target>
        <ActionIcon size="lg" onClick={() => setOpened(!opened)}>
          <Indicator
            disabled={notificationsLength === 0}
            inline
            size={12}
            offset={2}
            position="bottom-end"
            color="blue"
            withBorder
          >
            <BellIcon style={{ width: 22, height: 22 }} />
          </Indicator>
        </ActionIcon>
      </Popover.Target>

      <Popover.Dropdown mx={matches ? 16 : 0}>
        <Tabs
          value={activeTab}
          onTabChange={(value) =>
            setActiveTab(isNull(value) ? undefined : value)
          }
        >
          <Stack spacing="xs">
            <Group position="apart" noWrap>
              {notificationsLength === 0 ? (
                <Text size="sm" weight={600}>
                  {t("notifications.title")}
                </Text>
              ) : (
                <ScrollArea scrollbarSize={5} offsetScrollbars>
                  <SegmentedControl
                    size="xs"
                    data={tabs}
                    value={
                      tabs.some((e) => e.value === activeTab)
                        ? activeTab
                        : undefined
                    }
                    onChange={(value) => setActiveTab(value)}
                    styles={(theme) => ({
                      active: {
                        background: hexToRGBA(theme.colors.blue[9], 0.3),
                      },
                    })}
                  />
                </ScrollArea>
              )}

              {notificationsLength > 0 && (
                <Badge size="sm" sx={{ flexShrink: 0 }}>
                  {notificationsLength}
                </Badge>
              )}
            </Group>

            <ScrollArea scrollbarSize={3} offsetScrollbars>
              {notificationsLength === 0 && (
                <Text size="xs" weight={600} color="dimmed" align="center">
                  {t("noNotification")}
                </Text>
              )}

              <Tabs.Panel value="connections">
                <Stack spacing={8} sx={{ maxHeight: 300 }}>
                  {connectionRequestNotifications.map(
                    (connection: ConnectionType, i: number) => {
                      let user = users.find(
                        (e) =>
                          e.id ===
                          [connection.target1Id, connection.target2Id].filter(
                            (e) => e !== activeUserId
                          )[0]
                      );
                      let notificationMessage = `${user?.name} ${user?.surname}`;
                      let notificationRoute = "/";

                      return (
                        <Paper
                          key={"notification-" + i}
                          sx={(theme) => ({
                            background:
                              theme.colorScheme === "dark"
                                ? theme.colors.dark[7]
                                : theme.colors.gray[1],
                            userSelect: "none",
                            ":hover": {
                              background:
                                theme.colorScheme === "dark"
                                  ? theme.colors.dark[8]
                                  : theme.colors.gray[2],
                            },
                          })}
                          p="xs"
                        >
                          <Group
                            position="apart"
                            align="flex-start"
                            noWrap
                            spacing={10}
                            sx={{ position: "relative" }}
                          >
                            <Group
                              align="flex-start"
                              spacing={10}
                              noWrap
                              sx={{ width: "100%" }}
                            >
                              {!!user ? (
                                <ActiveAvatar userId={user?.id} size={32} />
                              ) : (
                                <ThemeIcon size={32} variant="light">
                                  <DocumentTextIcon width={20} height={20} />
                                </ThemeIcon>
                              )}
                              <Stack
                                spacing={0}
                                sx={{ width: "100%" }}
                                align="flex-start"
                              >
                                <Text size="xs" weight={700}>
                                  {t(`notificationTitles.connections`)}
                                </Text>
                                <Anchor
                                  size="xs"
                                  weight={600}
                                  color="dimmed"
                                  onClick={() => {
                                    push(notificationRoute);
                                    setOpened(false);
                                  }}
                                >
                                  {notificationMessage}
                                </Anchor>
                              </Stack>
                            </Group>

                            <Group spacing={4} noWrap sx={{ height: 32 }}>
                              {fetchingUsers.includes(user?.id as number) ? (
                                <Loader size="xs" variant="bars" mr={8} />
                              ) : (
                                <>
                                  <ActionIcon
                                    size="sm"
                                    color="red"
                                    onClick={() =>
                                      handleReject(user?.id as number)
                                    }
                                  >
                                    <XMarkIcon width={16} height={16} />
                                  </ActionIcon>
                                  <ActionIcon
                                    size="sm"
                                    color="green"
                                    onClick={() =>
                                      handleConnect(user?.id as number)
                                    }
                                  >
                                    <CheckIcon width={16} height={16} />
                                  </ActionIcon>
                                </>
                              )}
                            </Group>

                            {/* <ActionIcon
                                size={32}
                                color="gray"
                                onClick={() => {
                                  handleDelete(notification.id);
                                }}
                                loading={deletedNotifications.includes(
                                  notification.id
                                )}
                                loaderProps={{ size: 14 }}
                              >
                                <TrashIcon width={14} height={14} />
                              </ActionIcon> */}
                          </Group>
                        </Paper>
                      );
                    }
                  )}
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value="messages">
                <Stack
                  spacing={8}
                  sx={{ position: "relative", maxHeight: 300 }}
                >
                  {notifications
                    .filter((e) => e.target === "messages")
                    .map((notification: NotificationType, i: number) => (
                      <Notification
                        key={`notification-${i}`}
                        notification={notification}
                        setOpened={setOpened}
                      />
                    ))}
                </Stack>
              </Tabs.Panel>

              <Tabs.Panel value="tasks">
                <Stack
                  spacing={8}
                  sx={{ position: "relative", maxHeight: 300 }}
                >
                  {notifications
                    .filter((e) => e.target === "tasks")
                    .map((notification: NotificationType, i: number) => (
                      <Notification
                        key={`notification-${i}`}
                        notification={notification}
                        setOpened={setOpened}
                      />
                    ))}
                </Stack>
              </Tabs.Panel>
            </ScrollArea>

            <Group grow spacing="xs">
              <Button
                onClick={() => markAllAsRead()}
                variant="light"
                size="xs"
                color="blue"
                loading={viewAll}
                loaderProps={{ variant: "bars" }}
                disabled={
                  messageNotifications.length + taskNotifications.length === 0
                }
              >
                {t("notifications.markAllAsRead")}
              </Button>
              <Button
                onClick={() => markOnlyTab()}
                variant="light"
                size="xs"
                color="blue"
                loading={viewTab}
                loaderProps={{ variant: "bars" }}
                disabled={
                  messageNotifications.length + taskNotifications.length === 0
                }
              >
                {t("notifications.markOnlyTabs")}
              </Button>
            </Group>

            <Group position="right">
              <Anchor
                size="xs"
                weight={600}
                onClick={() => {
                  push("/notifications");
                  setOpened(false);
                }}
                sx={{
                  display: "flex",
                  alignItems: "center",
                  gap: 4,
                }}
              >
                <ChevronRightIcon width={12} height={12} strokeWidth={3} />{" "}
                {t("notifications.center")}
              </Anchor>
            </Group>
          </Stack>
        </Tabs>
      </Popover.Dropdown>
    </Popover>
  );
};

export default NotificationMenu;
