import { DocumentTextIcon, TrashIcon } from "@heroicons/react/24/outline";
import {
  ActionIcon,
  Anchor,
  Group,
  MantineNumberSize,
  Paper,
  Stack,
  Text,
  ThemeIcon,
} from "@mantine/core";
import { findLast, isString, isUndefined } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { NotificationType } from "~utils/types/Notification";
import { UserType } from "~utils/types/User";

const Notification: CC<{
  notification: NotificationType;
  setOpened?: (value: boolean) => void;
  dark?: boolean;
  spacing?: MantineNumberSize;
}> = ({ notification, setOpened, dark = false, spacing = "xs" }) => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setTaskViewModalData, setIsOpenTaskViewModal, setProjectModal } =
    useStore(
      "temp",
      (state) => ({
        setTaskViewModalData: state.setTaskViewModalData!,
        setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
        setProjectModal: state.setProjectModal!,
      }),
      shallow
    );
  const { tasks } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { actionViewNotification } = useStore(
    "notifications",
    (state) => ({
      actionViewNotification: state.actionViewNotification!,
    }),
    shallow
  );
  const { chats, messages } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      messages: state.messages!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { push } = useRouter();

  const [deleting, setDeleting] = useState(false);

  let user: UserType | undefined;
  let notificationTitle: string | undefined;
  let notificationMessage: string | undefined;
  let notificationRoute: string | undefined;

  let handleClick = () => {};

  switch (notification.target) {
    case "tasks":
      notificationMessage = tasks.find(
        (e) => e.id === +notification.value
      )?.title;
      notificationRoute = "/apps/kanban";

      handleClick = () => {
        const activeTask = tasks.find((e) => e.id === +notification.value);

        if (activeTask) {
          setIsOpenTaskViewModal(true);
          setTaskViewModalData(activeTask);
        }
      };
      break;

    case "messages":
      const activeChat = chats.find((e) => e.id === +notification.value);

      const lastMessage = findLast(
        messages,
        (e) => e.chatId === +notification.value
      );

      user = users.find((e) => e.id === lastMessage?.ownerId);

      if (
        activeChat?.type === "person" ||
        activeChat?.type === "organization"
      ) {
        notificationMessage = `${user?.name} ${user?.surname}: ${lastMessage?.content.text}`;
        notificationRoute = `/apps/chat/${+notification.value}`;
      } else if (activeChat?.type === "task") {
        const activeTask = tasks.find((e) => e.id === activeChat.taskId);

        notificationMessage = `${user?.name} ${user?.surname}: ${lastMessage?.content.text}`;
        notificationRoute = "/apps/kanban";

        notificationTitle = `${t("notifications.task")}: ${activeTask?.title}`;

        handleClick = () => {
          if (activeTask) {
            setIsOpenTaskViewModal(true);
            setTaskViewModalData(activeTask);
          }
        };
      } else if (activeChat?.type === "offer") {
        const activeOffer = offers.find((e) => e.id === activeChat.offerId);
        const activeProject = projects.find((e) =>
          e.offerIds.includes(activeOffer?.id as number)
        );

        notificationMessage = `${user?.name} ${user?.surname}: ${lastMessage?.content.text}`;
        notificationRoute = undefined;

        notificationTitle = `${t("notifications.project")}: ${
          activeOffer?.name
        }`;

        handleClick = () => {
          setProjectModal({
            isOpen: true,
            type: "edit",
            data: activeProject,
          });
        };
      }
      break;

    default:
      break;
  }

  const handleDelete = useCallback(
    async (id: number) => {
      setDeleting(true);

      try {
        await actionViewNotification([{ id }]);
      } finally {
        setDeleting(false);
      }
    },
    [actionViewNotification]
  );

  return (
    <Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[7 + (dark ? 1 : 0)]
            : theme.colors.gray[1],
        userSelect: "none",
        ":hover": {
          background:
            theme.colorScheme === "dark"
              ? theme.colors.dark[8 + (dark ? 1 : 0)]
              : theme.colors.gray[2],
        },
      })}
      p={spacing}
    >
      <Group
        position="apart"
        align="center"
        noWrap
        spacing={10}
        sx={{ position: "relative" }}
      >
        <Group align="center" spacing={10} noWrap sx={{ width: "100%" }}>
          {!!user ? (
            <ActiveAvatar userId={user?.id} size={32} />
          ) : (
            <ThemeIcon size={32} variant="light">
              <DocumentTextIcon width={20} height={20} />
            </ThemeIcon>
          )}
          <Stack spacing={0} sx={{ width: "100%" }} align="flex-start">
            <Text size="xs" weight={700}>
              {notificationTitle ? `${notificationTitle} - ` : ""}{" "}
              {t(`notificationTitles.${notification.target}`)}
            </Text>
            <Anchor
              size="xs"
              weight={600}
              color="dimmed"
              sx={{
                maxWidth: "100%",
                whiteSpace: "nowrap",
                textOverflow: "ellipsis ",
                overflow: "hidden",
              }}
              onClick={() => {
                if (isString(notificationRoute)) {
                  push(notificationRoute);
                }

                if (!isUndefined(setOpened)) {
                  setOpened(false);
                }

                handleClick();

                handleDelete(notification.id);
              }}
            >
              {notificationMessage}
            </Anchor>
          </Stack>
        </Group>

        <ActionIcon
          size={32}
          color="gray"
          onClick={() => {
            handleDelete(notification.id);
          }}
          loading={deleting}
          loaderProps={{ size: 14 }}
        >
          <TrashIcon width={14} height={14} />
        </ActionIcon>
      </Group>
    </Paper>
  );
};

export default Notification;
