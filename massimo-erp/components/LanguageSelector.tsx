import { useCallback, useState } from "react";

import { LanguageIcon } from "@heroicons/react/24/outline";
import { ActionIcon, Menu } from "@mantine/core";
import { useRouter } from "next/router";
import { Lang<PERSON> } from "~types";
import { setCookie } from "cookies-next";
import { useTranslation } from "next-i18next";

const LanguageSelector = function () {
  const {
    push,

    query,
    locale,
    asPath,
    pathname,
    defaultLocale,
    locales: languages,
  } = useRouter();
  const abbrLang = (locale ?? defaultLocale) as keyof typeof Langs;

  const selectedLanguage = Langs[abbrLang];
  const { t } = useTranslation();

  const setSelectedLanguage = useCallback(
    (newLocale: string) => {
      const findedLanguagePair = Object.entries(Langs).filter(
        (langData) => langData[1] == newLocale
      )[0];
      const newLanguage = findedLanguagePair[0];
      if (selectedLanguage !== newLocale) {
        push({ pathname, query }, asPath, {
          locale: newLanguage,
        });

        setCookie("NEXT_LOCALE", `${newLanguage}`);
      }
    },
    [asPath, pathname, push, query, selectedLanguage]
  );

  return (
    <Menu
      shadow="md"
      width={200}
      offset={12}
      position="bottom-end"
      transition="fade"
      withArrow
    >
      <Menu.Target>
        <ActionIcon size="lg">
          <LanguageIcon style={{ width: 22, height: 22 }} />
        </ActionIcon>
      </Menu.Target>

      <Menu.Dropdown>
        <Menu.Label>{t("languageSelector.title")}</Menu.Label>
        {Object.values(Langs).map((language, i: number) => (
          <Menu.Item
            key={"language-" + i}
            color={language === selectedLanguage ? "blue" : ""}
            onClick={() => setSelectedLanguage(language)}
          >
            {language}
          </Menu.Item>
        ))}
      </Menu.Dropdown>
    </Menu>
  );
};

export default LanguageSelector;
