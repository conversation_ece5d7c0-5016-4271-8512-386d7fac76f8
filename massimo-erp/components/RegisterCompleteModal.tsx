import {
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  Group,
  Image,
  Modal,
  Stack,
  Text,
  useMantineTheme,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useCallback } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";

const RegisterCompleteModal: CC = function () {
  const { isOpenRegisterCompleteModal, setIsOpenRegisterCompleteModal } =
    useStore(
      "temp",
      (state) => ({
        isOpenRegisterCompleteModal: state.isOpenRegisterCompleteModal!,
        setIsOpenRegisterCompleteModal: state.setIsOpenRegisterCompleteModal!,
      }),
      shallow
    );

  const theme = useMantineTheme();
  const { t } = useTranslation();

  const closeModal = useCallback(() => {
    setIsOpenRegisterCompleteModal(false);
  }, [setIsOpenRegisterCompleteModal]);

  return (
    <Modal
      opened={isOpenRegisterCompleteModal}
      onClose={closeModal}
      withCloseButton={false}
      centered
      transition={isOpenRegisterCompleteModal ? "slide-down" : "slide-up"}
      size={400}
      zIndex={1010}
      styles={{
        modal: {
          overflow: "hidden",
        },
      }}
    >
      <Stack>
        <Group position="center">
          <Image
            alt="Register Completed"
            src={
              theme.colorScheme === "dark"
                ? "/register-dark.gif"
                : "/register-light.gif"
            }
            width={100}
            withPlaceholder
          />
        </Group>
        <Text size="md" weight={500}>
          {t("registerCompleteText")}
        </Text>
        <Divider />
        <Group position="right">
          <Button variant="light" color="blue" onClick={closeModal}>
            {t("ok")}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default RegisterCompleteModal;
