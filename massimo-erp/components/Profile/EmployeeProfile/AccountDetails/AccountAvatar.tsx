import React from "react";
import { Indicator, Avatar } from "@mantine/core";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import { CC } from "~types";

const AccountAvatar: CC<{
  id?: number;
}> = function ({ id }) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  return (
    // <Indicator
    //   dot
    //   inline
    //   size={16}
    //   position="bottom-end"
    //   color="green"
    //   withBorder
    //   sx={{ transform: "translateY(-50%)" }}
    // >
    <ActiveAvatar
      userId={id !== undefined ? id : activeUserId}
      sx={{ width: 80, height: 80, transform: "translateY(-50%)" }}
    />
    // </Indicator>
  );
};

export default AccountAvatar;
