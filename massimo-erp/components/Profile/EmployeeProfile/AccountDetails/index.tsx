import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { LinkIcon, PlusSmallIcon } from "@heroicons/react/24/outline";
import {
  Box,
  Divider,
  Group,
  Paper,
  SegmentedControl,
  Space,
  Stack,
  Text,
  Title,
  ThemeIcon,
  Button,
  ScrollArea,
  ActionIcon,
  useMantineTheme,
} from "@mantine/core";

import Banner from "./Banner";
import AccountAvatar from "./AccountAvatar";
import { useElementSize, useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import { RoleType } from "~utils/types/Roles";
import { useCallback, useEffect, useState } from "react";

interface PropType {
  shown?: UserType;
  activeTab: string;
  setActiveTab: (value: string) => void;
}

const AccountDetails = function (props: PropType) {
  const { shown, activeTab, setActiveTab } = props;

  const theme = useMantineTheme();
  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(max-width: 1300px)");
  const { t } = useTranslation();
  const [isFetching, setIsFetching] = useState(false);

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setPostModal } = useStore(
    "temp",
    (state) => ({
      setPostModal: state.setPostModal!,
    }),
    shallow
  );

  const {
    users,
    actionConnectUser,
    connections,
    connectionRequests,
    actionGetConnections,
    actionGetConnectionRequests,
  } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      actionConnectUser: state.actionConnectUser!,
      connections: state.connections!,
      connectionRequests: state.connectionRequests!,
      actionGetConnections: state.actionGetConnections!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeProfile = shown !== undefined ? shown : activeUser!;

  const sentRequest = connectionRequests.some((e) => {
    if (
      (e.target1Id === activeUserId &&
        e.isAccepted1 &&
        e.target2Id === activeProfile.id &&
        !e.isAccepted2) ||
      (e.target2Id === activeUserId &&
        e.isAccepted2 &&
        e.target1Id === activeProfile.id &&
        !e.isAccepted1)
    ) {
      return true;
    }

    return false;
  });

  const isConnected =
    activeProfile.id === activeUserId ||
    connections.some((e) =>
      e.target1Id === activeProfile.id
        ? e.target2Id === activeUserId
        : e.target2Id === activeProfile.id
        ? e.target1Id === activeUserId
        : false
    );

  const handleConnect = useCallback(async () => {
    if (activeProfile.id !== activeUserId) {
      setIsFetching(true);

      try {
        await actionConnectUser(activeProfile.id);
        await actionGetConnections(activeProfile.id);
        await actionGetConnectionRequests();
      } finally {
        setIsFetching(false);
      }
    }
  }, [
    actionConnectUser,
    actionGetConnectionRequests,
    actionGetConnections,
    activeProfile.id,
    activeUserId,
  ]);

  return (
    <Paper
      radius="md"
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        overflow: "hidden",
      })}
      ref={ref}
    >
      <Banner />

      <Box
        sx={(theme) => ({
          height: width <= 1000 ? "auto" : 80,
          padding: `0 ${width > 500 ? theme.spacing.xl * 2 : 0}px`,
          display: "flex",
          flexDirection: width <= 1000 ? "column" : "row",
          alignItems: "center",
          justifyContent: "space-between",
          paddingBottom: width <= 1000 ? theme.spacing.md : 0,
        })}
      >
        <Box
          sx={{
            display: "flex",
            flexDirection: width <= 1000 ? "column" : "row",
            alignItems: "center",
            gap: 16,
          }}
        >
          <Group>
            <AccountAvatar id={shown?.id} />
          </Group>
          <Box />
          <Box
            sx={{
              display: "flex",
              flexDirection: width <= 400 ? "column" : "row",
              alignItems: "center",
              gap: width <= 400 ? 4 : 16,
            }}
            mt={width <= 1000 ? -40 : 0}
            mb={width <= 1000 ? 20 : 0}
          >
            <Stack spacing={0}>
              <Title order={4} sx={{ whiteSpace: "nowrap" }}>
                {activeProfile.name} {activeProfile.surname}
              </Title>
              <Text color="dimmed" size="xs">
                {t(
                  `defaultRoles.${(
                    (roles.find(
                      (x) => x.id === activeProfile.defaultRoleId[0]
                    ) as RoleType) || {}
                  ).label?.toLowerCase()}`
                )}
              </Text>
            </Stack>
            <Divider orientation="vertical" my={width <= 1000 ? 4 : 6} />
            <Group spacing="md" noWrap>
              <ThemeIcon size="lg" variant="light" color="blue">
                <LinkIcon style={{ width: 16, height: 16 }} />
              </ThemeIcon>
              <Stack spacing={0}>
                <Text size="md" sx={{ lineHeight: 1 }}>
                  {
                    connections.filter((e) =>
                      [e.target1Id, e.target2Id].includes(activeProfile.id)
                    ).length
                  }
                </Text>
                <Text color="dimmed" size="xs">
                  {t("profile.connections")}
                </Text>
              </Stack>
            </Group>
            {shown?.id !== activeUserId && (
              <>
                <Divider orientation="vertical" my={width <= 1000 ? 4 : 6} />
                <Button
                  size="xs"
                  variant="light"
                  color={sentRequest ? "yellow" : isConnected ? "blue" : "gray"}
                  leftIcon={<LinkIcon style={{ width: 16, height: 16 }} />}
                  onClick={handleConnect}
                  loading={isFetching}
                  loaderProps={{
                    type: "bars",
                  }}
                >
                  {t(
                    sentRequest
                      ? "RequestSent"
                      : isConnected
                      ? "Connected"
                      : "Connect"
                  )}
                </Button>
              </>
            )}
          </Box>
        </Box>

        {width < 1000 && shown?.id === activeUserId && (
          <Button
            size="xs"
            variant="light"
            onClick={() =>
              setPostModal({
                modalType: "new",
                isOpen: true,
                type: "post",
              })
            }
            sx={{
              height: 31.69,
            }}
            mb="md"
          >
            {t("profile.newPost")}
          </Button>
        )}

        <Group>
          {width >= 1000 && shown?.id === activeUserId && (
            <Button
              size="xs"
              variant="light"
              onClick={() =>
                setPostModal({
                  modalType: "new",
                  isOpen: true,
                  type: "post",
                })
              }
              sx={{
                height: 31.69,
              }}
            >
              {t("profile.newPost")}
            </Button>
          )}

          <ScrollArea
            styles={{
              viewport: {
                maxWidth: width < 400 ? "calc(100vw - 64px)" : "auto",
                paddingBottom: 0,
              },
            }}
            offsetScrollbars
            scrollbarSize={5}
          >
            <SegmentedControl
              styles={{
                root: {
                  background: "transparent",
                },
              }}
              color="blue"
              value={activeTab}
              onChange={(value: string) => setActiveTab(value)}
              data={[
                ...(isConnected
                  ? [
                      {
                        label: t("profile.posts"),
                        value: "posts",
                      },
                    ]
                  : []),
                // { label: t("profile.Shared"), value: "shared" },
                // { label: t("profile.ToDo"), value: "todo" },
                // { label: t("profile.Completed"), value: "completed" },
                // { label: t("profile.Gallery"), value: "gallery" },
                ...(!shown?.isCustomer
                  ? [
                      {
                        label: t("Tasks"),
                        value: "tasks",
                      },
                    ]
                  : []),
              ]}
            />
          </ScrollArea>
        </Group>
      </Box>
    </Paper>
  );
};

export default AccountDetails;
