import {
  Avatar,
  Group,
  Paper,
  Space,
  Stack,
  Text,
  Title,
  ActionIcon,
  ThemeIcon,
  useMantineTheme,
  Anchor,
  Tooltip,
} from "@mantine/core";
import {
  LinkIcon,
  PaperAirplaneIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { CustomerType } from "~utils/types/Customer";
import { useRouter } from "next/router";
import { orderBy } from "lodash";

const OrganizationsList: CC<{
  userId: number;
}> = function ({ userId }) {
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { subscribedChats } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();

  const activeUser = users.find((e) => e.id === userId);
  const organizationsList = customers.filter((e) =>
    activeUser?.organizationIds.includes(e.id)
  );

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Text size="sm" color="dimmed" weight={600}>
        {t("organizationsList")}
      </Text>
      <Space h="md" />
      <Stack>
        {organizationsList.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" my="xs" align="center">
            {t("profile.empty")}
          </Text>
        )}
        {orderBy(organizationsList, "id").map(
          (organization: CustomerType, i: number) => {
            const chatId = subscribedChats.find(
              (e) =>
                e.type === "organization" &&
                e.organizationId === organization.id
            )?.id;

            return (
              <Group
                key={"organization-" + organization.id}
                sx={{
                  position: "relative",
                }}
              >
                <ThemeIcon size={38} variant="light" color="blue" radius="xl">
                  <UserGroupIcon style={{ width: 20, height: 20 }} />
                </ThemeIcon>
                <Stack
                  spacing={0}
                  sx={{ width: "calc(100% - 92px)", position: "relative" }}
                >
                  <Tooltip label={`${organization.fullName}`}>
                    <Anchor
                      size={"sm"}
                      sx={(theme) => ({
                        color: theme.colors.gray[4],
                        maxWidth: "100%",
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                      })}
                      onClick={() => {
                        push(`/profile/organization/${organization.id}`);
                      }}
                    >
                      {organization.fullName}
                    </Anchor>
                  </Tooltip>
                  <Text
                    size="xs"
                    color="dimmed"
                    sx={(theme) => ({
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    })}
                  >
                    {organization.shortName}
                  </Text>
                </Stack>

                {!!chatId && (
                  <ActionIcon
                    variant="transparent"
                    size="sm"
                    onClick={() => push(`/apps/chat/${chatId}`)}
                  >
                    <PaperAirplaneIcon
                      style={{
                        width: 18,
                        height: 18,
                      }}
                    />
                  </ActionIcon>
                )}
              </Group>
            );
          }
        )}
      </Stack>
    </Paper>
  );
};

export default OrganizationsList;
