import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Image,
  SimpleGrid,
  Stack,
  Text,
  Group,
  Avatar,
  Menu,
  ActionIcon,
  Anchor,
  Divider,
  Textarea,
  Button,
  ScrollArea,
} from "@mantine/core";
import { formatDate } from "~utils/tools";
import {
  EllipsisVerticalIcon,
  HeartIcon,
  PaperAirplaneIcon,
  PaperClipIcon,
} from "@heroicons/react/24/outline";
import Link from "next/link";
import { useMediaQuery } from "@mantine/hooks";

export default function GalleryPostViewModal() {
  const { galleryPostViewModal, setGalleryPostViewModal } = useStore(
    "temp",
    (state) => ({
      galleryPostViewModal: state.galleryPostViewModal!,
      setGalleryPostViewModal: state.setGalleryPostViewModal!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 992px)");

  return (
    <Modal
      opened={galleryPostViewModal.isOpen}
      onClose={() =>
        setGalleryPostViewModal({
          isOpen: false,
          data: undefined,
        })
      }
      withCloseButton={false}
      centered
      transition={galleryPostViewModal.isOpen ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
      size={1200}
    >
      <SimpleGrid
        cols={matches ? 1 : 2}
        spacing="lg"
        sx={{ height: matches ? "auto" : "calc(100vh - 136px)" }}
      >
        <Stack
          sx={{
            height: "100%",
          }}
        >
          <Image
            src="https://images.unsplash.com/photo-1527118732049-c88155f2107c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80"
            alt="Panda"
            height={500}
            fit="contain"
            withPlaceholder
          />
        </Stack>
        <Stack spacing="sm" justify="space-between">
          <Group position="apart">
            <Group>
              <Avatar />
              <Link href="/profile">
                <Text
                  size="sm"
                  weight={600}
                  sx={(theme) => ({
                    color:
                      theme.colorScheme === "dark" ? theme.white : theme.black,
                  })}
                >
                  John Doe
                </Text>
              </Link>
              <Anchor size="xs">Connect</Anchor>
            </Group>

            <Menu
              width={150}
              shadow="md"
              position="bottom-end"
              transition="fade"
            >
              <Menu.Target>
                <ActionIcon>
                  <EllipsisVerticalIcon />
                </ActionIcon>
              </Menu.Target>
              <Menu.Dropdown>
                <Menu.Item>Report</Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Group>
          <Divider />
          <ScrollArea>
            <Stack sx={{ maxHeight: matches ? "auto" : "calc(100vh - 390px)" }}>
              {Array(50)
                .fill(1)
                .map((a, i) => (
                  <Stack key={i} spacing={8} align="flex-start">
                    <Group
                      sx={{ width: "100%" }}
                      position="apart"
                      align="center"
                    >
                      <Group>
                        <Avatar src="" size={32} />
                        <Group spacing={8}>
                          <Link href="/profile">
                            <Text
                              size="sm"
                              weight={600}
                              sx={(theme) => ({
                                color:
                                  theme.colorScheme === "dark"
                                    ? theme.white
                                    : theme.black,
                              })}
                            >
                              John Doe
                            </Text>
                          </Link>
                          <Text size="xs" color="dimmed">
                            {formatDate(new Date())}
                          </Text>
                        </Group>
                      </Group>
                      <Menu
                        width={150}
                        shadow="md"
                        position="bottom-end"
                        transition="fade"
                      >
                        <Menu.Target>
                          <ActionIcon>
                            <EllipsisVerticalIcon />
                          </ActionIcon>
                        </Menu.Target>
                        <Menu.Dropdown>
                          <Menu.Item>Report</Menu.Item>
                        </Menu.Dropdown>
                      </Menu>
                    </Group>

                    <Text> test </Text>

                    {/* <Group>
                    {comment.files.map((file: any, i: number) => (
                      <Paper
                        key={"commentFile-" + i}
                        sx={(theme) => ({
                          background:
                            theme.colorScheme === "dark"
                              ? theme.colors.dark[7]
                              : theme.colors.gray[3],
                          padding: 8,
                        })}
                      >
                        <Group>
                          <ThemeIcon color="gray">
                            <DocumentIcon style={{ width: 16, height: 16 }} />
                          </ThemeIcon>
                          <Stack spacing={0}>
                            <Text size="sm">{file.name}</Text>
                          </Stack>
                          <ActionIcon color="gray">
                            <ArrowDownTrayIcon style={{ width: 16, height: 16 }} />
                          </ActionIcon>
                        </Group>
                      </Paper>
                    ))}
                  </Group> */}
                  </Stack>
                ))}
            </Stack>
          </ScrollArea>
          <Stack
            sx={(theme) =>
              matches
                ? {
                    background:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[7]
                        : theme.colors.gray[2],
                    position: "sticky",
                    bottom: -48,
                  }
                : {}
            }
          >
            <Divider />
            <Stack spacing={4}>
              <Group spacing={4}>
                <ActionIcon
                  size="sm"
                  variant="transparent"
                  color="pink"
                  onClick={() => {}}
                >
                  <HeartIcon style={{ width: 20, height: 20 }} />
                </ActionIcon>
                <Text>1 Likes</Text>
              </Group>
              <Text size="xs" weight={600} color="dimmed">
                {formatDate(new Date())}
              </Text>
            </Stack>
            <Textarea />
            <Group position="apart">
              <Group>
                <Button
                  compact
                  variant="light"
                  color="gray"
                  leftIcon={<PaperClipIcon style={{ width: 16, height: 16 }} />}
                >
                  <Text color="dimmed">Attachment</Text>
                </Button>
              </Group>

              <Button
                compact
                variant="light"
                color="blue"
                onClick={() => {}}
                leftIcon={
                  <PaperAirplaneIcon style={{ width: 16, height: 16 }} />
                }
              >
                Send
              </Button>
            </Group>
          </Stack>
        </Stack>
      </SimpleGrid>
    </Modal>
  );
}
