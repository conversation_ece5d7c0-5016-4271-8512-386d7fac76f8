import { Paper, Text, Space, Group, Badge, Tooltip } from "@mantine/core";
import { orderBy } from "lodash";
import { useTranslation } from "next-i18next";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { DepartmentType } from "~utils/types/Department";

const DepartmentList: CC<{
  userId: number;
}> = ({ userId }) => {
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );

  const subscribedDepartments: DepartmentType[] = departments.filter((e) =>
    e.userIds.includes(userId)
  );

  const { t } = useTranslation();

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Text size="sm" color="dimmed" weight={600}>
        {t("departmentList")}
      </Text>
      <Space h="md" />
      <Group spacing={8}>
        {subscribedDepartments.length === 0 && (
          <Text
            sx={{ width: "100%" }}
            size="sm"
            weight={600}
            color="dimmed"
            my="xs"
            align="center"
          >
            {t("profile.empty")}
          </Text>
        )}
        {orderBy(subscribedDepartments, "id").map(
          (department: DepartmentType, i: number) => {
            return (
              <Tooltip
                key={`department-${department.id}`}
                label={department.label}
              >
                <Badge
                  variant="light"
                  styles={{
                    root: {
                      background: hexToRGBA(department.color, 0.1),
                      color: department.color,
                    },
                  }}
                >
                  {department.label}
                </Badge>
              </Tooltip>
            );
          }
        )}
      </Group>
    </Paper>
  );
};

export default DepartmentList;
