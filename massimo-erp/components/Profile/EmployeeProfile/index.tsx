import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

import {
  Box,
  Space,
  Stack,
  Tabs,
  Grid,
  Text,
  Col,
  Accordion,
  SimpleGrid,
} from "@mantine/core";
import { useState, useEffect, useCallback } from "react";
import AccountDetails from "./AccountDetails";
import EmployeeList from "./EmployeeList";
import Post from "../../Post";
import TodoTask from "./TodoTask";
import CompletedTask from "./CompletedTask";
import { TaskType } from "~utils/types/Task";
import { useElementSize, useMediaQuery } from "@mantine/hooks";
import GalleryPostViewModal from "./GalleryPostViewModal";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { UserType } from "~utils/types/User";
import OrganizationsList from "./OrganizationsList";
import TasksTab from "./TasksTab";
import DepartmentList from "./DepartmentList";
import Stories from "~components/Stories";
import { PostType } from "~utils/types/Post";
import PostArea from "../PostArea";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { isNumber, isUndefined } from "lodash";

const EmployeeProfile: CC<{
  id?: number;
}> = function ({ id }) {
  const { setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );
  const { users, connections } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      connections: state.connections!,
    }),
    shallow
  );
  const { actionGetUserReport } = useStore(
    "analytics",
    (state) => ({
      actionGetUserReport: state.actionGetUserReport!,
    }),
    shallow
  );
  const { selectedProjectId, activeUserId } = useStore(
    "global",
    (state) => ({
      selectedProjectId: state.selectedProjectId!,
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const {
    posts,
    stories,
    actionGetPosts,
    resetPosts,
    resetStories,
    actionGetFeed,
  } = useStore(
    "posts",
    (state) => ({
      posts: state.posts!,
      stories: state.stories!,
      actionGetPosts: state.actionGetPosts!,
      resetPosts: state.resetPosts!,
      resetStories: state.resetStories!,
      actionGetFeed: state.actionGetFeed!,
    }),
    shallow
  );

  const { ref, width } = useElementSize();
  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 992px)");

  const shownUser = users.find((e: UserType) => e.id === +(id || -1));

  const isConnected =
    shownUser?.id === activeUserId ||
    connections.some((e) =>
      e.target1Id === shownUser?.id
        ? e.target2Id === activeUserId
        : e.target2Id === shownUser?.id
        ? e.target1Id === activeUserId
        : false
    );

  const [activeTab, setActiveTab] = useState(isConnected ? "posts" : "tasks");

  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState(true);

  const getPosts = useCallback(
    async (p?: number) => {
      try {
        const hasMoreData = await actionGetPosts(
          id as number,
          isNumber(p) ? p : page
        );
        setHasMore(hasMoreData);
      } finally {
        setPage((p) => p + 1);
      }
    },
    [actionGetPosts, id, page]
  );

  const handleReset = useCallback(async () => {
    await resetPosts();
    setPage(0);
    setHasMore(true);

    getPosts(0);
  }, [getPosts, resetPosts]);

  useEffect(() => {
    handleReset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, activeTab]);

  const getTimeline = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(0);

    try {
      await actionGetUserReport(id);
    } finally {
      setIsLoading(false);
    }
  }, [actionGetUserReport, id, setIsLoading, setLoadingLevel]);

  useEffect(() => {
    getTimeline();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedProjectId]);

  useEffect(() => {
    setActiveTab(isConnected ? "posts" : "tasks");
  }, [isConnected]);

  return (
    <CustomInfiniteScroll
      dataLength={posts.length}
      hasMore={activeTab === "posts" ? hasMore : false}
      next={getPosts}
      height="calc(100vh - 70px)"
    >
      <Stack
        align="stretch"
        ref={ref}
        sx={{
          opacity: width === 0 ? 0 : 1,
          transition: "opacity .2s ease",
          position: "relative",
        }}
        p={matches ? 16 : 32}
      >
        {/* <GalleryPostViewModal /> */}
        <Tabs value={activeTab}>
          <AccountDetails
            shown={shownUser}
            activeTab={activeTab}
            setActiveTab={(value: string) => setActiveTab(value)}
          />

          <Space h="md" />

          <Grid gutter="md">
            {activeTab !== "gallery" && (
              <Col span={width <= 1280 ? 12 : 3} sx={{ order: 0 }}>
                <Stack>
                  <EmployeeList userId={id || -1} />
                  <DepartmentList userId={id || -1} />
                </Stack>
              </Col>
            )}

            <Col
              span={width <= 1280 ? 12 : 6}
              sx={{
                order: width <= 1280 ? 2 : 1,
                position: "relative",
                display: "flex",
              }}
            >
              <Tabs.Panel value="posts" sx={{ flexGrow: 1 }}>
                <Stories userId={shownUser?.id} group={false} />
                <SimpleGrid
                  cols={width > 1150 ? 3 : width > 700 ? 3 : width > 450 ? 2 : 1}
                  spacing="md"
                  verticalSpacing={32}
                >
                  {posts.map((post: PostType, i: number) => {
                    return <Post key={`post-${i}`} post={post} />;
                  })}
                </SimpleGrid>
              </Tabs.Panel>

              {!shownUser?.isCustomer && (
                <Tabs.Panel value="tasks" sx={{ flexGrow: 1 }}>
                  <TasksTab userId={id || -1} />
                </Tabs.Panel>
              )}
              {/* <Tabs.Panel value="shared" sx={{ flexGrow: 1 }}>
              {posts.length > 0 ? (
                <Stack sx={{ flexDirection: "column-reverse" }}>
                  {posts.map((post: any, i: number) => (
                    <Post key={"post-" + i} post={post} />
                  ))}
                </Stack>
              ) : (
                <Text
                  color="dimmed"
                  size="md"
                  weight={600}
                  align="center"
                  mt="md"
                >
                  {t("profile.empty")}
                </Text>
              )}
            </Tabs.Panel> */}

              {/* <Tabs.Panel value="todo" sx={{ flexGrow: 1 }}>
              <Stack
                sx={{ width: "100%" }}
                align="stretch"
                justify="flex-start"
              >
                {tasks.map((task: TaskType, i: number) => (
                  <TodoTask key={"todoTask-" + i} task={task} />
                ))}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="completed" sx={{ flexGrow: 1 }}>
              <Stack
                sx={{ width: "100%" }}
                align="stretch"
                justify="flex-start"
              >
                {tasks.map((task: TaskType, i: number) => (
                  <CompletedTask key={"todoTask-" + i} task={task} />
                ))}
              </Stack>
            </Tabs.Panel> */}

              {/* <Tabs.Panel value="gallery" sx={{ flexGrow: 1 }}>
              <Grid>
                <Grid.Col span={matches ? 4 : 3}>
                  <AspectRatio ratio={1 / 1} mx="auto">
                    <Box
                      sx={{
                        cursor: "pointer",
                        transition: "filter .2s ease",
                        userSelect: "none",
                        borderRadius: 8,
                        ":hover": {
                          filter: "brightness(.8)",
                        },
                      }}
                      onClick={() => {}}
                    >
                      <Image
                        src="https://images.unsplash.com/photo-1527118732049-c88155f2107c?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=720&q=80"
                        alt="Panda"
                      />
                    </Box>
                  </AspectRatio>
                </Grid.Col>
              </Grid>
            </Tabs.Panel> */}
            </Col>

            {activeTab !== "gallery" && (
              <Col
                span={width <= 1280 ? 12 : 3}
                sx={{ order: width <= 1280 ? 1 : 2 }}
              >
                <OrganizationsList userId={id || -1} />
              </Col>
            )}
          </Grid>
        </Tabs>
      </Stack>
    </CustomInfiniteScroll>
  );
};

export default EmployeeProfile;
