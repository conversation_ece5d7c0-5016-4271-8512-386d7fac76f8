import shallow from "zustand/shallow";
import { calculateProgress, formatDate } from "~utils/tools";
import { useStore } from "~utils/store";
import { TaskType } from "~utils/types/Task";

import {
  Box,
  Group,
  Paper,
  Space,
  Text,
  Title,
  Progress,
  Tooltip,
} from "@mantine/core";

interface PropType {
  task: TaskType;
}

const TodoTask = function (props: PropType) {
  const { task } = props;
  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { setIsOpenTaskViewModal, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Group align="center">
        <Tooltip label={"Priority: " + task.priority}>
          <Box
            sx={(theme) => ({
              width: 10,
              height: 10,
              borderRadius: "50%",
              background: theme.colors[priorityColors[task.priority]][9],
            })}
          />
        </Tooltip>
        <Title
          order={5}
          sx={(theme) => ({
            cursor: "pointer",
            transition: "color .2s ease",
            ":hover": { color: theme.colors.gray[6] },
            lineHeight: 1,
          })}
          onClick={() => {
            setTaskViewModalData(task);
            setIsOpenTaskViewModal(true);
          }}
        >
          {task.title}
        </Title>
        <Text color="dimmed" size="xs" weight={600} sx={{ lineHeight: 1 }}>
          {formatDate(task.start)} - {formatDate(task.end)}
        </Text>
      </Group>
      {/* {task.contents.filter((e) => e.type === "sub-tasks").length > 0 && (
        <>
          <Space h="sm" />
          <Group sx={{ flexWrap: "nowrap" }} align="center">
            <Text color="dimmed" size="sm">
              {calculateProgress(task.contents)}%
            </Text>
            <Progress
              size="sm"
              value={calculateProgress(task.contents)}
              sx={{ width: "100%" }}
            />
          </Group>
        </>
      )} */}
    </Paper>
  );
};

export default TodoTask;
