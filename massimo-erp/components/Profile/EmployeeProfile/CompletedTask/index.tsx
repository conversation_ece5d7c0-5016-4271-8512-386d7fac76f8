import shallow from "zustand/shallow";
import { calculateProgress, formatDate } from "~utils/tools";
import { useStore } from "~utils/store";
import { TaskType } from "~utils/types/Task";

import {
  Box,
  Group,
  Paper,
  Text,
  Title,
  Menu,
  Tooltip,
  ActionIcon,
} from "@mantine/core";
import { ShareIcon } from "@heroicons/react/24/outline";
import { useCallback } from "react";

interface PropType {
  task: TaskType;
}

const CompletedTask = function (props: PropType) {
  const { task } = props;
  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { setIsOpenTaskViewModal, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );

  // const shareTask = useCallback(() => {
  //   setSharePostModalData({ id: posts.length + 1, taskId: [task.id] });
  //   setIsOpenSharePostModal(true);
  // }, [setIsOpenSharePostModal, task, posts, setSharePostModalData]);

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Group position="apart" align="center">
        <Group align="center">
          <Tooltip label={"Priority: " + task.priority}>
            <Box
              sx={(theme) => ({
                width: 10,
                height: 10,
                borderRadius: "50%",
                background: theme.colors[priorityColors[task.priority]][9],
              })}
            />
          </Tooltip>
          <Title
            order={5}
            sx={(theme) => ({
              cursor: "pointer",
              transition: "color .2s ease",
              ":hover": { color: theme.colors.gray[6] },
            })}
            onClick={() => {
              setTaskViewModalData(task);
              setIsOpenTaskViewModal(true);
            }}
          >
            {task.title}
          </Title>
          <Text color="dimmed" size="xs" weight={600}>
            {formatDate(task.start)} - {formatDate(task.end)}
          </Text>
        </Group>

        <Menu width={150} shadow="md" transition="fade" position="bottom-end">
          <Menu.Target>
            <ActionIcon>
              <ShareIcon style={{ width: 20, height: 20 }} />
            </ActionIcon>
          </Menu.Target>

          <Menu.Dropdown>
            {/* <Menu.Item onClick={() => shareTask()}>Share Post</Menu.Item> */}
            <Menu.Item>Send Message</Menu.Item>
          </Menu.Dropdown>
        </Menu>
      </Group>
    </Paper>
  );
};

export default CompletedTask;
