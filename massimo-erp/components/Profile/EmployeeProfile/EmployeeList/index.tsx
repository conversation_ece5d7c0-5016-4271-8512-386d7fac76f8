import {
  Avatar,
  Group,
  Paper,
  Space,
  Stack,
  Text,
  Title,
  ActionIcon,
  useMantineTheme,
  Anchor,
  Divider,
  Toolt<PERSON>,
  Loader,
} from "@mantine/core";
import {
  CheckIcon,
  LinkIcon,
  PaperAirplaneIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { ConnectionType, UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import { useRouter } from "next/router";
import { useCallback, useEffect, useState } from "react";
import { isNumber, orderBy, uniqBy } from "lodash";

const EmployeeList: CC<{
  userId: number;
}> = function ({ userId }) {
  const {
    users,
    actionConnectUser,
    connections,
    connectionRequests,
    actionGetConnections,
    actionGetConnectionRequests,
    actionRejectUser,
  } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      actionConnectUser: state.actionConnectUser!,
      connections: state.connections!,
      connectionRequests: state.connectionRequests!,
      actionGetConnections: state.actionGetConnections!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
      actionRejectUser: state.actionRejectUser!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { subscribedChats } = useStore(
    "chat",
    (state) => ({
      subscribedChats: state.subscribedChats!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { notifications } = useStore(
    "notifications",
    (state) => ({
      notifications: state.notifications!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();

  const [fetchConnections, setFetchConnections] = useState(true);

  const [fetchingUsers, setFetchingUsers] = useState<number[]>([]);

  const activeProfile = users.find((e) => e.id === userId)!;

  const activeUser = users.find((e) => e.id === activeUserId);
  // const isConnected = activeUser?.connectionIds.includes(activeProfile.id);,

  const getConnections = useCallback(async () => {
    setFetchConnections(true);

    try {
      await actionGetConnections(userId);
      await actionGetConnectionRequests();
    } finally {
      setFetchConnections(false);
    }
  }, [actionGetConnectionRequests, actionGetConnections, userId]);

  const handleConnect = useCallback(
    async (targetId: number) => {
      setFetchingUsers((users) => [...users, targetId]);
      setFetchConnections(true);

      try {
        await actionConnectUser(targetId);
      } finally {
        setFetchingUsers((users) => users.filter((e) => e !== targetId));
        getConnections();
      }
    },
    [actionConnectUser, getConnections]
  );

  const handleReject = useCallback(
    async (targetId: number) => {
      setFetchingUsers((users) => [...users, targetId]);
      setFetchConnections(true);

      try {
        await actionRejectUser(targetId);
      } finally {
        setFetchingUsers((users) => users.filter((e) => e !== targetId));
        getConnections();
      }
    },
    [actionRejectUser, getConnections]
  );

  useEffect(() => {
    getConnections();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const ownConnections = connections.filter((e) =>
    [e.target1Id, e.target2Id].includes(userId)
  );

  const commingRequests = connectionRequests.filter((c) => {
    if (
      (c.isAccepted2 && c.target1Id === activeUserId) ||
      (c.isAccepted1 && c.target2Id === activeUserId)
    ) {
      return true;
    }

    return false;
  });

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Text size="sm" color="dimmed" weight={600}>
        {t("connectionsList")}
      </Text>
      {fetchConnections ? (
        <Group position="center" mt="sm">
          <Loader size="sm" variant="bars" />
        </Group>
      ) : (
        <>
          <Space h="md" />
          <Stack>
            {ownConnections.length === 0 && (
              <Text
                size="sm"
                weight={600}
                color="dimmed"
                align="center"
                my="xs"
              >
                {t("profile.empty")}
              </Text>
            )}
            {orderBy(ownConnections, "id").map(
              (connection: ConnectionType, i: number) => {
                const targetUser = users.find(
                  (e) =>
                    e.id ===
                    (connection.target1Id === userId
                      ? connection.target2Id
                      : connection.target1Id)
                );

                const isConnected = connections.some((e) =>
                  e.target1Id === targetUser?.id
                    ? e.target2Id === activeUserId
                    : e.target2Id === targetUser?.id
                    ? e.target1Id === activeUserId
                    : false
                );
                const isRequested = connectionRequests.some((e) => {
                  if (
                    (e.target1Id === targetUser?.id && e.isAccepted2) ||
                    (e.target2Id === targetUser?.id && e.isAccepted1)
                  ) {
                    return true;
                  }

                  return false;
                });

                const chatId = subscribedChats.find(
                  (e) =>
                    e.type === "person" &&
                    [e.personId, e.ownerId].includes(targetUser?.id)
                )?.id;

                const defaultRoleLabel = roles.find(
                  (e) => e.id === targetUser?.defaultRoleId[0]
                )?.label;

                return (
                  <Group
                    key={"connection-" + connection?.id}
                    noWrap
                    position="apart"
                    spacing={0}
                    sx={{ position: "relative", width: "100%" }}
                  >
                    <Group
                      noWrap
                      sx={{
                        position: "relative",
                        width: "calc(100% - 140px)",
                      }}
                    >
                      <ActiveAvatar userId={targetUser?.id} />
                      <Stack
                        align="flex-start"
                        spacing={0}
                        sx={{
                          width: "100%",
                        }}
                      >
                        <Tooltip
                          label={`${targetUser?.name} ${targetUser?.surname}`}
                        >
                          <Anchor
                            size={"sm"}
                            sx={(theme) => ({
                              color: theme.colors.gray[4],
                              maxWidth: "100%",
                              whiteSpace: "nowrap",
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                            })}
                            onClick={() => {
                              push(`/profile/user/${targetUser?.id}`);
                            }}
                          >
                            {targetUser?.name} {targetUser?.surname}
                          </Anchor>
                        </Tooltip>
                        {defaultRoleLabel && (
                          <Text size="xs" color="dimmed">
                            {t(
                              `defaultRoles.${defaultRoleLabel.toLowerCase()}`
                            )}
                          </Text>
                        )}
                      </Stack>
                    </Group>

                    <Group noWrap>
                      {targetUser?.id && targetUser?.id !== activeUser?.id && (
                        <ActionIcon
                          variant="transparent"
                          size="sm"
                          onClick={() => {
                            handleConnect(targetUser?.id);
                          }}
                          loading={fetchingUsers.includes(targetUser?.id)}
                        >
                          <LinkIcon
                            style={{
                              width: 18,
                              height: 18,
                              color: isRequested
                                ? theme.colors.yellow[7]
                                : isConnected
                                ? theme.colors.blue[7]
                                : theme.colors.dark[3],
                            }}
                          />
                        </ActionIcon>
                      )}
                      {!!chatId && targetUser?.id !== activeUserId && (
                        <ActionIcon
                          variant="transparent"
                          size="sm"
                          onClick={() => push(`/apps/chat/${chatId}`)}
                        >
                          <PaperAirplaneIcon
                            style={{
                              width: 18,
                              height: 18,
                            }}
                          />
                        </ActionIcon>
                      )}
                    </Group>
                  </Group>
                );
              }
            )}
          </Stack>
          {commingRequests.length > 0 && activeUserId === userId && (
            <>
              <Space h="md" />
              <Text size="sm" color="dimmed" weight={600}>
                {t("connectionRequests")}
              </Text>
              <Space h="md" />
              <Stack>
                {orderBy(commingRequests, "id").map(
                  (connection: ConnectionType, i: number) => {
                    const targetUser = users.find(
                      (e) =>
                        e.id ===
                        [connection.target1Id, connection.target2Id].filter(
                          (e) => e !== activeUserId
                        )[0]
                    );

                    if (!targetUser) {
                      return null;
                    }

                    const chatId = subscribedChats.find(
                      (e) =>
                        e.type === "person" &&
                        [e.personId, e.ownerId].includes(targetUser?.id)
                    )?.id;
                    const defaultRoleLabel = roles.find(
                      (e) => e.id === targetUser?.defaultRoleId[0]
                    )?.label;

                    return (
                      <Group
                        key={"connection-" + connection?.id}
                        sx={{ position: "relative" }}
                      >
                        <ActiveAvatar userId={targetUser?.id} />
                        <Stack
                          align="flex-start"
                          spacing={0}
                          sx={{
                            width: "calc(100% - 118px)",
                            position: "relative",
                          }}
                        >
                          <Tooltip
                            label={`${targetUser?.name} ${targetUser?.surname}`}
                          >
                            <Anchor
                              size={"sm"}
                              sx={(theme) => ({
                                color: theme.colors.gray[4],
                                maxWidth: "100%",
                                whiteSpace: "nowrap",
                                textOverflow: "ellipsis",
                                overflow: "hidden",
                              })}
                              onClick={() => {
                                push(`/profile/user/${targetUser?.id}`);
                              }}
                            >
                              {targetUser?.name} {targetUser?.surname}
                            </Anchor>
                          </Tooltip>
                          {defaultRoleLabel && (
                            <Text size="xs" color="dimmed">
                              {t(
                                `defaultRoles.${defaultRoleLabel.toLowerCase()}`
                              )}
                            </Text>
                          )}
                        </Stack>

                        <Group spacing={4}>
                          <ActionIcon
                            size="sm"
                            color="red"
                            onClick={() => handleReject(targetUser?.id)}
                          >
                            <XMarkIcon width={16} height={16} />
                          </ActionIcon>
                          <ActionIcon
                            size="sm"
                            color="green"
                            onClick={() => handleConnect(targetUser?.id)}
                          >
                            <CheckIcon width={16} height={16} />
                          </ActionIcon>
                        </Group>
                      </Group>
                    );
                  }
                )}
              </Stack>
            </>
          )}
        </>
      )}
    </Paper>
  );
};

export default EmployeeList;
