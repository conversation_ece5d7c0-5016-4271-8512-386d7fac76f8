import {
  Accordion,
  Stack,
  Group,
  Text,
  Badge,
  <PERSON>chor,
  <PERSON><PERSON><PERSON>,
  MantineTheme,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useCallback, useEffect, useState } from "react";
import Masonry from "react-masonry-css";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { formatDate, formatTime } from "~utils/tools";
import { TaskType } from "~utils/types/Task";

interface TimelineType {
  name: string;
  color: string;
  tasks: TaskType[];
}

const TasksTab: CC<{
  userId: number;
}> = function ({ userId }) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { tasks, taskStatusList, statusColors, priorityColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      taskStatusList: state.taskStatusList!,
      statusColors: state.statusColors!,
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const {
    setIsOpenTaskViewModal,
    setTaskViewModalData,
    setProjectModal,
    setLastOffer,
  } = useStore(
    "temp",
    (state) => ({
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setTaskViewModalData: state.setTaskViewModalData!,
      setProjectModal: state.setProjectModal!,
      setLastOffer: state.setLastOffer!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { profileTaskTimeline } = useStore(
    "analytics",
    (state) => ({
      profileTaskTimeline: state.profileTaskTimeline!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const [openedAccordion, setOpenedAccordion] = useState<string>(
    t(`profile.tasks.${profileTaskTimeline[0].name}`)
  );

  const openTaskModal = useCallback(
    (data: TaskType) => {
      setIsOpenTaskViewModal(true);
      setTaskViewModalData(data);
    },
    [setIsOpenTaskViewModal, setTaskViewModalData]
  );

  return (
    <Accordion
      variant="separated"
      value={openedAccordion}
      onChange={(value: string) => setOpenedAccordion(value)}
    >
      <Masonry
        breakpointCols={{
          default: 2,
          1200: 1,
        }}
        className="my-masonry-grid"
        columnClassName="my-masonry-grid_column"
      >
        {profileTaskTimeline.map((timelineItem: TimelineType, i: number) => {
          const timelineName = t(`profile.tasks.${timelineItem.name}`);

          return (
            <Accordion.Item
              key={"timeline-" + i}
              value={timelineName}
              sx={(theme: MantineTheme) => ({
                button: {
                  width: "100%",
                  backgroundColor:
                    theme.colorScheme === "dark"
                      ? theme.colors[timelineItem.color][8]
                      : theme.colors[timelineItem.color][6],
                  color: theme.colors.gray[0],
                  borderRadius: theme.radius.md,
                  borderBottomLeftRadius:
                    openedAccordion === timelineName ? "0" : theme.radius.md,
                  borderBottomRightRadius:
                    openedAccordion === timelineName ? "0" : theme.radius.md,
                  transition: "all .2s ease",
                  ":hover": {
                    backgroundColor:
                      theme.colorScheme === "dark"
                        ? theme.colors[timelineItem.color][9]
                        : theme.colors[timelineItem.color][7],
                  },
                },
                border: "none",
              })}
            >
              <Accordion.Control>{timelineName}</Accordion.Control>
              <Accordion.Panel
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[2],
                  borderBottomLeftRadius: theme.radius.md,
                  borderBottomRightRadius: theme.radius.md,
                })}
              >
                {timelineItem.tasks.length === 0 && (
                  <Text
                    size="sm"
                    weight={600}
                    color="dimmed"
                    align="center"
                    mt="sm"
                  >
                    {t("profile.empty")}
                  </Text>
                )}
                {timelineItem.tasks.map((task: TaskType, i: number) => {
                  const project = projects.find((e) => e.id === task.projectId);
                  const lastOffer = offers.find(
                    (e) => e.id === project?.offerIds.at(-1)
                  );
                  const request = requests.find(
                    (e) => e.id === project?.requestId
                  );
                  const customer = customers.find(
                    (e) => e.id === request?.customerId
                  );

                  return (
                    <Stack key={`task-${task.id}`}>
                      <Stack spacing={8} mt={4}>
                        <Group>
                          <Text
                            size="md"
                            weight={600}
                            sx={{
                              cursor: "pointer",
                              transition: "filter .2s ease",
                              ":hover": {
                                filter: "brightness(.9)",
                              },
                            }}
                            onClick={() => openTaskModal(task)}
                          >
                            {task.title}
                          </Text>
                        </Group>
                        <Group align="baseline" spacing="xs">
                          <Badge size="sm" color={statusColors[task.status]}>
                            {task.status}
                          </Badge>
                          <Group spacing={8}>
                            <Text size="xs" weight={600} color="dimmed">
                              {t("profile.tasks.project")}
                            </Text>
                            <Anchor
                              size="xs"
                              weight={600}
                              onClick={() => {
                                setLastOffer(lastOffer!);
                                setProjectModal({
                                  isOpen: true,
                                  type: "edit",
                                  isOwner:
                                    customer?.personIds.includes(activeUserId),
                                  data: project,
                                });
                              }}
                            >
                              {lastOffer?.name}
                            </Anchor>
                          </Group>
                          <Group spacing={8}>
                            <Text size="xs" weight={600} color="dimmed">
                              {t("profile.tasks.endDate")}
                            </Text>
                            <Text size="xs" weight={600}>
                              {formatDate(task.end)} {formatTime(task.end)}
                            </Text>
                          </Group>
                        </Group>
                      </Stack>
                      {i !== timelineItem.tasks.length - 1 && (
                        <Divider mt="md" mb="sm" />
                      )}
                    </Stack>
                  );
                })}
              </Accordion.Panel>
            </Accordion.Item>
          );
        })}
      </Masonry>
    </Accordion>
  );
};

export default TasksTab;
