import React, { useCallback, useEffect, useRef, useState } from "react";
import InfiniteScroll from "~components/SharedComponents/InfiniteScroll";

import { useStore } from "~utils/store";
import { PostType } from "~utils/types/Post";
import { CC } from "~types";

import { Box, Button, Group, Loader, SimpleGrid, Stack } from "@mantine/core";
import Post from "~components/Post";
import { useIntersection, useViewportSize } from "@mantine/hooks";
import { ArrowPathIcon } from "@heroicons/react/24/outline";
import Masonry from "react-masonry-css";
import shallow from "zustand/shallow";
import { useRouter } from "next/router";

const PostArea: CC<{
  userId?: number;
  organizationId?: number;
  withMasonry?: boolean;
  feed?: boolean;
}> = ({ userId, organizationId, withMasonry = false, feed = false }) => {
  const { posts, actionGetPosts, resetPosts, resetStories, actionGetFeed } =
    useStore(
      "posts",
      (state) => ({
        posts: state.posts!,
        actionGetPosts: state.actionGetPosts!,
        resetPosts: state.resetPosts!,
        resetStories: state.resetStories!,
        actionGetFeed: state.actionGetFeed!,
      }),
      shallow
    );

  const router = useRouter();

  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState(true);

  const getPosts = useCallback(async () => {
    try {
      let hasMoreData = true;

      if (feed) {
        hasMoreData = await actionGetFeed(page);
      } else {
        hasMoreData = await actionGetPosts(userId as number, page);
      }

      setHasMore(hasMoreData);
    } finally {
      setPage((p) => p + 1);
    }
  }, [actionGetFeed, actionGetPosts, feed, page, userId]);

  const handleReset = useCallback(async () => {
    await resetStories();
    await resetPosts();
    setPage(0);
    setHasMore(true);
  }, [resetPosts, resetStories]);

  useEffect(() => {
    handleReset();
    getPosts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId]);

  useEffect(() => {
    const handleRouteChange = () => {
      handleReset();
    };

    router.events.on("routeChangeStart", handleRouteChange);

    return () => {
      router.events.off("routeChangeStart", handleRouteChange);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <InfiniteScroll
      dataLength={posts.length}
      hasMore={hasMore}
      next={getPosts}
      scrollableTarget="exploreScroll"
    >
      {/* {!withMasonry ? (
        <Stack sx={{ width: "100%", maxWidth: 550 }} mx="auto" spacing="xl">
          {posts.map((post: PostType, i: number) => {
            return <Post key={`post-${i}`} post={post} />;
          })}
        </Stack>
      ) : (
        <Masonry
          breakpointCols={{
            default: 3,
            1100: 1,
          }}
          className="my-masonry-grid"
          columnClassName="my-masonry-grid_column"
        >
          {posts.map((post: PostType, i: number) => {
            return <Post key={`post-${i}`} post={post} />;
          })}
        </Masonry>
      )} */}

      <SimpleGrid cols={3}>
        {posts.map((post: PostType, i: number) => {
          return <Post key={`post-${i}`} post={post} />;
        })}
      </SimpleGrid>
    </InfiniteScroll>
  );
};

export default PostArea;
