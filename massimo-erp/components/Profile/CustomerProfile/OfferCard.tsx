import {
  ArrowPathIcon,
  CubeIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import {
  Paper,
  Group,
  Stack,
  Text,
  Badge,
  Box,
  CheckIcon,
  Tooltip,
  useMantineTheme,
} from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate, hexToRGBA } from "~utils/tools";
import { OfferType, ProjectType } from "~utils/types/Project";

interface PropType {
  data: OfferType;
  project: ProjectType;
}

export default function RequestCard(props: PropType) {
  const { data, project } = props;

  const { setProjectModal, setLastOffer } = useStore(
    "temp",
    (state) => ({
      setProjectModal: state.setProjectModal!,
      setLastOffer: state.setLastOffer!,
    }),
    shallow
  );
  const { offerStatusColors, offers } = useStore(
    "offers",
    (state) => ({
      offerStatusColors: state.offerStatusColors!,
      offers: state.offers!,
    }),
    shallow
  );
  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );

  const theme = useMantineTheme();

  return (
    <Paper
      sx={(theme) => ({
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        cursor: "pointer",
        transition: "filter .2s ease",
        ":hover": {
          filter: "brightness(.9)",
        },
        padding: theme.spacing.sm,
        display: "flex",
      })}
      onClick={() => {
        setLastOffer(
          offers.filter((e) => project.offerIds.includes(e.id)).slice(-1)[0]
        );
        setProjectModal({
          isOpen: true,
          isOwner: false,
          type: "edit",
          data: project,
        });
      }}
    >
      <Tooltip label={project.priority}>
        <Box
          sx={(theme) => ({
            width: 4,
            height: "auto",
            borderRadius: 8,
            background: theme.colors[priorityColors[project.priority]][9],
            marginRight: 8,
            flexShrink: 0,
          })}
        />
      </Tooltip>
      <Group position="apart" noWrap px={4} sx={{ width: "100%" }}>
        <Stack spacing={8}>
          <Stack spacing={2}>
            <Text size="xs" weight={600}>
              {data.name}
            </Text>
            <Text size="md" weight={600}>
              {!Number.isNaN(data.budget!)
                ? `₺ ${data.budget}`.replace(/\B(?=(\d{3})+(?!\d))/g, ",")
                : "₺ "}
            </Text>
            <Text size="xs" weight={600} color="dimmed">
              {formatDate(data.start)} - {formatDate(data.end)}
            </Text>
          </Stack>
          <Group spacing={4}>
            {project.departmentIds?.map((id: number, i: number) => {
              const activeDepartment = departments.filter(
                (e) => e.id === id
              )[0] || { color: "#000000" };

              return (
                <Badge
                  key={"department-" + i}
                  sx={(theme) => ({
                    background: hexToRGBA(activeDepartment.color, 0.1),
                    color: activeDepartment.color,
                  })}
                >
                  {activeDepartment.name}
                </Badge>
              );
            })}
          </Group>
        </Stack>
        <Tooltip label={data.status}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            {data.status === "Offer" ? (
              <CubeIcon
                style={{
                  width: 24,
                  height: 24,
                  color: theme.colors[offerStatusColors[data.status]][9],
                }}
              />
            ) : data.status === "Canceled" ? (
              <XMarkIcon
                style={{
                  width: 24,
                  height: 24,
                  color: theme.colors[offerStatusColors[data.status]][9],
                }}
              />
            ) : data.status === "Revised" ? (
              <ArrowPathIcon
                style={{
                  width: 24,
                  height: 24,
                  color: theme.colors[offerStatusColors[data.status]][9],
                }}
              />
            ) : (
              <CheckIcon
                style={{
                  width: 16,
                  height: 16,
                  color: theme.colors[offerStatusColors[data.status]][9],
                }}
              />
            )}
          </Box>
        </Tooltip>
      </Group>
    </Paper>
  );
}
