import React from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { BanknotesIcon } from "@heroicons/react/24/outline";
import {
  Avatar,
  Box,
  Divider,
  Group,
  Indicator,
  Paper,
  SegmentedControl,
  Button,
  Stack,
  Text,
  ThemeIcon,
  Title,
  Image,
  ScrollArea,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import { RoleType } from "~utils/types/Roles";
import ActiveAvatar from "~components/ActiveAvatar";
import { CustomerType } from "~utils/types/Customer";

interface PropType {
  shown?: CustomerType;
  activeTab: string;
  setActiveTab(value: string): void;
}

export default function AccountDetails(props: PropType) {
  const { shown, activeTab, setActiveTab } = props;
  const matches = useMediaQuery("(max-width: 1300px)");
  const { t } = useTranslation();

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { setSendRequestModal } = useStore(
    "temp",
    (state) => ({
      setSendRequestModal: state.setSendRequestModal!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const showInfos = shown?.personIds.includes(activeUserId);

  return (
    <Paper
      radius="md"
      sx={(theme) => ({
        width: "calc(100vw-32px)",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[1],
        overflow: "hidden",
      })}
    >
      <Image
        height={200}
        alt="banner"
        src="https://cdn.pixabay.com/photo/2021/10/01/03/57/mountain-6671289_960_720.jpg"
        withPlaceholder
      ></Image>
      <Box
        sx={(theme) => ({
          width: "calc(100vw-32px)",
          height: matches ? "auto" : 80,
          padding: `0 ${matches ? 0 : theme.spacing.xl * 2 + "px"}`,
        })}
      >
        <Box
          sx={{
            height: "100%",
            display: "flex",
            flexDirection: matches ? "column" : "row",
            alignItems: "center",
            justifyContent: "space-between",
          }}
        >
          <Box
            p={matches ? "lg" : 0}
            pt={0}
            sx={{
              display: "flex",
              flexDirection: matches ? "column" : "row",
              alignItems: "center",
              gap: matches ? 0 : 16,
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                gap: 32,
              }}
              mb={matches ? "md" : 0}
            >
              <Stack spacing={0} mt={matches ? 20 : 0}>
                <Title order={4} align={matches ? "center" : "left"}>
                  {shown?.fullName}
                </Title>
                <Text
                  color="dimmed"
                  size="xs"
                  align={matches ? "center" : "left"}
                >
                  {shown?.shortName}
                </Text>
              </Stack>
            </Box>

            {!matches && <Divider orientation="vertical" my="xl" />}

            {shown === undefined && (
              <Group>
                <ThemeIcon size="lg" variant="light" color="blue">
                  <BanknotesIcon style={{ width: 16, height: 16 }} />
                </ThemeIcon>
                <Stack spacing={0}>
                  <Text size="md" sx={{ lineHeight: 1 }}>
                    2000₺
                  </Text>
                  <Text color="dimmed" size="xs">
                    {t("profile.balance")}
                  </Text>
                </Stack>
                <Divider orientation="vertical" my="xs" />
                <Stack spacing={0}>
                  <Text color="red" size="md" sx={{ lineHeight: 1 }}>
                    -1000₺
                  </Text>
                  <Text color="dimmed" size="xs">
                    {t("profile.debtStatus")}
                  </Text>
                </Stack>
              </Group>
            )}
          </Box>

          <ScrollArea offsetScrollbars scrollbarSize={3}>
            <Box
              pb={matches ? "lg" : 0}
              sx={{
                maxWidth: matches ? "calc(100vw - 84px)" : "auto",
              }}
            >
              <Group position="center">
                {showInfos && (
                  <Button
                    size="xs"
                    variant="light"
                    onClick={() =>
                      setSendRequestModal({
                        isOpen: true,
                        isCustomer: `${shown?.id}`,
                      })
                    }
                    sx={{
                      height: 31.69,
                    }}
                  >
                    {t("profile.sendRequest")}
                  </Button>
                )}
                <SegmentedControl
                  color="blue"
                  value={activeTab}
                  onChange={(value: string) => setActiveTab(value)}
                  data={
                    [
                      {
                        label: t("profile.posts"),
                        value: "posts",
                      },
                      { label: t("profile.Timeline"), value: "timeline" },
                      {
                        label: t("profile.CompletedProjects"),
                        value: "completedWorks",
                      },
                      // { label: "Authorized Team", value: "team" },
                    ]
                    // .concat(
                    //   showInfos
                    //     ? [
                    //         {
                    //           label: t("profile.Bills"),
                    //           value: "bills",
                    //         },
                    //       ]
                    //     : []
                    // )
                  }
                />
              </Group>
            </Box>
          </ScrollArea>
        </Box>
      </Box>
    </Paper>
  );
}
