import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

import {
  Box,
  Group,
  Paper,
  Space,
  Stack,
  Tabs,
  Text,
  Tooltip,
  SimpleGrid,
  Timeline,
  Col,
  Grid,
} from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { formatDate } from "~utils/tools";
import AccountDetails from "./AccountDetails";
import { useElementSize, useMediaQuery } from "@mantine/hooks";
import { OfferType, ProjectType } from "~utils/types/Project";
import ProjectCard from "~components/Profile/CustomerProfile/OfferCard";
import EmployeeList from "./EmployeeList";
import RequestCard from "~components/Admin/Requests/RequestCard";
import { CC } from "~types";
import { CustomerType } from "~utils/types/Customer";
import { PostType } from "~utils/types/Post";
import Post from "~components/Post";
import Stories from "../../Stories";
import PostArea from "../PostArea";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { isNumber, isUndefined } from "lodash";

const CustomerProfile: CC<{
  id?: number;
}> = function ({ id }) {
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { priorityColors } = useStore(
    "tasks",
    (state) => ({
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const {
    posts,
    stories,
    actionGetPosts,
    resetPosts,
    resetStories,
    actionGetFeed,
  } = useStore(
    "posts",
    (state) => ({
      posts: state.posts!,
      stories: state.stories!,
      actionGetPosts: state.actionGetPosts!,
      resetPosts: state.resetPosts!,
      resetStories: state.resetStories!,
      actionGetFeed: state.actionGetFeed!,
    }),
    shallow
  );

  const shownCustomer = customers.find(
    (e: CustomerType) => e.id === +(id || -1)
  );

  const ownProjects = projects.filter(
    (e) => requests.find((r) => r.id === e.requestId)?.customerId === id
  );

  const [activeTab, setActiveTab] = useState("posts");
  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(max-width: 992px)");

  const [page, setPage] = useState<number>(0);
  const [hasMore, setHasMore] = useState(true);

  const getPosts = useCallback(
    async (p?: number) => {
      if (isUndefined(id)) {
        return;
      }

      try {
        const hasMoreData = await actionGetPosts(
          id as number,
          isNumber(p) ? p : page,
          true
        );
        setHasMore(hasMoreData);
      } finally {
        setPage((p) => p + 1);
      }
    },
    [actionGetPosts, id, page]
  );

  const handleReset = useCallback(async () => {
    await resetPosts();
    setPage(0);
    setHasMore(true);

    getPosts(0);
  }, [getPosts, resetPosts]);

  useEffect(() => {
    handleReset();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, activeTab]);

  return (
    <CustomInfiniteScroll
      dataLength={posts.length}
      hasMore={activeTab === "posts" ? hasMore : false}
      next={getPosts}
      height="calc(100vh - 70px)"
    >
      <Stack
        align="stretch"
        ref={ref}
        sx={{ opacity: width === 0 ? 0 : 1, transition: "opacity .2s ease" }}
        p={matches ? 16 : 32}
      >
        <Tabs value={activeTab}>
          <AccountDetails
            shown={shownCustomer}
            activeTab={activeTab}
            setActiveTab={(value) => setActiveTab(value)}
          />

          <Space h="md" />

          <Grid gutter="md">
            <Col span={width <= 1280 ? 12 : "auto"} sx={{ order: 1 }}>
              <EmployeeList organizationId={id || -1} />
            </Col>

            <Col
              span={width <= 1280 ? 12 : 9}
              sx={{ order: width <= 1280 ? 2 : 1 }}
            >
              <Tabs.Panel value="posts">
                <SimpleGrid
                  cols={width > 1150 ? 3 : width > 700 ? 3 : width > 450 ? 2 : 1}
                  spacing="md"
                  verticalSpacing={32}
                >
                  {posts.map((post: PostType, i: number) => {
                    return <Post key={`post-${i}`} post={post} />;
                  })}
                </SimpleGrid>
              </Tabs.Panel>
              <Tabs.Panel value="timeline" sx={{ flexGrow: 1 }}>
                <SimpleGrid cols={width > 1000 ? 3 : width > 600 ? 2 : 1}>
                  {ownProjects.map((project: ProjectType, i: number) => {
                    const lastOffer = offers
                      .filter((e) => project.offerIds.includes(e.id))
                      .slice(-1)[0];

                    return lastOffer.status !== "Completed" ? (
                      <ProjectCard
                        key={"project-" + i}
                        data={lastOffer}
                        project={project}
                      />
                    ) : (
                      false
                    );
                  })}
                </SimpleGrid>
              </Tabs.Panel>
              <Tabs.Panel value="completedWorks" sx={{ flexGrow: 1 }}>
                <SimpleGrid cols={width > 1000 ? 3 : width > 600 ? 2 : 1}>
                  {ownProjects.map((project: ProjectType, i: number) => {
                    const lastOffer = offers
                      .filter((e) => project.offerIds.includes(e.id))
                      .slice(-1)[0];

                    return lastOffer.status === "Completed" ? (
                      <ProjectCard
                        key={"project-" + i}
                        data={lastOffer}
                        project={project}
                      />
                    ) : (
                      false
                    );
                  })}
                </SimpleGrid>
              </Tabs.Panel>
              {/* <Tabs.Panel value="team" sx={{ flexGrow: 1 }}>
              <SimpleGrid cols={width <= 1280 ? 1 : 3}>
                <Paper
                  p="md"
                  sx={(theme) => ({
                    background:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[8]
                        : theme.colors.gray[1],
                  })}
                >
                  <Group>
                    <Avatar
                      src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=250&q=80"
                      size="lg"
                      radius="xl"
                    />
                    <Stack spacing={0}>
                      <Title order={4}>John Doe</Title>
                      <Text color="dimmed" size="xs" weight={600}>
                        Web Developer
                      </Text>
                    </Stack>
                  </Group>
                </Paper>
                <Paper
                  p="md"
                  sx={(theme) => ({
                    background:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[8]
                        : theme.colors.gray[1],
                  })}
                >
                  <Group>
                    <Avatar
                      src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&ixlib=rb-1.2.1&auto=format&fit=crop&w=250&q=80"
                      size="lg"
                      radius="xl"
                    />
                    <Stack spacing={0}>
                      <Title order={4}>John Doe</Title>
                      <Text color="dimmed" size="xs" weight={600}>
                        Web Developer
                      </Text>
                    </Stack>
                  </Group>
                </Paper>
              </SimpleGrid>
            </Tabs.Panel> */}
              <Tabs.Panel value="bills" sx={{ flexGrow: 1 }}>
                <></>
              </Tabs.Panel>
            </Col>

            {/* <Col
            span={width <= 1280 ? 12 : "auto"}
            sx={{ order: width <= 1280 ? 1 : 2 }}
          >
            <EmployeeList />
          </Col> */}
          </Grid>
        </Tabs>
      </Stack>
    </CustomInfiniteScroll>
  );
};

export default CustomerProfile;
