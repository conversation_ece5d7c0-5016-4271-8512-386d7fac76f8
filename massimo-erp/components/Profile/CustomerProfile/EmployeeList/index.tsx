import {
  Avatar,
  Group,
  Paper,
  Space,
  Stack,
  Text,
  Title,
  ActionIcon,
  useMantineTheme,
  Anchor,
  Tooltip,
} from "@mantine/core";
import {
  LinkIcon,
  PaperAirplaneIcon,
  UserMinusIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import { Router, useRouter } from "next/router";
import { useCallback, useEffect, useState } from "react";
import { orderBy } from "lodash";

const EmployeeList: CC<{
  organizationId: number;
}> = function ({ organizationId }) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const {
    users,
    connections,
    connectionRequests,
    actionConnectUser,
    actionGetConnections,
    actionGetConnectionRequests,
  } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      connections: state.connections!,
      connectionRequests: state.connectionRequests!,
      actionConnectUser: state.actionConnectUser!,
      actionGetConnections: state.actionGetConnections!,
      actionGetConnectionRequests: state.actionGetConnectionRequests!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { chats, subscribedChats } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      subscribedChats: state.subscribedChats!,
    }),
    shallow
  );

  const theme = useMantineTheme();
  const { t } = useTranslation();
  const { push } = useRouter();
  const [fetchConnection, setFetchConnections] = useState(false);
  const [fetchingUsers, setFetchingUsers] = useState<number[]>([]);

  const activeOrganization = customers.find((e) => e.id === organizationId);
  const persons = users.filter((e) =>
    activeOrganization?.personIds.includes(e.id)
  );

  const getConnections = useCallback(async () => {
    setFetchConnections(true);

    try {
      await actionGetConnections(activeUserId);
      await actionGetConnectionRequests();
    } finally {
      setFetchConnections(false);
    }
  }, [actionGetConnectionRequests, actionGetConnections, activeUserId]);

  const handleConnect = useCallback(
    async (userId: number) => {
      setFetchingUsers((users) => [...users, userId]);

      try {
        await actionConnectUser(userId);
      } finally {
        await getConnections();
        setFetchingUsers((users) => users.filter((x) => x !== userId));
      }
    },
    [actionConnectUser, getConnections]
  );

  useEffect(() => {
    getConnections();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Paper
      sx={(theme) => ({
        width: "100%",
        background:
          theme.colorScheme === "dark"
            ? theme.colors.dark[8]
            : theme.colors.gray[2],
        padding: theme.spacing.md,
      })}
    >
      <Text size="sm" color="dimmed" weight={600}>
        {t("employeeList")}
      </Text>
      <Space h="md" />
      <Stack>
        {persons.length === 0 && (
          <Text size="sm" weight={600} color="dimmed" my="xs" align="center">
            {t("profile.empty")}
          </Text>
        )}
        {orderBy(persons, "id").map((user: UserType, i: number) => {
          const isConnected = connections.some((e) =>
            e.target1Id === user.id
              ? e.target2Id === activeUserId
              : e.target2Id === user.id
              ? e.target1Id === activeUserId
              : false
          );
          const isRequested = connectionRequests.some((e) => {
            if (
              (e.target1Id === user?.id && e.isAccepted2) ||
              (e.target2Id === user?.id && e.isAccepted1)
            ) {
              return true;
            }

            return false;
          });

          const chatId = chats.find(
            (e) =>
              e.type === "person" &&
              (e.personId === user.id || e.ownerId === user.id)
          )?.id;
          const defaultRoleLabel = roles.find(
            (e) => e.id === user.defaultRoleId[0]
          )?.label;

          return (
            <Group
              key={"connection-" + user.id}
              sx={{
                position: "relative",
              }}
              noWrap
            >
              <ActiveAvatar userId={user.id} radius="xl" />
              <Stack spacing={0} sx={{ width: "100%", position: "relative" }}>
                <Tooltip label={`${user.name} ${user.surname}`}>
                  <Anchor
                    size={"sm"}
                    sx={(theme) => ({
                      color: theme.colors.gray[4],
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    })}
                    onClick={() => {
                      push(`/profile/user/${user.id}`);
                    }}
                  >
                    {user.name} {user.surname}
                  </Anchor>
                </Tooltip>
                {defaultRoleLabel && (
                  <Text size="xs" color="dimmed">
                    {t(`defaultRoles.${defaultRoleLabel.toLowerCase()}`)}
                  </Text>
                )}
              </Stack>

              {user.id !== activeUserId && (
                <ActionIcon
                  variant="transparent"
                  size="sm"
                  onClick={() => {
                    handleConnect(user.id);
                  }}
                  loading={fetchingUsers.includes(user.id)}
                >
                  <LinkIcon
                    style={{
                      width: 18,
                      height: 18,
                      color: isRequested
                        ? theme.colors.yellow[7]
                        : isConnected
                        ? theme.colors.blue[7]
                        : theme.colors.dark[3],
                    }}
                  />
                </ActionIcon>
              )}
              {isConnected && chatId && (
                <ActionIcon
                  variant="transparent"
                  size="sm"
                  onClick={() => push(`/apps/chat/${chatId}`)}
                >
                  <PaperAirplaneIcon
                    style={{
                      width: 18,
                      height: 18,
                    }}
                  />
                </ActionIcon>
              )}
            </Group>
          );
        })}
      </Stack>
    </Paper>
  );
};

export default EmployeeList;
