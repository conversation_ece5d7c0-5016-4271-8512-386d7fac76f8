import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { Group, Switch, Input, Text } from "@mantine/core";
import { PencilIcon } from "@heroicons/react/24/outline";
import { CC } from "~types";

const ModalHeader: CC<{
  canEdit: boolean;
}> = function ({ canEdit }) {
  const { taskViewModalData, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      taskViewModalData: state.taskViewModalData!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );

  return (
    <Group position="apart" noWrap>
      {canEdit ? (
        <Input
          icon={<PencilIcon style={{ width: 16, height: 16 }} />}
          value={taskViewModalData.title}
          onChange={(event: any) =>
            setTaskViewModalData({
              title: event.target.value,
            })
          }
          styles={(theme) => ({
            wrapper: {
              width: "100%",
            },
            input: {
              background: "transparent",
              borderColor: "transparent",
              paddingLeft: 0,
              fontSize: 18,
              fontWeight: 600,
              transition: "padding .2s ease, border-color .2s ease",
              ":focus": {
                borderColor:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[4]
                    : theme.colors.gray[4],
              },
            },
          })}
        />
      ) : (
        <Text size={18} weight={600}>
          {taskViewModalData.title}
        </Text>
      )}
    </Group>
  );
};

export default ModalHeader;
