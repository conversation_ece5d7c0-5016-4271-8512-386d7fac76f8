import { forwardRef, useCallback, useEffect, useRef, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate, formatTime } from "~utils/tools";
import {
  isEqual,
  isNumber,
  isObject,
  isString,
  omit,
  orderBy,
  uniq,
} from "lodash";

import {
  Avatar,
  Badge,
  Box,
  Button,
  Divider,
  Group,
  Modal,
  Select,
  Space,
  Stack,
  Text,
  ThemeIcon,
  Tooltip,
  ActionIcon,
  Menu,
  Textarea,
  ScrollArea,
  Switch,
  Paper,
  LoadingOverlay,
  useMantineTheme,
  FileButton,
  CloseButton,
  SimpleGrid,
  Image,
  Loader,
} from "@mantine/core";
import { openConfirmModal } from "@mantine/modals";

import {
  CalendarDaysIcon,
  PlusSmallIcon,
  ChevronDownIcon,
  PaperClipIcon,
  ClockIcon,
  ArrowPathRoundedSquareIcon,
  PhotoIcon,
  DocumentIcon,
  EllipsisHorizontalIcon,
  QueueListIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import { showNotification } from "@mantine/notifications";
import { Calendar, TimeRangeInput } from "@mantine/dates";
import ModalHeader from "./ModalHeader";
import DescriptionArea from "./DescriptionArea";
import ContentSubTasks from "./ContentSubTasks";
import {
  getHotkeyHandler,
  useDebouncedState,
  useElementSize,
  useMediaQuery,
} from "@mantine/hooks";
import SelectUserModal from "../SelectUserModal";
import { useTranslation } from "next-i18next";
import ActiveAvatar from "~components/ActiveAvatar";
import { ProjectType } from "~utils/types/Project";
import { CustomerType } from "~utils/types/Customer";
import { MessageType } from "~utils/types/Chat";
import TextMessage from "~components/Apps/Chat/MessageType/Text";
import FileMessage from "~components/Apps/Chat/MessageType/File";
import { CC } from "~types";
import ImageMessage from "~components/Apps/Chat/MessageType/Image";
import TaskMessage from "~components/Apps/Chat/MessageType/Task";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { NotificationType } from "~utils/types/Notification";

const MessageTypes = {
  text: TextMessage,
  image: ImageMessage,
  file: FileMessage,
  task: TaskMessage,
  subtask: ContentSubTasks,
};

interface ItemProps extends React.ComponentPropsWithoutRef<"div"> {
  label: string;
  customer: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(function Item(
  { label, customer, ...others }: ItemProps,
  ref
) {
  return (
    <div ref={ref} {...others}>
      <Stack spacing={4}>
        <Text size="sm" weight={600}>
          {label}
        </Text>
        <Text size="xs">{customer}</Text>
      </Stack>
    </div>
  );
});

const TaskViewModalContent: CC<{
  update: () => void;
  deleteTask: () => void;
}> = function ({ update, deleteTask }) {
  const {
    isLoading,
    loadingLevel,
    isOpenTaskViewModal,
    setIsOpenTaskViewModal,
    taskViewModalData,
    setTaskViewModalData,
    setIsOpenNewTagModal,
    setIsOpenSelectUserModal,
    blankTask,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      isOpenTaskViewModal: state.isOpenTaskViewModal!,
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      taskViewModalData: state.taskViewModalData!,
      setTaskViewModalData: state.setTaskViewModalData!,
      setIsOpenNewTagModal: state.setIsOpenNewTagModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
      blankTask: state.computed!.blankTask!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const {
    chats,
    messages,
    actionInitChat,
    actionDeleteMessage,
    actionSendMessage,
    actionGetMessages,
  } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      subscribedChats: state.subscribedChats!,
      messages: state.messages!,
      actionSendMessage: state.actionSendMessage!,
      actionDeleteMessage: state.actionDeleteMessage!,
      actionInitChat: state.actionInitChat!,
      actionGetMessages: state.actionGetMessages!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { tags } = useStore(
    "data",
    (state) => ({
      tags: state.tags!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { statusColors, priorityColors } = useStore(
    "tasks",
    (state) => ({
      statusColors: state.statusColors!,
      priorityColors: state.priorityColors!,
    }),
    shallow
  );
  const { actionUploadFiles } = useStore(
    "files",
    (state) => ({
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );
  const { notifications, actionViewNotification, actionSendNotifications } =
    useStore(
      "notifications",
      (state) => ({
        notifications: state.notifications!,
        actionViewNotification: state.actionViewNotification!,
        actionSendNotifications: state.actionSendNotifications!,
      }),
      shallow
    );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const matches = useMediaQuery("(max-width: 992px)");
  const viewport = useRef<HTMLDivElement>();
  const [scrollPosition, setScrollPositionChange] = useDebouncedState(
    { x: 0, y: 0 },
    400
  );

  const [comment, setComment] = useDebouncedState("", 200, {
    leading: true,
  });

  const activeUser = users.find((e) => e.id === activeUserId);
  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const [isSending, setIsSending] = useState(false);
  const [initChat, setInitChat] = useState(false);

  const [files, setFiles] = useState<File[]>([]);
  const [images, setImages] = useState<File[]>([]);

  const [hasMore, setHasMore] = useState(true);
  const [page, setPage] = useState(0);
  const [isFetching, setIsFetching] = useState(false);

  const activeUserDefaultRoleWeight =
    roles.find((e) => e.id === activeUser?.defaultRoleId[0] && e.isDefaultRole)
      ?.weight || 0;

  const canEdit =
    activeUserDefaultRoleWeight < 0 ||
    taskViewModalData.contributorIds[0] === activeUserId;

  const activeChat = chats.find(
    (e) => e.type === "task" && e.taskId === taskViewModalData.id
  );

  const ownMessages = orderBy(messages, "id").filter(
    (e) => e.chatId === activeChat?.id
  );

  const fetchMoreMessages = useCallback(async () => {
    let currentPage: number;

    setPage((p) => {
      currentPage = p + 1;
      return currentPage;
    });

    try {
      const hasMoreData = await actionGetMessages(
        activeChat?.id as number,
        currentPage!
      );

      setHasMore(hasMoreData);
    } catch (err) {
      throw err;
    }
  }, [actionGetMessages, activeChat]);

  const focusInput = useCallback(() => {
    (inputRef.current as any).focus();
  }, []);

  const scrollToBottom = useCallback(
    () =>
      viewport.current?.scrollTo({
        top: viewport.current!.scrollHeight,
        behavior: "smooth",
      }),
    []
  );

  const handleSelect = useCallback(
    (key: "files" | "images", data: File[]) => {
      const dataList: File[] = [];

      data.forEach((e) => {
        if (e.size >= 10485760) {
          showNotification({
            color: "red",
            title: t("largeFile.title"),
            message: t("largeFile.message"),
            autoClose: 3000,
            styles: {
              root: {
                zIndex: 400,
              },
            },
          });
        } else {
          dataList.push(e);
        }
      });

      if (key === "files") {
        setFiles([...files, ...dataList].slice(0, 20));
      } else {
        setImages([...images, ...dataList].slice(0, 20));
      }
    },
    [files, images, t]
  );

  const handleDelete = useCallback(
    async (messageData: MessageType) => {
      if (!isObject(messageData)) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });

        return;
      }

      setIsSending(true);

      try {
        await actionDeleteMessage(messageData);
      } catch (err) {
        console.error(err);

        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        setIsSending(false);
      }
    },
    [actionDeleteMessage, t]
  );

  const sendComment = useCallback(async () => {
    if (comment === "" && images.length === 0 && files.length === 0) {
      return;
    }
    if (!isSending) {
      setIsSending(true);

      let newMessage: Partial<MessageType> = {
        type: "text",
        ownerId: activeUserId,
        chatId: activeChat?.id || -1,
        content: {
          text: comment,
        },
      };

      try {
        if (images.length > 0) {
          const imageIds = await actionUploadFiles(
            images.map((imageFile) => ({
              userId: activeUserId,
              file: imageFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            imageIds.map((imageId) => {
              return actionSendMessage({
                type: "image",
                ownerId: activeUserId,
                chatId: activeChat?.id || -1,
                content: {
                  fileId: imageId,
                },
              });
            })
          );
        }

        if (files.length > 0) {
          const fileIds = await actionUploadFiles(
            files.map((fileFile) => ({
              userId: activeUserId,
              file: fileFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            fileIds.map((fileId) => {
              return actionSendMessage({
                type: "file",
                ownerId: activeUserId,
                chatId: activeChat?.id || -1,
                content: {
                  fileId: fileId,
                },
              });
            })
          );
        }

        if (comment && comment.length > 0) {
          await actionSendMessage(newMessage);
        }

        if (activeChat?.type === "task") {
          await actionSendNotifications(
            taskViewModalData.assigneeIds
              .filter((e) => e !== activeUserId)
              .map((e) => ({
                target: "messages",
                value: `${activeChat.id}`,
                user: e,
              }))
          );
        }
      } catch (error) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        scrollToBottom();
        if (inputRef.current) {
          inputRef.current.value = "";
        }
        setFiles([]);
        setImages([]);
        focusInput();
        setIsSending(false);
      }
    }
  }, [
    actionSendMessage,
    actionSendNotifications,
    actionUploadFiles,
    activeChat?.id,
    activeChat?.type,
    activeUserId,
    comment,
    files,
    focusInput,
    images,
    isSending,
    scrollToBottom,
    t,
    taskViewModalData.assigneeIds,
  ]);

  const addSubtasks = useCallback(() => {
    const newMessage: Partial<MessageType> = {
      type: "text",
      ownerId: activeUserId,
      chatId: activeChat?.id || -1,
      content: {
        type: "subtask",
        subtasks: [],
        contributors: [],
        start: new Date(),
        end: new Date(),
      },
    };

    try {
      actionSendMessage(newMessage);
    } catch {}
  }, [actionSendMessage, activeChat, activeUserId]);

  const readNotifications = useCallback(async () => {
    await notifications.forEach(async (notification: NotificationType) => {
      if (
        (notification.target === "messages" &&
          notification.value === `${activeChat?.id}`) ||
        (notification.target === "tasks" &&
          notification.value === `${taskViewModalData.id}`)
      ) {
        await actionViewNotification([{ id: notification.id }]);
      }
    });
  }, [
    actionViewNotification,
    activeChat?.id,
    notifications,
    taskViewModalData,
  ]);

  // const [attachableTags, setAttachableTags] = useState([]);

  // useEffect(() => {
  //   const c: any = tags.filter(
  //     ({ value: id1 }) =>
  //       !taskViewModalData!.tags?.some(({ value: id2 }) => id2 === id1)
  //   );
  //   // @ts-ignore
  //   setAttachableTags([...c]);
  // }, [tags, taskViewModalData]);

  // Tag Events
  // const addTagToTask = useCallback(
  //   (tag: TaskTagType) => {
  //     setTaskViewModalData({ tags: [...taskViewModalData.tags!, tag] });
  //   },
  //   [taskViewModalData, setTaskViewModalData]
  // );

  // const removeTagFromTask = useCallback(
  //   (i: number) => {
  //     const c = [...taskViewModalData.tags!];

  //     c.splice(i, 1);

  //     setTaskViewModalData({ tags: [...c] });
  //   },
  //   [taskViewModalData, setTaskViewModalData]
  // );

  useEffect(() => {
    focusInput();
    setInitChat(true);
    actionInitChat(activeChat?.id || -1).then(() => {
      if (viewport.current) {
        setTimeout(() => {
          scrollToBottom();
          setTimeout(() => {
            setInitChat(false);
          }, 10);
        }, 100);
      }
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    setComment("");
    setFiles([]);
    setImages([]);
    setPage(0);

    if (isOpenTaskViewModal) {
      readNotifications();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpenTaskViewModal]);

  return (
    <Box
      sx={() => ({
        display: "flex",
        flexDirection: matches ? "column" : "row",
        flexWrap: "nowrap",
        alignItems: "stretch",
        overflow: "hidden",
        height: "100%",
      })}
      ref={ref}
    >
      <Stack
        sx={() => ({
          display: "flex",
          width: "100%",
        })}
      >
        <ModalHeader canEdit={canEdit} />

        <CustomInfiniteScroll
          dataLength={ownMessages.length}
          hasMore={hasMore}
          height={450}
          inverse={true}
          next={fetchMoreMessages}
        >
          <Stack spacing="sm" justify="space-between">
            <DescriptionArea canEdit={canEdit} />

            <Stack>
              {ownMessages.map((message: MessageType, i: number) => {
                let isMine = message.ownerId === activeUserId;
                let owner = users.find((e) => e.id === message.ownerId);
                const defaultRoleWeight = roles.find(
                  (e) => activeUser?.defaultRoleId[0] === e.id
                )?.weight;

                const Message =
                  MessageTypes[
                    (message.content.type ||
                      message.type) as keyof typeof MessageTypes
                  ];

                if (!owner) {
                  return null;
                }

                return (
                  <Group
                    sx={{ width: "100%" }}
                    key={"content-" + i}
                    position={
                      message.type !== "subtasks"
                        ? isMine
                          ? "right"
                          : "left"
                        : "center"
                    }
                  >
                    <Paper
                      p="sm"
                      sx={(theme) => ({
                        position: "relative",
                        minWidth: 180,
                        background:
                          theme.colorScheme === "dark"
                            ? theme.colors.dark[8]
                            : theme.colors.gray[2],
                      })}
                    >
                      <Stack spacing={0}>
                        <Group position="apart">
                          <Text size="sm" weight={600}>
                            {owner.name} {owner.surname}
                          </Text>
                          {isNumber(defaultRoleWeight) &&
                            (isMine || defaultRoleWeight < 0) && (
                              <Menu
                                shadow="md"
                                width={200}
                                offset={0}
                                position={"top-end"}
                                transition="fade"
                                withArrow
                              >
                                <Menu.Target>
                                  <ActionIcon size="sm">
                                    <EllipsisHorizontalIcon
                                      style={{ width: 16, height: 16 }}
                                    />
                                  </ActionIcon>
                                </Menu.Target>
                                <Menu.Dropdown>
                                  <Menu.Item
                                    onClick={() => handleDelete(message)}
                                  >
                                    {t("chat.delete")}
                                  </Menu.Item>
                                </Menu.Dropdown>
                              </Menu>
                            )}
                        </Group>
                        <Group position="apart" align="flex-end" spacing={8}>
                          <Message
                            message={message as MessageType}
                            content={message.content as any}
                            isMobile={width < 600}
                            i={i}
                          />
                        </Group>
                        <Group position="right">
                          <Text size="xs" color="dimmed">
                            {formatTime(message.createdAt)}{" "}
                            {formatDate(message.createdAt)}
                          </Text>
                        </Group>
                      </Stack>
                    </Paper>
                  </Group>
                );
              })}
            </Stack>
          </Stack>
        </CustomInfiniteScroll>

        {/* <ScrollArea
          styles={{
            viewport: {
              width: "100%",
              minHeight: 350,
              maxHeight:
                images.length > 0 || files.length > 0
                  ? "calc(100vh - 700px)"
                  : "calc(100vh - 400px)",
            },
          }}
          scrollbarSize={7}
          offsetScrollbars
          viewportRef={viewport as any}
          onScrollPositionChange={setScrollPositionChange}
        >
          <Stack spacing="md" justify="space-between" sx={{ minHeight: 400 }}>
            <DescriptionArea />

            <Stack>
              {(initChat || isFetching) && (
                <Group position="center">
                  <Loader variant="bars" size="sm" />
                </Group>
              )}
              {orderBy(messages, "id")
                .filter((e) => e.chatId === activeChat?.id)
                .map((message: MessageType, i: number) => {
                  let isMine = message.ownerId === activeUserId;
                  let owner = users.find((e) => e.id === message.ownerId);
                  const defaultRoleWeight = roles.find(
                    (e) => activeUser?.defaultRoleId[0] === e.id
                  )?.weight;

                  const Message =
                    MessageTypes[
                      (message.content.type ||
                        message.type) as keyof typeof MessageTypes
                    ];

                  if (!owner) {
                    return null;
                  }

                  return (
                    <Group
                      sx={{ width: "100%" }}
                      key={"content-" + i}
                      position={
                        message.type !== "subtasks"
                          ? isMine
                            ? "right"
                            : "left"
                          : "center"
                      }
                    >
                      <Paper
                        p="sm"
                        sx={(theme) => ({
                          position: "relative",
                          minWidth: 180,
                          background:
                            theme.colorScheme === "dark"
                              ? theme.colors.dark[8]
                              : theme.colors.gray[2],
                        })}
                      >
                        <Stack spacing={0}>
                          <Group position="apart">
                            <Text size="sm" weight={600}>
                              {owner.name} {owner.surname}
                            </Text>
                            {isNumber(defaultRoleWeight) &&
                              (isMine || defaultRoleWeight < 0) && (
                                <Menu
                                  shadow="md"
                                  width={200}
                                  offset={0}
                                  position={"top-end"}
                                  transition="fade"
                                  withArrow
                                >
                                  <Menu.Target>
                                    <ActionIcon size="sm">
                                      <EllipsisHorizontalIcon
                                        style={{ width: 16, height: 16 }}
                                      />
                                    </ActionIcon>
                                  </Menu.Target>
                                  <Menu.Dropdown>
                                    <Menu.Item
                                      onClick={() => handleDelete(message)}
                                    >
                                      {t("chat.delete")}
                                    </Menu.Item>
                                  </Menu.Dropdown>
                                </Menu>
                              )}
                          </Group>
                          <Group position="apart" align="flex-end" spacing={8}>
                            <Message
                              message={message as MessageType}
                              content={message.content as any}
                              isMobile={width < 600}
                              i={i}
                            />
                          </Group>
                          <Group position="right">
                            <Text size="xs" color="dimmed">
                              {formatTime(message.createdAt)}{" "}
                              {formatDate(message.createdAt)}
                            </Text>
                          </Group>
                        </Stack>
                      </Paper>
                    </Group>
                  );
                })}
            </Stack>
          </Stack>
        </ScrollArea> */}

        <Stack>
          {(images.length > 0 || files.length > 0) && (
            <Paper
              sx={(theme) => ({
                padding: 12,
                background:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[8]
                    : theme.colors.gray[2],
                boxShadow: `0 -1px 3px rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0px -20px 25px -5px, rgba(0, 0, 0, 0.04) 0px -10px 10px -5px`,
                zIndex: 10,
              })}
            >
              <ScrollArea
                offsetScrollbars
                scrollbarSize={5}
                styles={{
                  viewport: {
                    maxHeight: 170,
                  },
                }}
              >
                {images.length > 0 && (
                  <Text size="xs" weight={600} color="dimmed" mb={8}>
                    {t("images")}
                  </Text>
                )}
                <SimpleGrid
                  cols={
                    width > 1200
                      ? 5
                      : width > 1000
                      ? 4
                      : width > 800
                      ? 3
                      : width > 600
                      ? 2
                      : 1
                  }
                  sx={{ position: "relative" }}
                >
                  {images.map((image: File, i: number) => {
                    const blob = new Blob([image], { type: "image/jpeg" });
                    const blobURL = URL.createObjectURL(blob);

                    return (
                      <Paper key={`image-${i}`} p={8}>
                        <Group noWrap spacing={8}>
                          <Image
                            height={36}
                            width={36}
                            src={blobURL}
                            radius={8}
                            alt="preview"
                            withPlaceholder
                          />
                          <Text
                            size="xs"
                            sx={{
                              width: "100%",
                              whiteSpace: "nowrap",
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                            }}
                          >
                            {image.name}
                          </Text>
                          <CloseButton
                            size="sm"
                            onClick={() =>
                              setImages(
                                images.filter((e, index) => index !== i)
                              )
                            }
                          />
                        </Group>
                      </Paper>
                    );
                  })}
                </SimpleGrid>
                <Space h="xs" />
                {files.length > 0 && (
                  <Text size="xs" weight={600} color="dimmed" mb={8}>
                    {t("files")}
                  </Text>
                )}
                <SimpleGrid
                  cols={
                    width > 1200
                      ? 5
                      : width > 1000
                      ? 4
                      : width > 800
                      ? 3
                      : width > 600
                      ? 2
                      : 1
                  }
                  sx={{ position: "relative" }}
                >
                  {files.map((image: File, i: number) => {
                    return (
                      <Paper key={`image-${i}`} p={8}>
                        <Group noWrap spacing={8}>
                          <ThemeIcon variant="light" size={36}>
                            <DocumentIcon style={{ width: 20, height: 20 }} />
                          </ThemeIcon>
                          <Text
                            size="xs"
                            sx={{
                              width: "100%",
                              whiteSpace: "nowrap",
                              textOverflow: "ellipsis",
                              overflow: "hidden",
                            }}
                          >
                            {image.name}
                          </Text>
                          <CloseButton
                            size="sm"
                            onClick={() =>
                              setFiles(files.filter((e, index) => index !== i))
                            }
                          />
                        </Group>
                      </Paper>
                    );
                  })}
                </SimpleGrid>
              </ScrollArea>
            </Paper>
          )}
          <Group
            align="flex-start"
            sx={{ flexWrap: "nowrap", position: "relative" }}
          >
            {isLoading && loadingLevel === 4 && (
              <Box
                sx={{
                  position: "absolute",
                  top: -32,
                  right: 12,
                }}
              >
                <Loader size="xs" />
              </Box>
            )}
            <Textarea
              ref={inputRef}
              defaultValue={comment}
              onChange={(event) => setComment(event.target.value)}
              sx={{ width: "100%" }}
              placeholder={t("taskViewModal.yourComment")}
              size="sm"
              autosize
              minRows={2}
              maxRows={4}
              onKeyDown={getHotkeyHandler([["Enter", () => sendComment()]])}
            />
          </Group>
          <Group position="apart">
            <Group>
              <FileButton
                multiple
                onChange={(files) => handleSelect("files", files)}
                accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.zip,.rar"
              >
                {(props) => (
                  <Button
                    {...props}
                    variant="light"
                    color="gray"
                    leftIcon={
                      <PaperClipIcon style={{ width: 16, height: 16 }} />
                    }
                  >
                    <Text color="dimmed">{t("taskViewModal.attachment")}</Text>
                  </Button>
                )}
              </FileButton>
              <FileButton
                multiple
                onChange={(images) => handleSelect("images", images)}
                accept="image/png,image/jpeg,image/webp"
              >
                {(props) => (
                  <Button
                    {...props}
                    variant="light"
                    color="gray"
                    leftIcon={<PhotoIcon style={{ width: 16, height: 16 }} />}
                  >
                    <Text color="dimmed">{t("taskViewModal.image")}</Text>
                  </Button>
                )}
              </FileButton>
              <Button
                variant="light"
                color="gray"
                leftIcon={<QueueListIcon style={{ width: 16, height: 16 }} />}
                onClick={addSubtasks}
              >
                <Text color="dimmed">{t("newTask.newSubtasks")}</Text>
              </Button>
            </Group>

            <Button
              color="blue"
              onClick={() => sendComment()}
              loading={isSending}
              loaderProps={{ size: "xs", type: "bars" }}
            >
              {t("send")}
            </Button>
          </Group>
        </Stack>
      </Stack>
      <Space w="sm" h="sm" />
      <Stack
        sx={(theme) => ({
          flexShrink: 0,
          width: matches ? "100%" : 320,
        })}
        spacing="md"
        justify="space-between"
      >
        <Stack
          sx={(theme) => ({
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[6]
                : theme.colors.gray[2],
            borderRadius: theme.radius.md,
          })}
          p="md"
        >
          <Select
            placeholder={t("taskViewModal.project")}
            itemComponent={SelectItem}
            value={`${taskViewModalData.projectId}`}
            onChange={(value: string) =>
              setTaskViewModalData({
                projectId: +value,
              })
            }
            data={
              projects
                .map((project: ProjectType) => {
                  let lastOffer = offers.find(
                    (c) => c.id === project.offerIds.at(-1)
                  );

                  if (!lastOffer) {
                    return null;
                  }

                  let lastRequest = requests.find(
                    (e) => e.id === project.requestId
                  )!;

                  return {
                    value: `${project.id}`,
                    label: lastOffer.name,
                    customer: (
                      customers.find(
                        (c) => c.id === lastRequest?.customerId
                      ) as CustomerType | undefined
                    )?.fullName,
                  };
                })
                .filter((x) => !!x) as any
            }
            transition="fade"
            transitionDuration={200}
            disabled={!canEdit}
          />
          {/* <Stack spacing={8}>
            <Text color="dimmed" size="xs">
              {t("taskViewModal.tags")}
            </Text>
            <Group spacing="xs">
              {taskViewModalData.tags?.map((tag: any, i: number) => (
                <Badge
                  key={"taskTags-" + i}
                  color={tag.color}
                  pr={3}
                  rightSection={
                    <ActionIcon size="xs" onClick={() => removeTagFromTask(i)}>
                      <XMarkIcon style={{ width: 10, height: 10 }} />
                    </ActionIcon>
                  }
                >
                  {tag.label}
                </Badge>
              ))}
              <Menu shadow="md" width={150} position="bottom-end">
                <Menu.Target>
                  <ActionIcon variant="light" size="xs">
                    <PlusSmallIcon style={{ width: 16, height: 16 }} />
                  </ActionIcon>
                </Menu.Target>

                <Menu.Dropdown>
                  {attachableTags.map((tag: TaskTagType, i: number) => (
                    <Menu.Item
                      onClick={() => addTagToTask(tag)}
                      key={"tag-" + i}
                    >
                      {tag.label}
                    </Menu.Item>
                  ))}
                  {attachableTags.length > 0 && <Divider my={4} />}
                  <Menu.Item onClick={() => setIsOpenNewTagModal(true)}>
                    {t("taskViewModal.newTag")}
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Group>
          </Stack> */}
          <Divider />
          <Stack align="flex-start" spacing="sm">
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.status")}
              </Text>
              <Group spacing={4}>
                <Badge color={statusColors[taskViewModalData.status!]}>
                  {t(`status.${taskViewModalData.status}`)}
                </Badge>
                {canEdit && (
                  <Menu shadow="md" width={150} position="bottom-end">
                    <Menu.Target>
                      <ActionIcon>
                        <ChevronDownIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ status: "To Do" })
                        }
                      >
                        {t("status.To Do")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ status: "Active" })
                        }
                      >
                        {t("status.Active")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ status: "Completed" })
                        }
                      >
                        {t("status.Completed")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ status: "Problems" })
                        }
                      >
                        {t("status.Problems")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ status: "Archived" })
                        }
                      >
                        {t("status.Archived")}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                )}
              </Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.priority")}
              </Text>
              <Group spacing={4}>
                <Badge color={priorityColors[taskViewModalData.priority!]}>
                  {t(`priority.${taskViewModalData.priority}`)}
                </Badge>
                {canEdit && (
                  <Menu shadow="md" width={150} position="bottom-end">
                    <Menu.Target>
                      <ActionIcon>
                        <ChevronDownIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ priority: "Very Low" })
                        }
                      >
                        {t("priority.Very Low")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ priority: "Low" })
                        }
                      >
                        {t("priority.Low")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ priority: "Medium" })
                        }
                      >
                        {t("priority.Medium")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ priority: "High" })
                        }
                      >
                        {t("priority.High")}
                      </Menu.Item>
                      <Menu.Item
                        onClick={() =>
                          setTaskViewModalData({ priority: "Very High" })
                        }
                      >
                        {t("priority.Very High")}
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                )}
              </Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.assignees")}
              </Text>
              <Avatar.Group spacing="sm">
                {taskViewModalData.assigneeIds?.map(
                  (assigneeId: number, i: number) => {
                    const activeUser = users.find((e) => e.id === assigneeId);

                    if (!activeUser) {
                      return null;
                    }

                    return (
                      <Tooltip
                        key={"assignee-" + i}
                        label={`${activeUser.name} ${activeUser.surname}`}
                        position="bottom-start"
                      >
                        <ActiveAvatar
                          userId={assigneeId}
                          size="sm"
                          radius="xl"
                        />
                      </Tooltip>
                    );
                  }
                )}
                {canEdit && (
                  <Avatar
                    size="sm"
                    radius="xl"
                    color="green"
                    onClick={() => setIsOpenSelectUserModal(true)}
                  >
                    <ActionIcon color="green">
                      <PlusSmallIcon style={{ width: 12, height: 12 }} />
                    </ActionIcon>
                  </Avatar>
                )}
                <SelectUserModal
                  onesList={taskViewModalData.assigneeIds}
                  handleSelect={(id: number) =>
                    setTaskViewModalData({
                      assigneeIds: [...taskViewModalData.assigneeIds!, id],
                    })
                  }
                  handleRemove={(id) =>
                    setTaskViewModalData({
                      assigneeIds: taskViewModalData.assigneeIds!.filter(
                        (e) => e !== +id
                      ),
                    })
                  }
                />
              </Avatar.Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.contributors")}
              </Text>
              <Avatar.Group spacing="sm">
                {taskViewModalData.contributorIds?.map(
                  (contributorId: number, i: number) => {
                    const activeUser = users.filter(
                      (e) => e.id === contributorId
                    )[0];

                    if (!activeUser) {
                      return null;
                    }

                    return (
                      <Tooltip
                        key={"assignee-" + i}
                        label={`${activeUser.name} ${activeUser.surname}`}
                        position="bottom-start"
                      >
                        <ActiveAvatar
                          userId={contributorId}
                          radius="xl"
                          size="sm"
                        />
                      </Tooltip>
                    );
                  }
                )}
              </Avatar.Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.startDate")}
              </Text>
              <Group spacing={8}>
                <ThemeIcon size="md" variant="light" color="blue">
                  <CalendarDaysIcon style={{ width: 16, height: 16 }} />
                </ThemeIcon>
                <Group spacing={4}>
                  <Text size="sm">{formatDate(taskViewModalData.start!)}</Text>

                  {canEdit && (
                    <Menu shadow="md" position="bottom-end">
                      <Menu.Target>
                        <ActionIcon>
                          <ArrowPathRoundedSquareIcon
                            style={{ width: 16, height: 16 }}
                          />
                        </ActionIcon>
                      </Menu.Target>

                      <Menu.Dropdown>
                        <Calendar
                          value={taskViewModalData.start!}
                          onChange={(value: Date) =>
                            setTaskViewModalData({ start: value })
                          }
                        />
                      </Menu.Dropdown>
                    </Menu>
                  )}
                </Group>
              </Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.endDate")}
              </Text>
              <Group spacing={8}>
                <ThemeIcon size="md" variant="light" color="blue">
                  <CalendarDaysIcon style={{ width: 16, height: 16 }} />
                </ThemeIcon>
                <Group spacing={4}>
                  <Text size="sm">{formatDate(taskViewModalData!.end!)}</Text>

                  {canEdit && (
                    <Menu shadow="md" position="bottom-end">
                      <Menu.Target>
                        <ActionIcon>
                          <ArrowPathRoundedSquareIcon
                            style={{ width: 16, height: 16 }}
                          />
                        </ActionIcon>
                      </Menu.Target>

                      <Menu.Dropdown>
                        <Calendar
                          value={taskViewModalData.end!}
                          onChange={(value: Date) =>
                            setTaskViewModalData({ end: value })
                          }
                        />
                      </Menu.Dropdown>
                    </Menu>
                  )}
                </Group>
              </Group>
            </Group>
            <Group>
              <Text color="dimmed" size="xs" sx={{ width: 70 }}>
                {t("taskViewModal.timeRange")}
              </Text>
              <Group spacing={8}>
                <ThemeIcon size="md" variant="light" color="blue">
                  <ClockIcon style={{ width: 16, height: 16 }} />
                </ThemeIcon>
                <Group spacing={4}>
                  <Text size="sm">
                    {formatTime(taskViewModalData!.start!)} -{" "}
                    {formatTime(taskViewModalData!.end!)}
                  </Text>

                  {canEdit && (
                    <Menu shadow="md" position="bottom-end">
                      <Menu.Target>
                        <ActionIcon>
                          <ArrowPathRoundedSquareIcon
                            style={{ width: 16, height: 16 }}
                          />
                        </ActionIcon>
                      </Menu.Target>

                      <Menu.Dropdown>
                        <TimeRangeInput
                          disabled={isLoading}
                          value={[
                            new Date(taskViewModalData.start),
                            new Date(taskViewModalData.end),
                          ]}
                          onChange={(value) => {
                            const [start, end] = value;

                            const newStartDate = new Date(
                              taskViewModalData.start
                            );
                            const newEndDate = new Date(taskViewModalData.end);

                            if (start) {
                              newStartDate.setHours(start.getHours());
                              newStartDate.setMinutes(start.getMinutes());
                            } else {
                              newStartDate.setHours(0);
                              newStartDate.setMinutes(0);
                            }

                            if (end) {
                              newEndDate.setHours(end.getHours());
                              newEndDate.setMinutes(end.getMinutes());
                            } else {
                              newStartDate.setHours(23);
                              newStartDate.setMinutes(59);
                            }

                            setTaskViewModalData({
                              start: newStartDate,
                              end: newEndDate,
                            });
                          }}
                          clearable
                          styles={{
                            input: {
                              border: "none",
                            },
                          }}
                        />
                      </Menu.Dropdown>
                    </Menu>
                  )}
                </Group>
              </Group>
            </Group>
            {canEdit && (
              <Group noWrap sx={{ flexShrink: 0 }}>
                <Switch
                  label={t("taskViewModal.onlyAssignees")}
                  checked={taskViewModalData.private}
                  onChange={() =>
                    setTaskViewModalData({
                      private: !taskViewModalData.private,
                    })
                  }
                  size="xs"
                />
              </Group>
            )}
            {canEdit && (
              <Group noWrap sx={{ flexShrink: 0 }}>
                <Switch
                  label={t("taskViewModal.hideFromCustomer")}
                  checked={taskViewModalData.customerPrivate}
                  onChange={() =>
                    setTaskViewModalData({
                      customerPrivate: !taskViewModalData.customerPrivate,
                    })
                  }
                  size="xs"
                />
              </Group>
            )}
          </Stack>
        </Stack>
        <Group position="right">
          <Button
            color="red"
            leftIcon={<TrashIcon width={16} height={16} />}
            onClick={() => deleteTask()}
          >
            {t("delete")}
          </Button>
          <Button onClick={() => update()}>{t("Update")}</Button>
        </Group>
      </Stack>
    </Box>
  );
};

const TaskModal = function () {
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    isOpenTaskViewModal,
    setIsOpenTaskViewModal,
    taskViewModalData,
    setTaskViewModalData,
    blankTask,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      isOpenTaskViewModal: state.isOpenTaskViewModal!,
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      taskViewModalData: state.taskViewModalData!,
      setTaskViewModalData: state.setTaskViewModalData!,
      blankTask: state.computed!.blankTask!,
    }),
    shallow
  );
  const { activeUserId, deleteData } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
      deleteData: state.deleteData!,
    }),
    shallow
  );
  const { tasks, filteredTasks, actionUpdateMultipleTask } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      filteredTasks: state.filteredTasks!,
      actionUpdateMultipleTask: state.actionUpdateMultipleTask!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setIsOpenTaskViewModal(false);

    setTimeout(() => {
      setTaskViewModalData(blankTask);
    }, 300);
  }, [
    isLoading,
    loadingLevel,
    setIsOpenTaskViewModal,
    setTaskViewModalData,
    blankTask,
  ]);

  const update = useCallback(async () => {
    const updated = {
      ...taskViewModalData,
    };

    if (taskViewModalData.start && taskViewModalData.end) {
      if (isString(taskViewModalData.start)) {
        updated.start = new Date(taskViewModalData.start);
      }

      if (isString(taskViewModalData.end)) {
        updated.end = new Date(taskViewModalData.end);
      }

      if (updated.start.getTime() > updated.end!.getTime()) {
        updated.start = updated.end;
      }
    }

    if (
      !isEqual(
        taskViewModalData,
        tasks.filter((e) => e.id === taskViewModalData.id)[0]
      )
    ) {
      setIsLoading(true);
      setLoadingLevel(1);

      try {
        await actionUpdateMultipleTask(
          [
            {
              ...updated,
              contributorIds: uniq([...updated.contributorIds, activeUserId]),
            },
          ],
          taskViewModalData.chatId
            ? {}
            : {
                chats: [
                  {
                    type: "task",
                    ownerId: activeUserId,
                    taskId: taskViewModalData.id,
                  },
                ],
              }
        );

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.taskUpdate"),
          autoClose: 3000,
        });
      } finally {
        setIsLoading(false);
        closeModal();
      }
    } else {
      closeModal();
    }
  }, [
    taskViewModalData,
    tasks,
    setIsLoading,
    setLoadingLevel,
    actionUpdateMultipleTask,
    activeUserId,
    t,
    closeModal,
  ]);

  const deleteTask = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await deleteData("tasks", taskViewModalData.id);
    } finally {
      closeModal();
      setIsLoading(false);
    }
  }, [
    closeModal,
    deleteData,
    setIsLoading,
    setLoadingLevel,
    taskViewModalData.id,
  ]);

  return (
    <Modal
      title={t("taskViewModal.title")}
      opened={isOpenTaskViewModal}
      onClose={() => {
        if (isLoading) {
          return;
        }

        if (
          !isEqual(
            omit(taskViewModalData, ["tags"]),
            omit(tasks.filter((e) => e.id === taskViewModalData.id)[0], [
              "tags",
            ])
          )
        ) {
          confirmModal();
        } else {
          closeModal();
        }
      }}
      centered
      transition={isOpenTaskViewModal ? "slide-down" : "slide-up"}
      size={1200}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <TaskViewModalContent update={update} deleteTask={deleteTask} />
    </Modal>
  );
};

export default TaskModal;
