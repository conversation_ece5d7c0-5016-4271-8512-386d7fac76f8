import { useEffect, useState } from "react";

import { Group, Checkbox, Input, ActionIcon } from "@mantine/core";
import { PencilIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { CC } from "~types";
import { useDebouncedState, useDebouncedValue } from "@mantine/hooks";

const Subtask: CC<{
  subtask: {
    label: string;
    completed: boolean;
  };
  ind: number;
  setSubtask: any;
}> = ({ subtask, setSubtask, ind }) => {
  const [label, setLabel] = useState(subtask.label);
  const [debounced] = useDebouncedValue(label, 200);

  useEffect(() => {
    if (label !== subtask.label) {
      setSubtask("setLabel", ind, label);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounced]);

  return (
    <Group position="apart" spacing="sm" noWrap>
      <Group sx={{ width: "100%" }} noWrap>
        <Checkbox
          size="sm"
          checked={subtask.completed}
          onChange={(event) =>
            setSubtask("toggle", ind, event.currentTarget.checked)
          }
        />
        <Input
          icon={<PencilIcon style={{ width: 14, height: 14 }} />}
          value={label}
          onChange={(event: any) => setLabel(event.target.value)}
          size="xs"
          styles={(theme) => ({
            wrapper: {
              width: "100%",
            },
            input: {
              background: "transparent",
              borderColor: "transparent",
              paddingLeft: 0,
              fontSize: 14,
              transition: "padding .2s ease, border-color .2s ease",
              textDecoration: subtask.completed ? "line-through" : "",
              ":focus": {
                borderColor:
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[4]
                    : theme.colors.gray[4],
              },
            },
          })}
        />
      </Group>
      <ActionIcon
        color="red"
        variant="subtle"
        onClick={() => setSubtask("delete", ind)}
      >
        <XMarkIcon style={{ width: 16, height: 16 }} />
      </ActionIcon>
    </Group>
  );
};

export default Subtask;
