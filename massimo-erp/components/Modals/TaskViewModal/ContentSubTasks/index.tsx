import { useCallback, useEffect, useState } from "react";
import { TaskSubtaskType, TaskType } from "~utils/types/Task";

import {
  Box,
  Group,
  ActionIcon,
  Title,
  Tooltip,
  Text,
  Progress,
  Avatar,
  Divider,
  Collapse,
  Stack,
  Space,
  Input,
  Menu,
  List,
  Checkbox,
  ThemeIcon,
} from "@mantine/core";
import {
  Bars3BottomRightIcon,
  CalendarIcon,
  CheckCircleIcon,
  ChevronDownIcon,
  PencilIcon,
  PlusSmallIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { formatDate } from "~utils/tools";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { cloneDeep } from "lodash";
import { getHotkeyHandler } from "@mantine/hooks";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import { MessageType } from "~utils/types/Chat";
import Subtask from "./Subtask";
import { RangeCalendar } from "@mantine/dates";

interface PropType {
  message: MessageType;
  content: any;
  isMobile: boolean;
  i: number;
  chatId?: number;
}

const ContentSubTasks = function (props: PropType) {
  const { message, content, isMobile, i, chatId } = props;
  const {
    setIsLoading,
    setLoadingLevel,
    taskViewModalData,
    setTaskViewModalData,
  } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      taskViewModalData: state.taskViewModalData!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { actionUpdateMessage } = useStore(
    "chat",
    (state) => ({
      actionUpdateMessage: state.actionUpdateMessage!,
    }),
    shallow
  );

  const [subtasksCollapsed, setSubtasksCollapsed] = useState(true);
  const [progress, setProgress] = useState(0);
  const [newSubtask, setNewSubtask] = useState("");

  useEffect(() => {
    let totalSubtask = 0;
    let completedSubtask = 0;

    content.subtasks.map((st: TaskSubtaskType) => {
      totalSubtask++;
      if (st.completed) {
        completedSubtask++;
      }
    });

    const ratio = completedSubtask / totalSubtask;

    if (isNaN(ratio)) {
      setProgress(0);
    } else {
      setProgress(+(ratio * 100).toFixed(2));
    }
  }, [content]);

  const setSubtask = useCallback(
    async (
      event: "setLabel" | "delete" | "toggle" | "add" | "setDate",
      i?: number,
      value?: any
    ) => {
      setIsLoading(true);
      setLoadingLevel(4);

      const updatedMessage = cloneDeep(message);
      updatedMessage.createdAt = new Date(updatedMessage.createdAt);

      if (event === "setLabel" && typeof i === "number") {
        updatedMessage.content.subtasks[i].label = value;
      } else if (event === "delete") {
        updatedMessage.content.subtasks.splice(i, 1);
      } else if (event === "toggle" && typeof i === "number") {
        updatedMessage.content.subtasks[i].completed = value;
      } else if (event === "add") {
        updatedMessage.content.subtasks.push({
          id: updatedMessage.content.subtasks.length,
          label: newSubtask,
          completed: false,
        });
      }
      //  else if (event === "setDate") {
      //   // updatedMessage.content.start = value[0];
      //   // updatedMessage.content.end = value[1];
      // }

      try {
        await actionUpdateMessage(updatedMessage);
      } finally {
        if (event === "add") {
          setNewSubtask("");
        }
        setIsLoading(false);
      }
    },
    [actionUpdateMessage, message, newSubtask, setIsLoading, setLoadingLevel]
  );

  return (
    <Box
      sx={(theme) => ({
        minWidth: isMobile ? "unset" : 500,
        width: "100%",
      })}
      my="xs"
    >
      <Group position="apart">
        <Group>
          <ActionIcon
            onClick={() => setSubtasksCollapsed(!subtasksCollapsed)}
            size={24}
          >
            <ChevronDownIcon
              style={{
                width: 16,
                height: 16,
                transform: subtasksCollapsed ? "rotate(180deg)" : "",
                transition: "transform .2s ease",
              }}
            />
          </ActionIcon>
          <Title order={6}>Sub-Tasks</Title>
          <Tooltip label={progress + "%"}>
            <Progress
              color="green"
              size="sm"
              radius="md"
              value={progress}
              sx={{ width: 70 }}
            />
          </Tooltip>
        </Group>
        <Group spacing={8} align="center">
          {content.contributors.length > 0 && (
            <>
              <Text color="dimmed" size="xs">
                Contributors
              </Text>
              <Avatar.Group spacing="sm">
                {content.contributors?.map((userId: number, i: number) => {
                  const activeUser = users.find(
                    (e) => e.id === userId
                  ) as UserType;

                  return (
                    <Tooltip
                      key={"user-" + i}
                      label={activeUser.name}
                      position="top-start"
                    >
                      <ActiveAvatar userId={userId} radius="xl" size="sm" />
                    </Tooltip>
                  );
                })}
              </Avatar.Group>
              <Divider orientation="vertical" mx="xs" my={4} />
            </>
          )}
          {/* <Text color="dimmed" size="xs">
            {formatDate(content.start)} - {formatDate(content.end)}
          </Text>
          <Menu zIndex={10000}>
            <Menu.Target>
              <ActionIcon color="blue" variant="light">
                <CalendarIcon style={{ width: 16, height: 16 }} />
              </ActionIcon>
            </Menu.Target>

            <Menu.Dropdown>
              <RangeCalendar
                value={[content.start, content.end] as [Date, Date]}
                onChange={(value: Date[]) => {
                  setSubtask("setDate", undefined, value);
                }}
              />
            </Menu.Dropdown>
          </Menu> */}
          {/* <Divider orientation="vertical" mx="xs" my={4} />
          <ActionIcon color="dark" onClick={() => deleteContent()}>
            <TrashIcon style={{ width: 16, height: 16 }} />
          </ActionIcon> */}
        </Group>
      </Group>
      <Collapse in={subtasksCollapsed}>
        <Space h="sm" />
        <Stack>
          <Stack spacing="xs">
            {content.subtasks.map(
              (
                subtask: {
                  label: string;
                  completed: boolean;
                },
                ind: number
              ) => (
                <Subtask
                  key={`subtask-${ind}`}
                  subtask={subtask}
                  ind={ind}
                  setSubtask={setSubtask}
                />
              )
            )}
          </Stack>
          <Box style={{ display: "flex" }}>
            <Input
              size="xs"
              placeholder="New Subtask Label"
              sx={{ flexGrow: 1 }}
              value={newSubtask}
              onChange={(event: any) => setNewSubtask(event.target.value)}
              onKeyDown={getHotkeyHandler([["Enter", () => setSubtask("add")]])}
            />
            <Space w="sm" />
            <ActionIcon
              color="green"
              variant="light"
              onClick={() => setSubtask("add")}
            >
              <PlusSmallIcon />
            </ActionIcon>
          </Box>
        </Stack>
      </Collapse>
    </Box>
  );
};

export default ContentSubTasks;
