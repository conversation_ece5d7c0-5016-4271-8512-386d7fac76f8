import { useState } from "react";
import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { RichTextEditor, Link } from "@mantine/tiptap";
import { useEditor } from "@tiptap/react";
import Highlight from "@tiptap/extension-highlight";
import StarterKit from "@tiptap/starter-kit";
import Underline from "@tiptap/extension-underline";
import TextAlign from "@tiptap/extension-text-align";
import Superscript from "@tiptap/extension-superscript";
import SubScript from "@tiptap/extension-subscript";

import {
  Tabs,
  SegmentedControl,
  TypographyStylesProvider,
  Stack,
  Text,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { CC } from "~types";

const DynamicTextEditor = dynamic(() => import("@mantine/rte"), {
  ssr: false,
});

const DescriptionArea: CC<{
  canEdit: boolean;
}> = function ({ canEdit }) {
  const { taskViewModalData, setTaskViewModalData } = useStore(
    "temp",
    (state) => ({
      taskViewModalData: state.taskViewModalData!,
      setTaskViewModalData: state.setTaskViewModalData!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const [tabValue, setTabValue] = useState("preview");

  const editor = useEditor({
    extensions: [
      StarterKit,
      Underline,
      Link,
      Superscript,
      SubScript,
      Highlight,
      TextAlign.configure({ types: ["heading", "paragraph"] }),
    ],
    content: taskViewModalData.description,
    onUpdate: (e) => setTaskViewModalData({ description: e.editor.getHTML() }),
  });

  return canEdit ? (
    <Tabs variant="pills" defaultValue="preview" value={tabValue} radius="md">
      <SegmentedControl
        size="xs"
        color="blue"
        value={tabValue}
        onChange={(value: string) => setTabValue(value)}
        data={[
          { label: t("taskViewModal.description"), value: "description" },
          { label: t("taskViewModal.preview"), value: "preview" },
        ]}
      />

      <Tabs.Panel value="description" mt="md" sx={{ overflow: "visible" }}>
        {/* <DynamicTextEditor
          value={taskViewModalData.description}
          onChange={(value: string) =>
            setTaskViewModalData({ description: value })
          }
        /> */}
        <RichTextEditor editor={editor}>
          <RichTextEditor.Toolbar sticky stickyOffset={60}>
            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Bold />
              <RichTextEditor.Italic />
              <RichTextEditor.Underline />
              <RichTextEditor.Strikethrough />
              <RichTextEditor.ClearFormatting />
              <RichTextEditor.Highlight />
              <RichTextEditor.Code />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.H1 />
              <RichTextEditor.H2 />
              <RichTextEditor.H3 />
              <RichTextEditor.H4 />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Blockquote />
              <RichTextEditor.Hr />
              <RichTextEditor.BulletList />
              <RichTextEditor.OrderedList />
              <RichTextEditor.Subscript />
              <RichTextEditor.Superscript />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.Link />
              <RichTextEditor.Unlink />
            </RichTextEditor.ControlsGroup>

            <RichTextEditor.ControlsGroup>
              <RichTextEditor.AlignLeft />
              <RichTextEditor.AlignCenter />
              <RichTextEditor.AlignJustify />
              <RichTextEditor.AlignRight />
            </RichTextEditor.ControlsGroup>
          </RichTextEditor.Toolbar>

          <RichTextEditor.Content />
        </RichTextEditor>
      </Tabs.Panel>

      <Tabs.Panel value="preview" pt="xs">
        <TypographyStylesProvider>
          <div
            dangerouslySetInnerHTML={{
              __html: taskViewModalData.description!,
            }}
            className="preview"
          />
        </TypographyStylesProvider>
      </Tabs.Panel>
    </Tabs>
  ) : (
    <TypographyStylesProvider>
      <div
        dangerouslySetInnerHTML={{
          __html: taskViewModalData.description!,
        }}
        className="preview"
      />
    </TypographyStylesProvider>
  );
};

export default DescriptionArea;
