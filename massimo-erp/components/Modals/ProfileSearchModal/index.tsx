import {
  MagnifyingGlassIcon,
  UserGroupIcon,
} from "@heroicons/react/24/outline";
import {
  Avatar,
  Badge,
  Divider,
  Group,
  Input,
  LoadingOverlay,
  Modal,
  Paper,
  SimpleGrid,
  Stack,
  Text,
  Tooltip,
} from "@mantine/core";
import {
  getHotkey<PERSON>and<PERSON>,
  useDebouncedState,
  useElementSize,
} from "@mantine/hooks";
import { orderBy } from "lodash";
import { useTranslation } from "next-i18next";
import { useRouter } from "next/router";
import React, { useCallback, useRef, useState } from "react";
import shallow from "zustand/shallow";
import ActiveAvatar from "~components/ActiveAvatar";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { CustomerType } from "~utils/types/Customer";
import { DepartmentType } from "~utils/types/Department";
import { UserType } from "~utils/types/User";

const ModalContent: CC<{
  closeModal: () => void;
}> = ({ closeModal }) => {
  const { setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { users, filteredUsers, actionResetUsers, actionGetUsers } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      filteredUsers: state.filteredUsers!,
      actionResetUsers: state.actionResetUsers!,
      actionGetUsers: state.actionGetUsers!,
    }),
    shallow
  );
  const {
    customers,
    filteredCustomers,
    actionResetCustomers,
    actionGetCustomers,
  } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
      filteredCustomers: state.filteredCustomers!,
      actionResetCustomers: state.actionResetCustomers!,
      actionGetCustomers: state.actionGetCustomers!,
    }),
    shallow
  );

  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const { ref, width } = useElementSize();
  const { t } = useTranslation();
  const { push } = useRouter();

  const [isFetched, setIsFetched] = useState(false);
  const [search, setSearch] = useDebouncedState("", 200, { leading: true });

  const updateSearch = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      if (search) {
        await Promise.all([
          actionGetUsers(300, 0, search),
          actionGetCustomers(300, 0, search),
        ]);
      } else {
        actionResetUsers();
        actionResetCustomers();
      }
    } finally {
      setIsLoading(false);
      setIsFetched(true);
    }
  }, [
    actionGetCustomers,
    actionGetUsers,
    actionResetCustomers,
    actionResetUsers,
    search,
    setIsLoading,
    setLoadingLevel,
  ]);

  return (
    <Stack ref={ref}>
      <Input
        ref={inputRef as any}
        size="sm"
        icon={<MagnifyingGlassIcon width={16} height={16} />}
        placeholder={t("search")}
        defaultValue={search}
        onChange={(event: any) => setSearch(event.target.value)}
        onKeyDown={getHotkeyHandler([["Enter", updateSearch]])}
      />
      <Stack>
        <Divider label={t("organizations")} labelPosition="left" />
        <SimpleGrid cols={width >= 700 ? 3 : width > 510 ? 2 : 1}>
          {orderBy(
            search.length > 0 && isFetched ? filteredCustomers : customers,
            "id"
          ).map((customer: CustomerType, i: number) => (
            <Tooltip key={`organization-${i}`} label={`${customer.fullName}`}>
              <Paper
                sx={(theme) => ({
                  position: "relative",
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[8]
                      : theme.colors.gray[1],
                  padding: theme.spacing.sm,
                  transition: "filter .2s ease",
                  cursor: "pointer",
                  userSelect: "none",
                  ":hover": {
                    filter: "brightness(.9)",
                  },
                })}
                onClick={() => {
                  closeModal();
                  push({
                    pathname: `/profile/organization/${customer.id}`,
                  });
                }}
              >
                <Group noWrap>
                  <Avatar size="md">
                    <UserGroupIcon style={{ width: 20, height: 20 }} />
                  </Avatar>
                  <Stack
                    spacing={4}
                    sx={{
                      width: "calc(100% - 70px)",
                    }}
                  >
                    <Text
                      size="sm"
                      weight={600}
                      sx={{
                        maxWidth: "100%",
                        lineHeight: 1,
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                      }}
                    >
                      {customer.fullName}
                    </Text>
                    <Text
                      size="xs"
                      weight={600}
                      color="dimmed"
                      sx={{
                        maxWidth: "100%",
                        lineHeight: 1,
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                      }}
                    >
                      {customer.shortName}
                    </Text>
                  </Stack>
                </Group>
              </Paper>
            </Tooltip>
          ))}
        </SimpleGrid>
      </Stack>
      <Stack>
        <Divider label={t("users")} labelPosition="left" />
        <SimpleGrid cols={width >= 700 ? 3 : width > 510 ? 2 : 1}>
          {orderBy(
            search.length > 0 && isFetched ? filteredUsers : users,
            "id"
          ).map((user: UserType, i: number) => {
            const defaultRole = roles.find(
              (e) => e.id === user.defaultRoleId[0]
            )?.label;

            return (
              <Tooltip key={`user-${i}`} label={`${user.name} ${user.surname}`}>
                <Paper
                  sx={(theme) => ({
                    position: "relative",
                    background:
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[8]
                        : theme.colors.gray[1],
                    padding: theme.spacing.sm,
                    transition: "filter .2s ease",
                    cursor: "pointer",
                    userSelect: "none",
                    ":hover": {
                      filter: "brightness(.9)",
                    },
                  })}
                  onClick={() => {
                    closeModal();
                    push({
                      pathname: `/profile/user/${user.id}`,
                    });
                  }}
                >
                  <Stack spacing="xs">
                    <Group>
                      <ActiveAvatar userId={user.id} size="md" radius="xl" />
                      <Stack
                        spacing={0}
                        sx={{
                          width: "calc(100% - 70px)",
                        }}
                      >
                        <Text
                          size="sm"
                          weight={600}
                          sx={{
                            maxWidth: "100%",
                            lineHeight: 1,
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                        >
                          {user.name} {user.surname}
                        </Text>
                        <Text size="xs" weight={600} color="dimmed">
                          {!!defaultRole &&
                            t(`defaultRoles.${defaultRole.toLowerCase()}`)}
                        </Text>
                      </Stack>
                    </Group>
                  </Stack>
                </Paper>
              </Tooltip>
            );
          })}
        </SimpleGrid>
      </Stack>
    </Stack>
  );
};

const ProfileSearchModal = () => {
  const { isLoading, loadingLevel, profileSearchModal, setProfileSearchModal } =
    useStore(
      "temp",
      (state) => ({
        isLoading: state.isLoading!,
        loadingLevel: state.loadingLevel!,
        profileSearchModal: state.profileSearchModal!,
        setProfileSearchModal: state.setProfileSearchModal!,
      }),
      shallow
    );

  const { t } = useTranslation();

  const closeModal = useCallback(() => {
    setProfileSearchModal({
      isOpen: false,
    });
  }, [setProfileSearchModal]);

  return (
    <Modal
      title={t("profileSearch")}
      opened={profileSearchModal.isOpen}
      onClose={() => closeModal()}
      centered
      transition={profileSearchModal.isOpen ? "slide-down" : "slide-up"}
      size={800}
      zIndex={2000}
      overflow="inside"
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        radius="md"
      />

      <ModalContent closeModal={closeModal} />
    </Modal>
  );
};

export default ProfileSearchModal;
