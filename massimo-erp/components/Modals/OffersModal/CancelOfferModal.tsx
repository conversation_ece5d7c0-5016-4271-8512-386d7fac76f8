import {
  <PERSON><PERSON>,
  <PERSON><PERSON>r,
  Group,
  Modal,
  Stack,
  Textarea,
  LoadingOverlay,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

interface PropType {
  isOpen: boolean;
  close: () => void;
  cancelOffer: (reason: string) => void;
}

export default function CancelOfferModal(props: PropType) {
  const { isLoading, setIsLoading, loadingLevel, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { isOpen, close, cancelOffer } = props;

  const [reason, setReason] = useState("");
  const { t } = useTranslation();

  const closeModal = useCallback(() => {
    close();
    setReason("");
  }, [close]);

  const cancel = useCallback(() => {
    setIsLoading(true);
    setLoadingLevel(2);

    setTimeout(() => {
      setIsLoading(false);
      cancelOffer(reason);
      closeModal();
    }, 1000);
  }, [cancelOffer, closeModal, reason, setIsLoading, setLoadingLevel]);

  return (
    <Modal
      opened={isOpen}
      onClose={() => close()}
      title={t("cancelOffer")}
      centered
      transition={isOpen ? "slide-down" : "slide-up"}
      size={500}
      zIndex={1020}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 2}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md
        })}
      />
      <Stack>
        <Textarea
          placeholder={t("cancelReason")}
          autosize
          minRows={3}
          maxRows={5}
          value={reason}
          onChange={(event) => setReason(event.target.value)}
        />
        <Divider />
        <Group position="right">
          <Button variant="light" color="gray" onClick={() => close()}>
            {t("cancel")}
          </Button>
          <Button color="red" onClick={() => cancel()}>
            {t("cancelOffer")}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
