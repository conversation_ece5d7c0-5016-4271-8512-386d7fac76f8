import {
  Box,
  Stack,
  Text,
  Divider,
  Group,
  Input,
  NumberInput,
  Textarea,
  Button,
} from "@mantine/core";
import { DatePicker } from "@mantine/dates";
import { useSetState } from "@mantine/hooks";
import { isEqual } from "lodash";
import { useTranslation } from "next-i18next";
import { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate, numberFormatter } from "~utils/tools";

interface PropType {
  requestId: any;
}

export default function ContentRequest(props: PropType) {
  const { requestId } = props;

  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );

  const activeRequest = requests.filter((e) => e.id === requestId)[0];

  const { t } = useTranslation();

  return (
    <Box
      sx={(theme) => ({
        width: "100%",
        borderWidth: 2,
        borderStyle: "solid",
        borderColor:
          theme.colorScheme === "dark"
            ? theme.colors.dark[6]
            : theme.colors.gray[2],
        borderRadius: 8,
        padding: theme.spacing.md,
      })}
      mb="xs"
    >
      <Stack spacing={8}>
        <Text size="lg" weight={600}>
          {activeRequest?.name}
        </Text>
        <Text size="md">{activeRequest?.description}</Text>
        <Divider />
        <Group position="right">
          <Group spacing={8} align="baseline">
            <Text size="xs" color="dimmed">
              {t("offersModal.dateRange")}
            </Text>
            <Text weight={600}>
              {formatDate(new Date(activeRequest?.start))} -{" "}
              {formatDate(new Date(activeRequest?.end))}
            </Text>
          </Group>
          <Group spacing={8} align="baseline">
            <Text size="xs" color="dimmed">
              {t("budget")}
            </Text>
            <Text weight={600}>{numberFormatter(activeRequest?.budget)}</Text>
          </Group>
        </Group>
      </Stack>
    </Box>
  );
}
