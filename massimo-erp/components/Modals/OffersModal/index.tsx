import { useCallback, useEffect, useRef, useState } from "react";

import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import {
  cloneDeep,
  flattenDepth,
  isDate,
  isEqual,
  isNull,
  isNumber,
  isObject,
  last,
  merge,
  omit,
  orderBy,
  uniq,
} from "lodash";

import { openConfirmModal } from "@mantine/modals";
import {
  Modal,
  Text,
  Group,
  Box,
  ScrollArea,
  Stack,
  Button,
  Space,
  Avatar,
  Paper,
  Textarea,
  CloseButton,
  SegmentedControl,
  Tooltip,
  Tabs,
  Grid,
  Input,
  Select,
  MultiSelect,
  Divider,
  SimpleGrid,
  ActionIcon,
  LoadingOverlay,
  useMantineTheme,
  Menu,
  FileButton,
  ThemeIcon,
  Image,
  Loader,
  Table,
} from "@mantine/core";
import {
  getHotkeyHandler,
  useDebouncedState,
  useElementSize,
  useForceUpdate,
  useMediaQuery,
} from "@mantine/hooks";
import {
  ArrowPathIcon,
  CheckIcon,
  ClipboardDocumentListIcon,
  CubeIcon,
  DocumentIcon,
  EllipsisHorizontalIcon,
  PaperClipIcon,
  PhotoIcon,
  PlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { formatDate, formatTime } from "~utils/tools";
import { DateRangePicker, TimeRangeInput } from "@mantine/dates";
import ContentPrice from "./ContentPrice";
import GiveAQuoteModal from "./GiveAQuoteModal";
import {
  OfferPriorityType,
  OfferType,
  RequestType,
} from "~utils/types/Project";
import ContentRequest from "./ContentRequest";
import OfferInformations from "./OfferInformations";
import { DepartmentType } from "~utils/types/Department";
import { CustomerType } from "~utils/types/Customer";
import CancelOfferModal from "./CancelOfferModal";
import ContentCancelled from "./ContentCancelled";
import SelectUserModal from "../SelectUserModal";
import { UserType } from "~utils/types/User";
import { useTranslation } from "next-i18next";
import ActiveAvatar from "~components/ActiveAvatar";
import { RoleType } from "~utils/types/Roles";
import { ChatType, MessageType } from "~utils/types/Chat";
import TextMessage from "~components/Apps/Chat/MessageType/Text";
import FileMessage from "~components/Apps/Chat/MessageType/File";
import { showNotification } from "@mantine/notifications";
import ImageMessage from "~components/Apps/Chat/MessageType/Image";
import ContentRevised from "./ContentRevised";
import { TaskType } from "~utils/types/Task";
import TasksTab from "./TasksTab";
import { CC } from "~types";
import CustomInfiniteScroll from "~components/SharedComponents/InfiniteScroll";
import { NotificationType } from "~utils/types/Notification";

const prioritys = ["Very Low", "Low", "Medium", "High", "Very High"];

const MessageTypes = {
  text: TextMessage,
  image: ImageMessage,
  file: FileMessage,
  price: ContentPrice,
};

const DynamicOffersTab: CC<{
  activeTab: string;
  setActiveTab: (value: string) => void;
  isEditable?: boolean;
  isViewable?: boolean;
}> = function ({ activeTab, setActiveTab, isEditable, isViewable }) {
  const { offers, offerStatusColors } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
      offerStatusColors: state.offerStatusColors!,
    }),
    shallow
  );
  const { projectModal } = useStore(
    "temp",
    (state) => ({
      projectModal: state.projectModal!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const theme = useMantineTheme();

  return (
    <SegmentedControl
      value={activeTab}
      onChange={(value: string) => setActiveTab(value)}
      color={
        activeTab === "description" ||
        activeTab === "invoices" ||
        activeTab === "tasks"
          ? "blue"
          : offerStatusColors[
              offers.filter((e) => e.id === +activeTab)[0].status
            ]
      }
      styles={{
        root: {
          overflow: "visible",
        },
        active: {
          opacity: 0.2,
        },
      }}
      data={[
        {
          label: t("offersModal.description"),
          value: "description",
        },
        ...(isEditable
          ? [
              {
                label: t("offersModal.invoices"),
                value: "invoices",
              },
            ]
          : []),
        ...(isViewable
          ? [
              {
                label: t("offersModal.tasks"),
                value: "tasks",
              },
            ]
          : []),
        ...orderBy(
          offers.filter((e) => projectModal.data.offerIds.includes(e.id)),
          "id"
        ).map((e, i) => ({
          label: (
            <Tooltip
              label={t(`offersModal.statuses.${e.status}`)}
              openDelay={300}
            >
              <Group spacing={8} noWrap>
                {e.status === "Offer" ? (
                  <CubeIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors[offerStatusColors[e.status]][9],
                    }}
                  />
                ) : e.status === "Canceled" ? (
                  <XMarkIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors[offerStatusColors[e.status]][9],
                    }}
                  />
                ) : e.status === "Revised" ? (
                  <ArrowPathIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors[offerStatusColors[e.status]][9],
                    }}
                  />
                ) : (
                  <CheckIcon
                    style={{
                      width: 16,
                      height: 16,
                      color: theme.colors[offerStatusColors[e.status]][9],
                    }}
                  />
                )}
                {i + 1}
              </Group>
            </Tooltip>
          ),
          value: `${e.id}`,
        })),
      ]}
    />
  );
};

const OffersModal = () => {
  const { activeUserId, deleteData } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
      deleteData: state.deleteData!,
    }),
    shallow
  );
  const { offers, setOffer } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
      setOffer: state.setOffer!,
    }),
    shallow
  );
  const { departments, filteredDepartments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
      filteredDepartments: state.filteredDepartments!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { actionUpdateMultipleProjects, actionGetProjects } = useStore(
    "projects",
    (state) => ({
      actionUpdateMultipleProjects: state.actionUpdateMultipleProjects!,
      actionGetProjects: state.actionGetProjects!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    lastOffer,
    setLastOffer,
    projectModal,
    setProjectModal,
    setIsOpenSelectUserModal,
    blankProject,
    blankOffer,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      projectModal: state.projectModal!,
      setProjectModal: state.setProjectModal!,
      lastOffer: state.lastOffer!,
      setLastOffer: state.setLastOffer!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
      blankProject: state.computed!.blankProject!,
      blankOffer: state.computed!.blankOffer!,
    }),
    shallow
  );
  const {
    chats,
    messages,
    actionInitChat,
    actionDeleteMessage,
    actionSendMessage,
    actionGetMessages,
    actionUpdateMessage,
  } = useStore(
    "chat",
    (state) => ({
      chats: state.chats!,
      subscribedChats: state.subscribedChats!,
      messages: state.messages!,
      actionSendMessage: state.actionSendMessage!,
      actionDeleteMessage: state.actionDeleteMessage!,
      actionInitChat: state.actionInitChat!,
      actionGetMessages: state.actionGetMessages!,
      actionUpdateMessage: state.actionUpdateMessage!,
    }),
    shallow
  );
  const { actionUploadFiles } = useStore(
    "files",
    (state) => ({
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );
  const { notifications, actionViewNotification, actionSendNotifications } =
    useStore(
      "notifications",
      (state) => ({
        notifications: state.notifications!,
        actionViewNotification: state.actionViewNotification!,
        actionSendNotifications: state.actionSendNotifications!,
      }),
      shallow
    );

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRoleWeight = roles.find(
    (e: RoleType) => e.id === activeUser?.defaultRoleId[0]
  )?.weight;

  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  const inputRef = useRef<HTMLTextAreaElement | null>(null);
  const viewport = useRef<HTMLDivElement>();

  const [scrollPosition, setScrollPositionChange] = useDebouncedState(
    { x: 0, y: 0 },
    400
  );

  const forceUpdate = useForceUpdate();

  const [isSending, setIsSending] = useState(false);
  const [initChat, setInitChat] = useState(false);
  const [isFetching, setIsFetching] = useState(false);

  const [files, setFiles] = useState<File[]>([]);
  const [images, setImages] = useState<File[]>([]);
  const [page, setPage] = useState(0);

  const [isOpenCancelOffer, setIsOpenCancelOffer] = useState(false);

  const [text, setText] = useDebouncedState("", 200, {
    leading: true,
  });
  const [isOpenGiveAQuoteModal, setGiveAQuoteModal] = useState(false);
  const [activeTab, setActiveTab] = useState("description");
  const [userModalType, setUserModalType] = useState<"allowed" | "restricted">(
    "allowed"
  );

  const [hasMore, setHasMore] = useState(true);

  const activeChat = chats.find(
    (e: ChatType) => e.type === "offer" && e.offerId === +activeTab
  );

  const ownMessages = messages.filter((e) => {
    return !isNull(e) && e.chatId === activeChat?.id;
  });

  const fetchMoreMessages = useCallback(async () => {
    let currentPage: number;

    setPage((p) => {
      currentPage = p + 1;
      return currentPage;
    });

    try {
      const hasMoreData = await actionGetMessages(
        activeChat?.id as number,
        currentPage!
      );

      setHasMore(hasMoreData);
    } catch (err) {
      throw err;
    }
  }, [actionGetMessages, activeChat]);

  const activeRequest = requests.find(
    (e: RequestType) => e.id === projectModal.data.requestId
  );
  const activeCustomer = customers.find(
    (e: CustomerType) => e.id === activeRequest?.customerId
  );
  const activeDepartments = departments.filter((e) =>
    projectModal.data.departmentIds?.includes(e.id)
  );

  const isEditable =
    (activeUserDefaultRoleWeight || 0) < 0 ||
    (activeCustomer?.personIds.includes(activeUserId) &&
      !projectModal.data.restrictedUserIds.includes(activeUserId));

  const isViewable =
    (activeUserDefaultRoleWeight || 0) < 0 ||
    activeDepartments.some((e) => e.userIds.includes(activeUserId)) ||
    (projectModal.data.allowedUserIds.includes(activeUserId) &&
      !projectModal.data.restrictedUserIds.includes(activeUserId));

  const focusInput = useCallback(() => {
    if (!!inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSelect = useCallback(
    (key: "files" | "images", data: File[]) => {
      const dataList: File[] = [];

      data.forEach((e) => {
        if (e.size >= 10485760) {
          showNotification({
            color: "red",
            title: t("largeFile.title"),
            message: t("largeFile.message"),
            autoClose: 3000,
            styles: {
              root: {
                zIndex: 400,
              },
            },
          });
        } else {
          dataList.push(e);
        }
      });

      if (key === "files") {
        setFiles([...files, ...dataList].slice(0, 20));
      } else {
        setImages([...images, ...dataList].slice(0, 20));
      }
    },
    [files, images, t]
  );

  const handleDelete = useCallback(
    async (messageData: MessageType) => {
      if (!isObject(messageData)) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });

        return;
      }

      setIsSending(true);

      try {
        await actionDeleteMessage(messageData);
      } catch (err) {
        console.error(err);

        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        setIsSending(false);
      }
    },
    [actionDeleteMessage, t]
  );

  const openSelectUserModal = useCallback(
    (type: "allowed" | "restricted") => {
      setUserModalType(type);
      setIsOpenSelectUserModal(true);
    },
    [setIsOpenSelectUserModal, setUserModalType]
  );

  const addUser = useCallback(
    (type: "allowed" | "restricted", id: number) => {
      if (type === "allowed") {
        setProjectModal({
          data: {
            ...projectModal.data,
            restrictedUserIds: projectModal.data.restrictedUserIds.filter(
              (e) => e !== id
            ),
            allowedUserIds: [...projectModal.data.allowedUserIds, id],
          },
        });
      } else if (type === "restricted") {
        setProjectModal({
          data: {
            ...projectModal.data,
            allowedUserIds: projectModal.data.allowedUserIds.filter(
              (e) => e !== id
            ),
            restrictedUserIds: [...projectModal.data.restrictedUserIds, id],
          },
        });
      }
    },
    [projectModal, setProjectModal]
  );

  const removeUser = useCallback(
    (type: "allowed" | "restricted", id: number) => {
      setProjectModal({
        data: {
          ...projectModal.data,
          [`${type}UserIds`]: projectModal.data[`${type}UserIds`].filter(
            (e) => e !== id
          ),
        },
      });
    },
    [projectModal.data, setProjectModal]
  );

  const scrollToBottom = useCallback(
    () =>
      viewport.current?.scrollTo({
        top: viewport.current!.scrollHeight,
        behavior: "smooth",
      }),
    [viewport]
  );

  const sendMessage = useCallback(async () => {
    if (text === "" && images.length === 0 && files.length === 0) {
      return;
    }
    if (!isSending) {
      setIsSending(true);

      let newMessage: Partial<MessageType> = {
        type: "text",
        ownerId: activeUserId,
        chatId: activeChat?.id || -1,
        content: {
          text: text,
        },
      };

      try {
        if (images.length > 0) {
          const imageIds = await actionUploadFiles(
            images.map((imageFile) => ({
              userId: activeUserId,
              file: imageFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            imageIds.map((imageId) => {
              return actionSendMessage({
                type: "image",
                ownerId: activeUserId,
                chatId: activeChat?.id || -1,
                content: {
                  fileId: imageId,
                },
              });
            })
          );
        }

        if (files.length > 0) {
          const fileIds = await actionUploadFiles(
            files.map((fileFile) => ({
              userId: activeUserId,
              file: fileFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            fileIds.map((fileId) => {
              return actionSendMessage({
                type: "file",
                ownerId: activeUserId,
                chatId: activeChat?.id || -1,
                content: {
                  fileId: fileId,
                },
              });
            })
          );
        }

        if (text && text.length > 0) {
          await actionSendMessage(newMessage);
        }

        if (activeChat?.type === "offer") {
          let userIds: number[] = activeCustomer?.personIds || [];

          departments
            .filter((e) => projectModal.data.departmentIds?.includes(e.id))
            .forEach((d) => {
              userIds = userIds.concat(d.userIds);
            });

          userIds = userIds.concat(projectModal.data.allowedUserIds);

          userIds = uniq(
            userIds.filter(
              (e) =>
                !projectModal.data.restrictedUserIds.includes(e) &&
                e !== activeUserId
            )
          );

          await actionSendNotifications(
            userIds.map((e) => ({
              target: "messages",
              value: `${activeChat.id}`,
              user: e,
            }))
          );
        }
      } catch (error) {
        showNotification({
          color: "red",
          title: t("message.fail.title"),
          message: t("message.fail.message"),
          autoClose: 3000,
        });
      } finally {
        scrollToBottom();
        if (inputRef.current) {
          inputRef.current.value = "";
        }
        setFiles([]);
        setImages([]);
        focusInput();
        setIsSending(false);
      }
    }
  }, [
    actionSendMessage,
    actionSendNotifications,
    actionUploadFiles,
    activeChat?.id,
    activeChat?.type,
    activeCustomer?.personIds,
    activeUserId,
    departments,
    files,
    focusInput,
    images,
    isSending,
    projectModal.data,
    scrollToBottom,
    t,
    text,
  ]);

  const sendAQuote = useCallback(
    async (price: number) => {
      if (!price && price === 0) {
        return;
      }

      if (!isSending) {
        setIsSending(true);

        let newMessage: Partial<MessageType> = {
          type: "text",
          ownerId: activeUserId,
          chatId: activeChat?.id || -1,
          content: {
            type: "price",
            price: price,
            result: "",
          },
        };

        try {
          if (price && price > 0) {
            actionSendMessage(newMessage);
          }
        } catch (error) {
          showNotification({
            color: "red",
            title: t("message.fail.title"),
            message: t("message.fail.message"),
            autoClose: 3000,
          });
        } finally {
          scrollToBottom();
          focusInput();
          setIsSending(false);
        }
      }
    },
    [
      actionSendMessage,
      activeChat?.id,
      activeUserId,
      focusInput,
      isSending,
      scrollToBottom,
      t,
    ]
  );

  const setPriceResult = useCallback(
    async (message: MessageType, result: "approved" | "rejected") => {
      const contentLastOffer = offers.filter((e) => e.id === lastOffer.id)[0];

      if (result === "approved") {
        contentLastOffer.budget = message.content.price;

        setOffer(contentLastOffer);
        setLastOffer(contentLastOffer);
      }

      setIsLoading(true);
      setLoadingLevel(1);

      const newMessage = cloneDeep(message);
      newMessage.content.result = result;

      if (!isDate(newMessage.createdAt)) {
        newMessage.createdAt = new Date(newMessage.createdAt);
      }

      try {
        await actionUpdateMessage(newMessage);
        await actionUpdateMultipleProjects([projectModal.data], {
          offers: [contentLastOffer],
        });
      } finally {
        setIsLoading(false);
      }
    },
    [
      actionUpdateMessage,
      actionUpdateMultipleProjects,
      lastOffer.id,
      offers,
      projectModal.data,
      setIsLoading,
      setLastOffer,
      setLoadingLevel,
      setOffer,
    ]
  );

  const cancelOffer = useCallback(
    async (reason: string) => {
      setIsOpenCancelOffer(false);
      setIsLoading(true);
      setLoadingLevel(1);

      const newLastOffer = cloneDeep(
        offers.find((e) => e.id === +activeTab)
      ) as OfferType;

      newLastOffer.status = "Canceled";
      newLastOffer.cancellationReason = reason;

      try {
        setLastOffer(newLastOffer);
        await actionUpdateMultipleProjects([projectModal.data], {
          offers: [newLastOffer],
        });
      } finally {
        setIsLoading(false);
        forceUpdate();
      }
    },
    [
      actionUpdateMultipleProjects,
      activeTab,
      forceUpdate,
      offers,
      projectModal.data,
      setIsLoading,
      setLastOffer,
      setLoadingLevel,
    ]
  );

  const revise = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    const activeOffer = offers.find((e) => e.id === +activeTab) as OfferType;

    const oldLastOffer = cloneDeep(activeOffer);
    oldLastOffer.status = "Revised";

    const newLastOffer = omit(cloneDeep(activeOffer), ["id"]);
    newLastOffer.status = "Offer";

    try {
      const newProject = await actionUpdateMultipleProjects(
        [
          merge(cloneDeep(projectModal.data), {
            offerIds: [...projectModal.data.offerIds, "$offers.-2.id"],
          }),
        ],
        {
          offers: [oldLastOffer, newLastOffer],
          chats: [
            {
              type: "offer",
              ownerId: activeUserId,
              offerId: "$offers.-2.id",
            },
          ],
        }
      );

      await actionGetProjects({
        id: projectModal.data.id,
      });

      // setProjectModal({
      //   data: {
      //     ...projectModal.data,
      //     offerIds: newProject[0].offerIds,
      //   },
      // });
    } finally {
      setIsLoading(false);
    }
  }, [
    actionGetProjects,
    actionUpdateMultipleProjects,
    activeTab,
    activeUserId,
    offers,
    projectModal.data,
    setIsLoading,
    setLoadingLevel,
  ]);

  const complete = useCallback(async () => {
    setIsOpenCancelOffer(false);
    setIsLoading(true);
    setLoadingLevel(1);

    const activeOffer = offers.find((e) => e.id === +activeTab) as OfferType;

    const newLastOffer = cloneDeep(activeOffer);
    newLastOffer.status = "Completed";

    try {
      setLastOffer(newLastOffer);
      await actionUpdateMultipleProjects([projectModal.data], {
        offers: [newLastOffer],
        chats: [],
      });
    } finally {
      setIsLoading(false);
      forceUpdate();
    }
  }, [
    actionUpdateMultipleProjects,
    activeTab,
    forceUpdate,
    offers,
    projectModal.data,
    setIsLoading,
    setLastOffer,
    setLoadingLevel,
  ]);

  const closeModal = useCallback(() => {
    setProjectModal({
      isOpen: false,
    });

    setTimeout(() => {
      setProjectModal({
        data: blankProject,
      });
      setLastOffer(blankOffer);
      setActiveTab("description");
    }, 300);
  }, [blankOffer, blankProject, setLastOffer, setProjectModal]);

  const saveOffer = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    let chats: Record<string, any>[] = [];

    offers
      .filter((e) => projectModal.data.offerIds.includes(e.id))
      .forEach((e) => {
        if (!e.chatId) {
          chats.push({
            type: "offer",
            ownerId: activeUserId,
            offerId: e.id,
          });
        }
      });

    try {
      const changes: any = {
        offers: [lastOffer],
      };

      if (chats.length > 0) {
        changes.chats = chats;
      }

      await actionUpdateMultipleProjects([projectModal.data], changes);
    } finally {
      closeModal();
      setIsLoading(false);
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    offers,
    projectModal.data,
    activeUserId,
    lastOffer,
    actionUpdateMultipleProjects,
    closeModal,
  ]);

  const deleteProject = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await deleteData("projects", projectModal.data.id);
    } finally {
      closeModal();
      setIsLoading(false);
    }
  }, [
    closeModal,
    deleteData,
    projectModal.data.id,
    setIsLoading,
    setLoadingLevel,
  ]);

  const readNotifications = useCallback(async () => {
    await notifications.forEach(async (notification: NotificationType) => {
      if (
        notification.target === "messages" &&
        notification.value === `${activeChat?.id}`
      ) {
        await actionViewNotification([{ id: notification.id }]);
      }
    });
  }, [actionViewNotification, activeChat?.id, notifications]);

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });

  useEffect(() => {
    let abort = false;

    setTimeout(() => {
      if (!abort && projectModal.isOpen) {
        scrollToBottom();
      }
    }, 10);

    return () => {
      abort = true;
    };
  }, [projectModal, scrollToBottom]);

  useEffect(() => {
    focusInput();
    setInitChat(true);
    actionInitChat(activeChat?.id || -1).then(() => {
      if (viewport.current) {
        setTimeout(() => {
          scrollToBottom();
          setTimeout(() => {
            setInitChat(false);
          }, 10);
        }, 100);
      }

      if (ownMessages.length > 10) {
        setHasMore(true);
      } else {
        setHasMore(false);
      }
      setPage(0);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeChat]);

  useEffect(() => {
    setText("");
    setFiles([]);
    setImages([]);
    setPage(0);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectModal.isOpen]);

  useEffect(() => {
    if (projectModal.isOpen) {
      readNotifications();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [projectModal.isOpen, activeChat]);

  return (
    <Modal
      opened={projectModal.isOpen}
      onClose={() => {
        if (isLoading && loadingLevel === 1) {
          return;
        }

        if (!isEqual(projectModal.data, blankOffer)) {
          closeModal();
        } else {
          confirmModal();
        }
      }}
      withCloseButton={false}
      centered
      transition={projectModal.isOpen ? "slide-down" : "slide-up"}
      size={1200}
      zIndex={1010}
      styles={{
        inner: {
          overflow: width <= 900 && width > 0 ? "auto" : "hidden",
        },
      }}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <CancelOfferModal
        isOpen={isOpenCancelOffer}
        close={() => setIsOpenCancelOffer(false)}
        cancelOffer={(reason: string) => cancelOffer(reason)}
      />
      <SelectUserModal
        userList={
          userModalType === "restricted"
            ? uniq(
                flattenDepth(
                  departments
                    .filter((e) =>
                      (projectModal.data.departmentIds || []).includes(e.id)
                    )
                    .map((e) => e.userIds),
                  2
                )
              )
            : (projectModal.data.departmentIds || []).length === 0
            ? undefined
            : users
                .filter(
                  (user: UserType) =>
                    !user.departmentIds?.some((id: number) =>
                      (projectModal.data.departmentIds || []).includes(id)
                    )
                )
                .map((e) => e.id)
        }
        onesList={projectModal.data[`${userModalType}UserIds`]}
        handleSelect={(id: number) => addUser(userModalType, id)}
        handleRemove={(id: number) => removeUser(userModalType, id)}
      />
      <Tabs value={activeTab}>
        <Group position="apart" noWrap mb="sm">
          <ScrollArea
            scrollbarSize={5}
            offsetScrollbars
            styles={{
              viewport: {
                maxWidth:
                  width <= 900 && width > 0 ? "calc(100vw - 130px)" : "auto",
              },
              root: {
                overflow: "visible !important",
              },
            }}
          >
            <DynamicOffersTab
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              isEditable={isEditable}
              isViewable={isViewable}
            />
          </ScrollArea>

          <CloseButton onClick={() => closeModal()} />
        </Group>

        <GiveAQuoteModal
          isOpen={isOpenGiveAQuoteModal}
          setIsOpen={(value: boolean) => setGiveAQuoteModal(value)}
          sendNewPrice={(value: number) => sendAQuote(value)}
        />

        <Box
          ref={ref}
          sx={() => ({
            display: "flex",
            flexDirection: width <= 900 && width > 0 ? "column" : "row",
            flexWrap: "nowrap",
            alignItems: "stretch",
            overflow: "hidden",
            width: "100%",
          })}
        >
          <Tabs.Panel value="description" sx={{ flexGrow: 1 }}>
            <Group noWrap align="flex-start" p={4}>
              <Grid p={4} justify="center">
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <Input.Wrapper label={t("offersModal.titleLabel")}>
                    <Input
                      disabled={!isEditable}
                      value={lastOffer.name || ""}
                      onChange={(event: any) =>
                        setLastOffer({
                          name: event.target.value,
                        })
                      }
                    />
                  </Input.Wrapper>
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <Input.Wrapper label={t("offersModal.customerLabel")}>
                    <Input
                      value={
                        projectModal.data.requestId
                          ? (
                              customers.find(
                                (e: CustomerType) =>
                                  e.id ===
                                  requests.find(
                                    (e: RequestType) =>
                                      e.id === projectModal.data.requestId
                                  )?.customerId
                              ) as CustomerType | undefined
                            )?.fullName || ""
                          : ""
                      }
                      disabled
                    />
                  </Input.Wrapper>
                </Grid.Col>
                <Grid.Col span={12}>
                  <Textarea
                    disabled={!isEditable}
                    label={t("offersModal.descriptionLabel")}
                    value={lastOffer.description || ""}
                    onChange={(event: any) => {
                      setLastOffer({
                        description: event.target.value,
                      });
                    }}
                  />
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <DateRangePicker
                    allowSingleDateInRange
                    disabled={!isEditable}
                    clearable={isEditable}
                    label={t("offersModal.dateRange")}
                    value={[new Date(lastOffer.start), new Date(lastOffer.end)]}
                    onChange={([start, end]: [Date, Date]) =>
                      setLastOffer({
                        start,
                        end,
                      })
                    }
                  />
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <TimeRangeInput
                    disabled={!isEditable}
                    label={t("offersModal.timeRange")}
                    value={[new Date(lastOffer.start), new Date(lastOffer.end)]}
                    onChange={([start, end]) => {
                      const newStartDate = new Date(lastOffer.start);
                      const newEndDate = new Date(lastOffer.end);

                      if (start) {
                        newStartDate.setHours(start.getHours());
                        newStartDate.setMinutes(start.getMinutes());
                      } else {
                        newStartDate.setHours(0);
                        newStartDate.setMinutes(0);
                      }

                      if (end) {
                        newEndDate.setHours(end.getHours());
                        newEndDate.setMinutes(end.getMinutes());
                      } else {
                        newStartDate.setHours(23);
                        newStartDate.setMinutes(59);
                      }

                      setLastOffer({
                        start: newStartDate,
                        end: newEndDate,
                      });
                    }}
                  />
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  {/* <MultiSelect
                    disabled={!isEditable}
                    label={t("offersModal.departments")}
                    value={(projectModal.data.roleIds || []).map((e) => `${e}`)}
                    data={flattenDepth(
                      departments
                        .filter((e) => e.roleIds.length > 0)
                        .map((department: DepartmentType) => {
                          return roles
                            .filter((role: RoleType) =>
                              department.roleIds.includes(+role.id)
                            )
                            .map((e) => ({
                              value: `${e.id}`,
                              label: e.label || "",
                              group: department.label,
                            }));
                        }),
                      1
                    )}
                    onChange={(value: string[]) =>
                      setProjectModal({
                        data: {
                          ...projectModal.data,
                          roleIds: value.map((e) => +e),
                        },
                      })
                    }
                  /> */}
                  <MultiSelect
                    disabled={!isEditable}
                    label={t("offersModal.departments")}
                    value={(projectModal.data.departmentIds || []).map(
                      (e) => `${e}`
                    )}
                    data={filteredDepartments
                      .filter((e) => e.isVisible)
                      .map((department: DepartmentType) => ({
                        value: `${department.id}`,
                        label: department.label || "",
                      }))}
                    onChange={(value: string[]) =>
                      setProjectModal({
                        data: {
                          ...projectModal.data,
                          departmentIds: value.map((e) => +e),
                        },
                      })
                    }
                  />
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <Select
                    disabled={!isEditable}
                    label={t("offersModal.priority")}
                    value={projectModal.data.priority || "Medium"}
                    data={prioritys.map((e) => ({
                      value: e,
                      label: t(`priority.${e}`),
                    }))}
                    onChange={(value: OfferPriorityType) =>
                      setProjectModal({
                        data: {
                          ...projectModal.data,
                          priority: value,
                        },
                      })
                    }
                  />
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <Divider
                    color="green"
                    label={t("offersModal.allowedUsers")}
                    labelPosition="center"
                    mb="sm"
                  />
                  <SimpleGrid cols={width < 1150 && width > 0 ? 1 : 2}>
                    {orderBy(projectModal.data.allowedUserIds, "id").map(
                      (id: number, i: number) => {
                        const activeUserId = users.find(
                          (e) => e.id === id
                        ) as UserType;

                        return (
                          <Tooltip
                            label={
                              activeUserId.name + " " + activeUserId.surname
                            }
                            key={"allowedUser-" + i}
                          >
                            <Box
                              sx={(theme) => ({
                                padding: theme.spacing.xs,
                                background:
                                  theme.colorScheme === "dark"
                                    ? theme.colors.dark[8]
                                    : theme.colors.gray[2],
                                borderRadius: 8,
                              })}
                            >
                              <Group
                                spacing={12}
                                noWrap
                                sx={{
                                  position: "relative",
                                  width: "100%",
                                }}
                              >
                                <ActiveAvatar userId={id} size={32} />
                                <Text
                                  sx={{
                                    width: "calc(100% - 80px)",
                                    whiteSpace: "nowrap",
                                    textOverflow: "ellipsis",
                                    overflow: "hidden",
                                  }}
                                >
                                  {activeUserId.name} {activeUserId.surname}
                                </Text>
                                {isEditable && (
                                  <ActionIcon
                                    size="sm"
                                    onClick={() => removeUser("allowed", id)}
                                  >
                                    <XMarkIcon
                                      style={{ width: 14, height: 14 }}
                                    />
                                  </ActionIcon>
                                )}
                              </Group>
                            </Box>
                          </Tooltip>
                        );
                      }
                    )}
                    {isEditable && (
                      <Box
                        sx={(theme) => ({
                          minHeight: 52,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: theme.radius.md,
                          border: `2px dashed ${
                            theme.colorScheme === "dark"
                              ? theme.colors.dark[6]
                              : theme.colors.gray[2]
                          }`,
                          cursor: "pointer",
                          transition: "filter .2s ease",
                          ":hover": {
                            filter: "brightness(.9)",
                          },
                        })}
                        onClick={() => openSelectUserModal("allowed")}
                      >
                        <PlusIcon style={{ width: 24, height: 24 }} />
                      </Box>
                    )}
                  </SimpleGrid>
                </Grid.Col>
                <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
                  <Divider
                    color="red"
                    label={t("offersModal.restrictedUsers")}
                    labelPosition="center"
                    mb="sm"
                  />
                  <SimpleGrid cols={width < 1150 && width > 0 ? 1 : 2}>
                    {orderBy(projectModal.data.restrictedUserIds, "id").map(
                      (id: number, i: number) => {
                        const activeUserId = users.filter(
                          (e) => e.id === id
                        )[0];
                        return (
                          <Tooltip
                            label={
                              activeUserId.name + " " + activeUserId.surname
                            }
                            key={"allowedUser-" + i}
                          >
                            <Box
                              sx={(theme) => ({
                                padding: theme.spacing.xs,
                                background:
                                  theme.colorScheme === "dark"
                                    ? theme.colors.dark[8]
                                    : theme.colors.gray[2],
                                borderRadius: 8,
                              })}
                            >
                              <Group spacing={12}>
                                <ActiveAvatar userId={id} size={32} />
                                <Text
                                  sx={{
                                    width: "calc(100% - 80px)",
                                    whiteSpace: "nowrap",
                                    textOverflow: "ellipsis",
                                    overflow: "hidden",
                                  }}
                                >
                                  {activeUserId.name} {activeUserId.surname}
                                </Text>
                                {isEditable && (
                                  <ActionIcon
                                    size="sm"
                                    onClick={() => removeUser("restricted", id)}
                                  >
                                    <XMarkIcon
                                      style={{ width: 14, height: 14 }}
                                    />
                                  </ActionIcon>
                                )}
                              </Group>
                            </Box>
                          </Tooltip>
                        );
                      }
                    )}
                    {isEditable && (
                      <Box
                        sx={(theme) => ({
                          minHeight: 52,
                          display: "flex",
                          alignItems: "center",
                          justifyContent: "center",
                          borderRadius: theme.radius.md,
                          border: `2px dashed ${
                            theme.colorScheme === "dark"
                              ? theme.colors.dark[6]
                              : theme.colors.gray[2]
                          }`,
                          cursor: "pointer",
                          transition: "filter .2s ease",
                          ":hover": {
                            filter: "brightness(.9)",
                          },
                        })}
                        onClick={() => openSelectUserModal("restricted")}
                      >
                        <PlusIcon style={{ width: 24, height: 24 }} />
                      </Box>
                    )}
                  </SimpleGrid>
                </Grid.Col>
              </Grid>
            </Group>
          </Tabs.Panel>
          <Tabs.Panel value="invoices" sx={{ flexGrow: 1 }}>
            <Stack
              justify="space-between"
              sx={{
                height: "100%",
              }}
            >
              <Text></Text>
              <Group position="right">
                <Button size="xs" color="blue">
                  {t("convertInvoice")}
                </Button>
              </Group>
            </Stack>
          </Tabs.Panel>
          <Tabs.Panel value="tasks" sx={{ flexGrow: 1 }}>
            <ScrollArea
              sx={{ height: "calc(100vh - 220px)" }}
              styles={{
                root: {
                  overflowY: "auto",
                  maxWidth:
                    width >= 1160 ? "auto" : width >= 900 ? width - 300 : width,
                },
              }}
              offsetScrollbars
            >
              <TasksTab
                projectId={projectModal.data.id}
                closeModal={closeModal}
              />
            </ScrollArea>
          </Tabs.Panel>
          {projectModal.data.offerIds.map((id: number, i: number) => {
            const activeOffer = offers.filter((e) => e.id === id)[0];

            return (
              <Tabs.Panel
                key={"panel-" + i}
                value={`${id}`}
                sx={{ flexGrow: 1 }}
              >
                <Stack
                  sx={() => ({
                    width: "100%",
                    height: "calc(100vh - 220px)",
                  })}
                  justify="flex-end"
                >
                  <CustomInfiniteScroll
                    dataLength={ownMessages.length}
                    hasMore={hasMore}
                    inverse={true}
                    next={fetchMoreMessages}
                    height="calc(100vh - 330px)"
                  >
                    <Stack spacing="md">
                      <ContentRequest requestId={projectModal.data.requestId} />
                      {ownMessages.map((message: MessageType, i: number) => {
                        let user = users.filter(
                          (e) => e.id === message.ownerId
                        )[0];

                        let isMine = message.ownerId === activeUserId;

                        const Message =
                          MessageTypes[
                            (message.content.type ||
                              message.type) as keyof typeof MessageTypes
                          ];

                        if (!user) {
                          return null;
                        }

                        return (
                          <Group
                            sx={{ width: "100%" }}
                            key={"content-" + i}
                            position={
                              message.type !== "subtasks"
                                ? isMine
                                  ? "right"
                                  : "left"
                                : "center"
                            }
                          >
                            <Paper
                              p="sm"
                              sx={(theme) => ({
                                position: "relative",
                                minWidth: 180,
                                background:
                                  theme.colorScheme === "dark"
                                    ? theme.colors.dark[8]
                                    : theme.colors.gray[2],
                              })}
                            >
                              <Stack spacing={0}>
                                <Group position="apart">
                                  <Text size="sm" weight={600}>
                                    {user.name} {user.surname}
                                  </Text>
                                  {isNumber(activeUserDefaultRoleWeight) &&
                                    (isMine ||
                                      activeUserDefaultRoleWeight < 0) && (
                                      <Menu
                                        shadow="md"
                                        width={200}
                                        offset={0}
                                        position={"top-end"}
                                        transition="fade"
                                        withArrow
                                      >
                                        <Menu.Target>
                                          <ActionIcon size="sm">
                                            <EllipsisHorizontalIcon
                                              style={{
                                                width: 16,
                                                height: 16,
                                              }}
                                            />
                                          </ActionIcon>
                                        </Menu.Target>
                                        <Menu.Dropdown>
                                          <Menu.Item
                                            onClick={() =>
                                              handleDelete(message)
                                            }
                                          >
                                            {t("chat.delete")}
                                          </Menu.Item>
                                        </Menu.Dropdown>
                                      </Menu>
                                    )}
                                </Group>
                                <Group
                                  position="apart"
                                  align="flex-end"
                                  spacing={8}
                                >
                                  <Message
                                    ownerId={message.ownerId}
                                    content={message.content as any}
                                    isMobile={false}
                                    approvePrice={() =>
                                      setPriceResult(message, "approved")
                                    }
                                    rejectPrice={() =>
                                      setPriceResult(message, "rejected")
                                    }
                                  />
                                </Group>
                                <Group position="right">
                                  <Text size="xs" color="dimmed">
                                    {formatTime(message.createdAt)}{" "}
                                    {formatDate(message.createdAt)}
                                  </Text>
                                </Group>
                              </Stack>
                            </Paper>
                          </Group>
                        );
                      })}

                      {!(
                        activeTab === "description" ||
                        activeTab === "invoices" ||
                        activeTab === "tasks"
                      ) &&
                        offers.filter((e) => e.id === +activeTab)[0].status ===
                          "Canceled" && (
                          <ContentCancelled
                            reason={
                              offers.filter((e) => e.id === +activeTab)[0]
                                .cancellationReason!
                            }
                          />
                        )}

                      {!(
                        activeTab === "description" ||
                        activeTab === "invoices" ||
                        activeTab === "tasks"
                      ) &&
                        offers.filter((e) => e.id === +activeTab)[0].status ===
                          "Revised" && <ContentRevised />}
                    </Stack>
                  </CustomInfiniteScroll>

                  {/* <ScrollArea
                    viewportRef={viewport as any}
                    scrollbarSize={7}
                    styles={{
                      viewport: {
                        maxHeight: "calc(100vh - 320px)",
                        paddingBottom: 0,
                      },
                    }}
                    offsetScrollbars
                    onScrollPositionChange={setScrollPositionChange}
                    pr={8}
                  >
                    <Stack spacing="md">
                      <ContentRequest requestId={projectModal.data.requestId} />
                      {messages
                        .filter((e) => e.chatId === activeChat?.id)
                        .map((message: MessageType, i: number) => {
                          let user = users.filter(
                            (e) => e.id === message.ownerId
                          )[0];

                          let isMine = message.ownerId === activeUserId;

                          const Message =
                            MessageTypes[
                              (message.content.type ||
                                message.type) as keyof typeof MessageTypes
                            ];

                          if (!user) {
                            return null;
                          }

                          return (
                            <Group
                              sx={{ width: "100%" }}
                              key={"content-" + i}
                              position={
                                message.type !== "subtasks"
                                  ? isMine
                                    ? "right"
                                    : "left"
                                  : "center"
                              }
                            >
                              <Paper
                                p="sm"
                                sx={(theme) => ({
                                  position: "relative",
                                  minWidth: 180,
                                  background:
                                    theme.colorScheme === "dark"
                                      ? theme.colors.dark[8]
                                      : theme.colors.gray[2],
                                })}
                              >
                                <Stack spacing={0}>
                                  <Group position="apart">
                                    <Text size="sm" weight={600}>
                                      {user.name} {user.surname}
                                    </Text>
                                    {isNumber(activeUserDefaultRoleWeight) &&
                                      (isMine ||
                                        activeUserDefaultRoleWeight < 0) && (
                                        <Menu
                                          shadow="md"
                                          width={200}
                                          offset={0}
                                          position={"top-end"}
                                          transition="fade"
                                          withArrow
                                        >
                                          <Menu.Target>
                                            <ActionIcon size="sm">
                                              <EllipsisHorizontalIcon
                                                style={{
                                                  width: 16,
                                                  height: 16,
                                                }}
                                              />
                                            </ActionIcon>
                                          </Menu.Target>
                                          <Menu.Dropdown>
                                            <Menu.Item
                                              onClick={() =>
                                                handleDelete(message)
                                              }
                                            >
                                              {t("chat.delete")}
                                            </Menu.Item>
                                          </Menu.Dropdown>
                                        </Menu>
                                      )}
                                  </Group>
                                  <Group
                                    position="apart"
                                    align="flex-end"
                                    spacing={8}
                                  >
                                    <Message
                                      ownerId={message.ownerId}
                                      content={message.content as any}
                                      isMobile={false}
                                      approvePrice={() =>
                                        setPriceResult(message, "approved")
                                      }
                                      rejectPrice={() =>
                                        setPriceResult(message, "rejected")
                                      }
                                    />
                                  </Group>
                                  <Group position="right">
                                    <Text size="xs" color="dimmed">
                                      {formatTime(message.createdAt)}{" "}
                                      {formatDate(message.createdAt)}
                                    </Text>
                                  </Group>
                                </Stack>
                              </Paper>
                            </Group>
                          );
                        })}

                      {!(
                        activeTab === "description" ||
                        activeTab === "invoices" ||
                        activeTab === "tasks"
                      ) &&
                        offers.filter((e) => e.id === +activeTab)[0].status ===
                          "Canceled" && (
                          <ContentCancelled
                            reason={
                              offers.filter((e) => e.id === +activeTab)[0]
                                .cancellationReason!
                            }
                          />
                        )}

                      {!(
                        activeTab === "description" ||
                        activeTab === "invoices" ||
                        activeTab === "tasks"
                      ) &&
                        offers.filter((e) => e.id === +activeTab)[0].status ===
                          "Revised" && <ContentRevised />}
                    </Stack>
                  </ScrollArea> */}

                  {isEditable && (
                    <Stack sx={{ position: "relative" }}>
                      {(images.length > 0 || files.length > 0) && (
                        <Paper
                          sx={(theme) => ({
                            padding: 12,
                            background:
                              theme.colorScheme === "dark"
                                ? theme.colors.dark[8]
                                : theme.colors.gray[2],
                            boxShadow: `0 -1px 3px rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0px -20px 25px -5px, rgba(0, 0, 0, 0.04) 0px -10px 10px -5px`,
                            zIndex: 10,
                          })}
                        >
                          <ScrollArea
                            offsetScrollbars
                            scrollbarSize={5}
                            styles={{
                              viewport: {
                                maxHeight: 170,
                              },
                            }}
                          >
                            {images.length > 0 && (
                              <Text
                                size="xs"
                                weight={600}
                                color="dimmed"
                                mb={8}
                              >
                                {t("images")}
                              </Text>
                            )}
                            <SimpleGrid
                              cols={
                                width > 1200
                                  ? 5
                                  : width > 1000
                                  ? 4
                                  : width > 800
                                  ? 3
                                  : width > 600
                                  ? 2
                                  : 1
                              }
                              sx={{ position: "relative" }}
                            >
                              {images.map((image: File, i: number) => {
                                const blob = new Blob([image], {
                                  type: "image/jpeg",
                                });
                                const blobURL = URL.createObjectURL(blob);

                                return (
                                  <Paper key={`image-${i}`} p={8}>
                                    <Group noWrap spacing={8}>
                                      <Image
                                        height={36}
                                        width={36}
                                        src={blobURL}
                                        radius={8}
                                        alt="preview"
                                        withPlaceholder
                                      />
                                      <Text
                                        size="xs"
                                        sx={{
                                          width: "100%",
                                          whiteSpace: "nowrap",
                                          textOverflow: "ellipsis",
                                          overflow: "hidden",
                                        }}
                                      >
                                        {image.name}
                                      </Text>
                                      <CloseButton
                                        size="sm"
                                        onClick={() =>
                                          setImages(
                                            images.filter(
                                              (e, index) => index !== i
                                            )
                                          )
                                        }
                                      />
                                    </Group>
                                  </Paper>
                                );
                              })}
                            </SimpleGrid>
                            <Space h="xs" />
                            {files.length > 0 && (
                              <Text
                                size="xs"
                                weight={600}
                                color="dimmed"
                                mb={8}
                              >
                                {t("files")}
                              </Text>
                            )}
                            <SimpleGrid
                              cols={
                                width > 1200
                                  ? 5
                                  : width > 1000
                                  ? 4
                                  : width > 800
                                  ? 3
                                  : width > 600
                                  ? 2
                                  : 1
                              }
                              sx={{ position: "relative" }}
                            >
                              {files.map((image: File, i: number) => {
                                return (
                                  <Paper key={`image-${i}`} p={8}>
                                    <Group noWrap spacing={8}>
                                      <ThemeIcon variant="light" size={36}>
                                        <DocumentIcon
                                          style={{ width: 20, height: 20 }}
                                        />
                                      </ThemeIcon>
                                      <Text
                                        size="xs"
                                        sx={{
                                          width: "100%",
                                          whiteSpace: "nowrap",
                                          textOverflow: "ellipsis",
                                          overflow: "hidden",
                                        }}
                                      >
                                        {image.name}
                                      </Text>
                                      <CloseButton
                                        size="sm"
                                        onClick={() =>
                                          setFiles(
                                            files.filter(
                                              (e, index) => index !== i
                                            )
                                          )
                                        }
                                      />
                                    </Group>
                                  </Paper>
                                );
                              })}
                            </SimpleGrid>
                          </ScrollArea>
                        </Paper>
                      )}
                      <Textarea
                        ref={inputRef}
                        placeholder={t("offersModal.yourMessage")}
                        autosize
                        maxRows={3}
                        minRows={1}
                        defaultValue={text}
                        disabled={activeOffer.status !== "Offer"}
                        onChange={(event) => setText(event.currentTarget.value)}
                        onKeyDown={getHotkeyHandler([["enter", sendMessage]])}
                      />
                      <Group position="apart">
                        <Group>
                          {activeOffer.status === "Offer" && (
                            <>
                              <FileButton
                                multiple
                                onChange={(files) =>
                                  handleSelect("files", files)
                                }
                                accept=".doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.pdf,.zip,.rar"
                              >
                                {(props) => (
                                  <Button
                                    {...props}
                                    variant="light"
                                    color="gray"
                                    size="xs"
                                    leftIcon={
                                      <PaperClipIcon
                                        style={{ width: 16, height: 16 }}
                                      />
                                    }
                                  >
                                    <Text color="dimmed">
                                      {t("taskViewModal.attachment")}
                                    </Text>
                                  </Button>
                                )}
                              </FileButton>
                              <FileButton
                                multiple
                                onChange={(images) =>
                                  handleSelect("images", images)
                                }
                                accept="image/png,image/jpeg,image/webp"
                              >
                                {(props) => (
                                  <Button
                                    {...props}
                                    size="xs"
                                    variant="light"
                                    color="gray"
                                    leftIcon={
                                      <PhotoIcon
                                        style={{ width: 16, height: 16 }}
                                      />
                                    }
                                  >
                                    <Text color="dimmed">
                                      {t("taskViewModal.image")}
                                    </Text>
                                  </Button>
                                )}
                              </FileButton>
                            </>
                          )}

                          {isNumber(activeUserDefaultRoleWeight) &&
                            activeUserDefaultRoleWeight < 0 && (
                              <Button
                                color="lime"
                                size="xs"
                                onClick={() => revise()}
                              >
                                {t("offersModal.Revise")}
                              </Button>
                            )}

                          {activeOffer.status === "Completed" &&
                            activeUserDefaultRoleWeight && (
                              <Button size="xs" color="cyan">
                                {t("createInvoice")}
                              </Button>
                            )}

                          {activeOffer.status === "Offer" && (
                            <>
                              <Button
                                color="red"
                                size="xs"
                                onClick={() => setIsOpenCancelOffer(true)}
                              >
                                {t("offersModal.cancelOffer")}
                              </Button>
                              {isNumber(activeUserDefaultRoleWeight) &&
                                activeUserDefaultRoleWeight < 0 && (
                                  <Button
                                    color="green"
                                    size="xs"
                                    onClick={() => complete()}
                                  >
                                    {t("offersModal.complete")}
                                  </Button>
                                )}
                            </>
                          )}
                        </Group>

                        <Group>
                          {activeOffer.status === "Offer" && (
                            <>
                              <Button
                                color="teal"
                                size="xs"
                                onClick={() => setGiveAQuoteModal(true)}
                              >
                                {t("offersModal.giveAQuote")}
                              </Button>
                              <Button
                                color="blue"
                                size="xs"
                                onClick={() => sendMessage()}
                                loading={isSending}
                                loaderProps={{
                                  size: "xs",
                                  variant: "bars",
                                }}
                              >
                                {t("send")}
                              </Button>
                            </>
                          )}
                        </Group>
                      </Group>
                    </Stack>
                  )}
                </Stack>
              </Tabs.Panel>
            );
          })}
          <Space w="sm" h="xl" />
          {!(width <= 900 && width > 0 && activeTab === "invoices") && (
            <OfferInformations
              fullWidth={width <= 900 && width > 0}
              data={
                activeTab === "description" ||
                activeTab === "invoices" ||
                activeTab === "tasks" ||
                +activeTab === lastOffer.id
                  ? lastOffer
                  : offers.filter((e) => e.id === +activeTab)[0]
              }
              save={() => saveOffer()}
              closeModal={closeModal}
              deleteProject={deleteProject}
            />
          )}
        </Box>
      </Tabs>
    </Modal>
  );
};

export default OffersModal;
