import {
  Mo<PERSON>,
  NumberInput,
  Stack,
  Di<PERSON>r,
  Group,
  Button,
  LoadingOverlay,
} from "@mantine/core";
import { BanknotesIcon } from "@heroicons/react/24/outline";
import { useCallback, useState } from "react";
import { numberFormatter } from "~utils/tools";
import { useTranslation } from "next-i18next";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

interface PropType {
  isOpen: boolean;
  setIsOpen(value: boolean): void;
  sendNewPrice(value: number): void;
}

export default function GiveAQuoteModal(props: PropType) {
  const { isLoading, setIsLoading, loadingLevel, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { isOpen, setIsOpen, sendNewPrice } = props;

  const [newPrice, setNewPrice] = useState(0);

  const closeModal = useCallback(() => {
    setIsOpen(false);
    setNewPrice(0);
  }, [setIsOpen]);

  const sendPrice = useCallback(() => {
    setIsLoading(true);
    setLoadingLevel(2);

    setTimeout(() => {
      setIsLoading(false);
      sendNewPrice(newPrice);
      closeModal();
    }, 1000);
  }, [setIsLoading, setLoadingLevel, sendNewPrice, newPrice, closeModal]);

  const { t } = useTranslation();

  return (
    <Modal
      title={t("giveAQuote")}
      opened={isOpen}
      onClose={() => closeModal()}
      centered
      transition={isOpen ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
      size={500}
      zIndex={10000}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 2}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md
        })}
      />
      <Stack>
        <NumberInput
          label={t("price")}
          hideControls
          value={newPrice}
          onChange={(value: number) => setNewPrice(value)}
          parser={(value) => value!.replace(/\₺\s?|(,*)/g, "")}
          formatter={(value) => numberFormatter(value as string)}
        />
        <Divider />
        <Group position="right">
          <Button size="xs" onClick={() => sendPrice()}>
            {t("send")}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
}
