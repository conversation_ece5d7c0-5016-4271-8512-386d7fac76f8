import { Box, Text } from "@mantine/core";
import { useTranslation } from "next-i18next";
import { hexToRGBA } from "~utils/tools";

export default function ContentRevised() {
  const { t } = useTranslation();

  return (
    <Box
      sx={(theme) => ({
        width: "100%",
        borderWidth: 2,
        borderStyle: "solid",
        borderColor:
          theme.colorScheme === "dark"
            ? theme.colors.lime[8]
            : theme.colors.lime[5],
        borderRadius: 8,
        padding: theme.spacing.md,
        background: hexToRGBA(theme.colors.lime[9], 0.1),
      })}
      mb="xs"
    >
      <Text color="lime.5" weight={600} mb={4}>
        {t("offersModal.revised")}
      </Text>
      <Text color="lime.3">{t("offersModal.revisedMessage")}</Text>
    </Box>
  );
}
