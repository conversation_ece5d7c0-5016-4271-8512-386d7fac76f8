import { formatDate, hexToRGB<PERSON>, numberFormatter } from "~utils/tools";

import {
  Paper,
  Group,
  Stack,
  Avatar,
  Text,
  Box,
  Button,
  Divider,
  useMantineTheme,
} from "@mantine/core";
import { useTranslation } from "next-i18next";
import { CheckIcon, XMarkIcon } from "@heroicons/react/24/outline";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";

interface PropType {
  ownerId: number;
  content: {
    price: number;
    result: string;
  };
  approvePrice: () => Promise<void>;
  rejectPrice: () => Promise<void>;
}

const ContentText = function (props: PropType) {
  const { ownerId, content, approvePrice, rejectPrice } = props;

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRole = roles.find(
    (e) => e.isDefaultRole && e.id === activeUser?.defaultRoleId[0]
  );

  const owner = users.find((e) => e.id === ownerId);

  const { t } = useTranslation();
  const theme = useMantineTheme();

  const isEditable =
    activeUserDefaultRole?.name === "super-admin" ||
    activeUserDefaultRole?.name === "admin" ||
    (activeUserId !== ownerId &&
      (owner?.isCustomer ? !activeUser?.isCustomer : activeUser?.isCustomer));

  return (
    <Stack spacing={4} mt="xs" sx={{ width: 200 }} mb={8}>
      <Box
        sx={(theme) => ({
          background:
            content.result === ""
              ? theme.colors.dark[6]
              : content.result === "approved"
              ? hexToRGBA(theme.colors.green[9], 0.15)
              : hexToRGBA(theme.colors.red[9], 0.15),
          borderRadius: theme.radius.md,
          textAlign: "center",
        })}
        py={4}
      >
        <Group position="center" spacing={12}>
          <Text size="lg" weight={600}>
            {numberFormatter(content.price)}
          </Text>
          {content.result !== "" &&
            (content.result === "approved" ? (
              <CheckIcon
                style={{
                  width: 16,
                  height: 16,
                  strokeWidth: 3,
                  color: theme.colors.green[6],
                }}
              />
            ) : (
              <XMarkIcon
                style={{
                  width: 16,
                  height: 16,
                  strokeWidth: 3,
                  color: theme.colors.red[6],
                }}
              />
            ))}
        </Group>
      </Box>
      {content.result === "" && isEditable && (
        <Group spacing={4} grow>
          <Button
            size="xs"
            variant="light"
            color="red"
            onClick={() => rejectPrice()}
          >
            {t("reject")}
          </Button>
          <Button
            size="xs"
            variant="light"
            color="green"
            onClick={() => approvePrice()}
          >
            {t("approve")}
          </Button>
        </Group>
      )}
    </Stack>
  );
};

export default ContentText;
