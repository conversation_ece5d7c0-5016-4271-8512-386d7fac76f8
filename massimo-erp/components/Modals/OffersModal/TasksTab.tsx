import { useEffect, useCallback } from "react";
import { Table, Badge, Text, Center, Title, Stack } from "@mantine/core";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { formatDate } from "~utils/tools";
import { TaskType } from "~utils/types/Task";
import { CC } from "~types";
import { useTranslation } from "next-i18next";
import { orderBy } from "lodash";

const TasksTab: CC<{
  projectId: number;
  closeModal: () => void;
}> = ({ projectId, closeModal }) => {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const {
    setTaskViewModalData,
    setIsOpenTaskViewModal,
    setIsLoading,
    setLoadingLevel,
  } = useStore(
    "temp",
    (state) => ({
      setTaskViewModalData: state.setTaskViewModalData!,
      setIsOpenTaskViewModal: state.setIsOpenTaskViewModal!,
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );
  const { tasks, actionGetTasks, statusColors } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
      actionGetTasks: state.actionGetTasks!,
      statusColors: state.statusColors!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRoleWeight =
    roles.find((e) => e.id === activeUser?.defaultRoleId[0])?.weight || 0;

  const shownTasks = orderBy(
    tasks.filter(
      (e) =>
        e.projectId === projectId &&
        (activeUserDefaultRoleWeight < 0 ? true : !e.customerPrivate)
    ),
    "id"
  );

  const openTask = useCallback(
    (task: TaskType) => {
      closeModal();
      setTaskViewModalData(task);
      setIsOpenTaskViewModal(true);
    },
    [closeModal, setIsOpenTaskViewModal, setTaskViewModalData]
  );

  const updateTasks = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      await actionGetTasks({
        projectId,
      });
    } finally {
      setIsLoading(false);
    }
  }, [actionGetTasks, projectId, setIsLoading, setLoadingLevel]);

  useEffect(() => {
    updateTasks();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Stack sx={{ minWidth: 500 }}>
      {shownTasks.length > 0 ? (
        <Table highlightOnHover>
          <thead>
            <tr>
              <th>{t("offersModal.tasksTable.status")}</th>
              <th>{t("offersModal.tasksTable.title")}</th>
              <th>{t("offersModal.tasksTable.date")}</th>
            </tr>
          </thead>
          <tbody>
            {shownTasks.map((task: TaskType, i: number) => {
              return (
                <tr
                  key={`task-${task.id}`}
                  onClick={() => openTask(task)}
                  style={{ cursor: "pointer" }}
                >
                  <td>
                    <Badge size="sm" color={statusColors[task.status]}>
                      {t(`status.${task.status}`)}
                    </Badge>
                  </td>
                  <td>{task.title}</td>
                  <td>
                    <Text size="xs">
                      {formatDate(task.start)} - {formatDate(task.end)}
                    </Text>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </Table>
      ) : (
        <Center py="sm">
          <Text size="sm">{t("crm.tasksTable.noTask")}</Text>
        </Center>
      )}
    </Stack>
  );
};

export default TasksTab;
