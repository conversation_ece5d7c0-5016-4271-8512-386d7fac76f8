import { OfferType } from "~utils/types/Project";

import {
  Stack,
  Paper,
  Text,
  Button,
  Box,
  Group,
  Avatar,
  Divider,
  ThemeIcon,
  Menu,
  ActionIcon,
  Badge,
  Tooltip,
} from "@mantine/core";
import {
  ArrowPathRoundedSquareIcon,
  CalendarDaysIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  ChevronUpDownIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  Calendar,
  DateRangePicker,
  RangeCalendar,
  TimeRangeInput,
} from "@mantine/dates";

import { formatDate, formatTime, numberFormatter } from "~utils/tools";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import Requests from "~pages/admin/requests";
import { useTranslation } from "next-i18next";
import { UserType } from "~utils/types/User";
import { RequestType } from "~utils/types/Project";
import ActiveAvatar from "~components/ActiveAvatar";
import { CustomerType } from "~utils/types/Customer";
import { isNumber } from "lodash";

interface PropType {
  data: OfferType;
  save: () => void;
  closeModal: () => void;
  fullWidth: boolean;
  deleteProject: () => void;
}

export default function OfferInformations(props: PropType) {
  const { data, save, closeModal, fullWidth, deleteProject } = props;

  const { isLoading, lastOffer, setLastOffer, projectModal } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      lastOffer: state.lastOffer!,
      setLastOffer: state.setLastOffer!,
      projectModal: state.projectModal!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { offerStatusList, offerStatusColors } = useStore(
    "offers",
    (state) => ({
      offerStatusList: state.offerStatusList!,
      offerStatusColors: state.offerStatusColors!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const activeRequest = requests.find(
    (e) => e.id === projectModal.data.requestId
  ) as RequestType | undefined;

  const approver = users.find((e) => e.id === activeRequest?.approverId)! as
    | UserType
    | undefined;

  const customer = customers.find(
    (e) => e.id === activeRequest?.customerId
  )! as CustomerType | undefined;

  const activeUser = users.find((e) => e.id === activeUserId);
  const activeUserDefaultRoleWeight = roles.find(
    (e) => e.isDefaultRole && e.id === activeUser?.defaultRoleId[0]
  )?.weight;

  return (
    <Stack
      sx={(theme) => ({
        flexShrink: 0,
        width: fullWidth ? "100%" : 300,
      })}
      spacing="md"
      justify="space-between"
    >
      <Stack
        sx={(theme) => ({
          width: "100%",
          background:
            theme.colorScheme === "dark"
              ? theme.colors.dark[6]
              : theme.colors.gray[2],
          borderRadius: theme.radius.md,
        })}
        p="md"
      >
        <Stack spacing={4}>
          <Paper
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[2],
              border: `2px dashed ${
                theme.colorScheme === "dark"
                  ? theme.colors.dark[4]
                  : theme.colors.gray[3]
              }`,
              borderRadius: theme.radius.md,
              padding: theme.spacing.sm,
            })}
          >
            <Group
              noWrap
              sx={{
                position: "relative",
                width: "100%",
              }}
            >
              <ActiveAvatar
                userId={activeRequest?.approverId}
                size="md"
                radius="xl"
              />
              <Stack
                spacing={0}
                sx={{
                  position: "relative",
                  width: "calc(100% - 56px)",
                }}
              >
                <Text size="xs" weight={600} color="dimmed">
                  {t("offersModal.approver")}
                </Text>
                <Tooltip label={`${approver?.name} ${approver?.surname}`}>
                  <Text
                    size="sm"
                    weight={600}
                    sx={{
                      maxWidth: "100%",
                      whiteSpace: "nowrap",
                      textOverflow: "ellipsis",
                      overflow: "hidden",
                    }}
                  >
                    {approver?.name} {approver?.surname}
                  </Text>
                </Tooltip>
              </Stack>
            </Group>
          </Paper>
          <Group position="center">
            <ChevronUpDownIcon style={{ width: 16, height: 16 }} />
          </Group>
          <Paper
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[2],
              border: `2px dashed ${
                theme.colorScheme === "dark"
                  ? theme.colors.dark[4]
                  : theme.colors.gray[3]
              }`,
              borderRadius: theme.radius.md,
              padding: theme.spacing.sm,
            })}
          >
            <Stack
              spacing={0}
              sx={{
                width: "100%",
                position: "relative",
              }}
            >
              <Text size="xs" weight={600} color="dimmed">
                {t("offersModal.customer")}
              </Text>
              <Tooltip label={customer?.fullName}>
                <Text
                  size="sm"
                  weight={600}
                  sx={{
                    maxWidth: "100%",
                    whiteSpace: "nowrap",
                    textOverflow: "ellipsis",
                    overflow: "hidden",
                  }}
                >
                  {customer?.fullName}
                </Text>
              </Tooltip>
              <Text
                size="xs"
                weight={600}
                color="dimmed"
                sx={{
                  maxWidth: "100%",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                }}
              >
                {customer?.shortName}
              </Text>
            </Stack>
          </Paper>
        </Stack>
        <Divider />
        <Stack align="flex-start" spacing="sm">
          <Group>
            <Text color="dimmed" size="xs" sx={{ width: 80 }}>
              {t("offersModal.status")}
            </Text>
            <Group spacing={4}>
              <Badge color={offerStatusColors[data.status!]}>
                {t(`offersModal.statuses.${data.status}`)}
              </Badge>
            </Group>
          </Group>
          <Group noWrap>
            <Text color="dimmed" size="xs" sx={{ width: 80 }}>
              {t("offersModal.activePrice")}
            </Text>
            <Text size="md" weight={600}>
              {data.budget > 0 ? numberFormatter(data.budget) : 0}
            </Text>
          </Group>
          <Group>
            <Text color="dimmed" size="xs" sx={{ width: 80 }}>
              {t("offersModal.dateRange")}
            </Text>
            <Group spacing={8}>
              <ThemeIcon size="md" variant="light" color="blue">
                <CalendarDaysIcon style={{ width: 16, height: 16 }} />
              </ThemeIcon>
              <Group spacing={8}>
                <Text size="sm">{formatDate(new Date(data.end!))}</Text>

                {data.id === lastOffer.id && projectModal.isOwner && (
                  <Menu shadow="md" position="bottom-end">
                    <Menu.Target>
                      <ActionIcon size={26}>
                        <ArrowPathRoundedSquareIcon
                          style={{ width: 16, height: 16 }}
                        />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <RangeCalendar
                        value={[new Date(data.start), new Date(data.end)]}
                        onChange={(data: [Date, Date]) => {
                          if (data[1] !== null) {
                            setLastOffer({
                              start: data[0],
                              end: data[1],
                            });
                          }
                        }}
                      />
                    </Menu.Dropdown>
                  </Menu>
                )}
              </Group>
            </Group>
          </Group>
          <Group>
            <Text color="dimmed" size="xs" sx={{ width: 80 }}>
              {t("offersModal.timeRange")}
            </Text>
            <Group spacing={8}>
              <ThemeIcon size="md" variant="light" color="blue">
                <CalendarDaysIcon style={{ width: 16, height: 16 }} />
              </ThemeIcon>
              <Group spacing={8}>
                <Text size="sm">
                  {formatTime(new Date(data.start!))} -{" "}
                  {formatTime(new Date(data.end!))}
                </Text>

                {data.id === lastOffer.id && projectModal.isOwner && (
                  <Menu shadow="md" position="bottom-end">
                    <Menu.Target>
                      <ActionIcon size={26}>
                        <ArrowPathRoundedSquareIcon
                          style={{ width: 16, height: 16 }}
                        />
                      </ActionIcon>
                    </Menu.Target>

                    <Menu.Dropdown>
                      <TimeRangeInput
                        styles={{
                          input: {
                            border: "none",
                          },
                        }}
                        value={[new Date(data.start), new Date(data.end)]}
                        onChange={([start, end]: [Date, Date]) => {
                          const newStartDate = new Date(lastOffer.start);
                          const newEndDate = new Date(lastOffer.end);

                          if (start) {
                            newStartDate.setHours(start.getHours());
                            newStartDate.setMinutes(start.getMinutes());
                          } else {
                            newStartDate.setHours(0);
                            newStartDate.setMinutes(0);
                          }

                          if (end) {
                            newEndDate.setHours(end.getHours());
                            newEndDate.setMinutes(end.getMinutes());
                          } else {
                            newStartDate.setHours(23);
                            newStartDate.setMinutes(59);
                          }

                          setLastOffer({
                            start: newStartDate,
                            end: newEndDate,
                          });
                        }}
                      />
                    </Menu.Dropdown>
                  </Menu>
                )}
              </Group>
            </Group>
          </Group>
          {/* <Group position="apart" noWrap sx={{ width: "100%" }}>
            {offerStatusList.indexOf(data.status) !== 0 ? (
              <Button
                size="xs"
                leftIcon={<ChevronLeftIcon style={{ width: 14, height: 14 }} />}
                onClick={
                  () => {}
                  // setOfferModal({
                  //   data: {
                  //     ...data,
                  //     status:
                  //       offerStatusList[
                  //         offerStatusList.indexOf(data.status) - 1
                  //       ],
                  //   },
                  // })
                }
              >
                Back
              </Button>
            ) : (
              <Box />
            )}
            {offerStatusList.indexOf(data.status) !==
            offerStatusList.length - 1 ? (
              <Button
                size="xs"
                rightIcon={
                  <ChevronRightIcon style={{ width: 14, height: 14 }} />
                }
                onClick={() => {
                  // let nextStatus =
                  //   offerStatusList[offerStatusList.indexOf(data.status) + 1];
                  // if (nextStatus === "Offer") {
                  //   let newProject: ProjectType = {
                  //     id: "project-" + (projects.length + 1),
                  //     title: data.title,
                  //     description: data.contents[0].data.description,
                  //     customerId: data.customerId,
                  //     start: data.start,
                  //     delivery: data.deliveryDate,
                  //     departments: data.departments,
                  //     priority: data.priority,
                  //     files: [],
                  //     offerIds: [data.id],
                  //   };
                  //   addProject(newProject);
                  // } else if (nextStatus === "Revised") {
                  //   addOffer({
                  //     ...data,
                  //     status: "Offer",
                  //   });
                  // }
                  // setOfferModal({
                  //   data: {
                  //     ...data,
                  //     status: nextStatus,
                  //   },
                  // });
                }}
              >
                Next
              </Button>
            ) : (
              <Box />
            )}
          </Group> */}
        </Stack>
      </Stack>
      <Group position="right">
        {isNumber(activeUserDefaultRoleWeight) &&
          activeUserDefaultRoleWeight < 0 && (
            <Button
              color="red"
              leftIcon={<TrashIcon width={16} height={16} />}
              onClick={() => deleteProject()}
            >
              {t("delete")}
            </Button>
          )}

        {projectModal.isOwner ? (
          <Button onClick={() => save()}>{t("save")}</Button>
        ) : (
          <Button variant="light" color="gray" onClick={() => closeModal()}>
            {t("cancel")}
          </Button>
        )}
      </Group>
    </Stack>
  );
}
