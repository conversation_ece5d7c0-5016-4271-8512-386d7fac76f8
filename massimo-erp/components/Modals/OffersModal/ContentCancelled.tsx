import { Box, Text } from "@mantine/core";
import { useTranslation } from "next-i18next";
import { hexToRGBA } from "~utils/tools";

interface PropType {
  reason: string;
}

export default function ContentCancelled(props: PropType) {
  const { reason } = props;

  const { t } = useTranslation();

  return (
    <Box
      sx={(theme) => ({
        width: "100%",
        borderWidth: 2,
        borderStyle: "solid",
        borderColor:
          theme.colorScheme === "dark"
            ? theme.colors.red[8]
            : theme.colors.red[5],
        borderRadius: 8,
        padding: theme.spacing.md,
        background: hexToRGBA(theme.colors.red[9], 0.1),
      })}
      mb="xs"
    >
      <Text color="red.5" weight={600} mb={4}>
        {t("cancelMessage")}
      </Text>
      <Text color="red.3">{reason}</Text>
    </Box>
  );
}
