import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Table,
  Group,
  Button,
  Select,
  Input,
  Pagination,
  Text,
  LoadingOverlay,
  ActionIcon,
  Anchor,
} from "@mantine/core";
import { CustomerType } from "~utils/types/Customer";
import {
  CheckIcon,
  MagnifyingGlassIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useState, useCallback, useEffect } from "react";
import { getHotkeyHandler, useElementSize } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { UserType } from "~utils/types/User";

const UnverifiedUsersModalContent: CC<{}> = function () {
  const { filteredUnverifiedUsers, actionUpdateMultipleUser, actionGetUsers } =
    useStore(
      "users",
      (state) => ({
        filteredUnverifiedUsers: state.filteredUnverifiedUsers!,
        actionUpdateMultipleUser: state.actionUpdateMultipleUser!,
        actionGetUsers: state.actionGetUsers!,
      }),
      shallow
    );
  const { setIsLoading, setLoadingLevel } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const [search, setSearch] = useState("");
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);

  const updateUsers = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);
    try {
      await actionGetUsers(+rowsPerPage, +activePage - 1, search, true);
    } finally {
      setIsLoading(false);
    }
  }, [
    actionGetUsers,
    activePage,
    rowsPerPage,
    search,
    setIsLoading,
    setLoadingLevel,
  ]);

  const verify = useCallback(
    async (user: UserType) => {
      setIsLoading(true);
      setLoadingLevel(1);

      try {
        await actionUpdateMultipleUser([{ ...user, isValid: true }]);
      } finally {
        setIsLoading(false);
        updateUsers();
      }
    },
    [actionUpdateMultipleUser, setIsLoading, setLoadingLevel, updateUsers]
  );

  useEffect(() => {
    updateUsers();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowsPerPage, activePage]);

  const rows = filteredUnverifiedUsers.map((user: UserType, i: number) => (
    <tr key={user.id}>
      <td>
        <Group>
          <Text size="sm" weight={600}>
            {user.name} {user.surname}
          </Text>
        </Group>
      </td>
      <td>
        <Anchor href={`mailto:${Anchor}`}>{user.email}</Anchor>
      </td>
      <td>{user.phone}</td>
      <td align="right">
        <Group position="right" spacing={4}>
          <ActionIcon
            variant="light"
            color="green"
            onClick={() => verify(user)}
          >
            <CheckIcon style={{ width: 16, height: 16 }} />
          </ActionIcon>
        </Group>
      </td>
    </tr>
  ));

  return (
    <>
      <Group
        position={width < 520 && width !== 0 ? "center" : "apart"}
        align="center"
        mb="md"
        ref={ref}
      >
        <Group position="center">
          <Text size="xs" weight={600} color="dimmed">
            {t("rowPerPage")}
          </Text>
          <Select
            size="xs"
            value={rowsPerPage}
            onChange={(value: string) => setRowsPerPage(value)}
            data={[
              { value: "10", label: "10" },
              { value: "20", label: "20" },
              { value: "50", label: "50" },
            ]}
            sx={{
              width: 100,
            }}
            transition="fade"
            transitionDuration={200}
          />
        </Group>
        <Group position="center">
          <Input
            size="xs"
            value={search}
            onChange={(event: any) => setSearch(event.target.value)}
            onKeyDown={getHotkeyHandler([["Enter", updateUsers]])}
            icon={<MagnifyingGlassIcon style={{ width: 16, height: 16 }} />}
            placeholder={t("search")}
            styles={{
              input: {
                width: 140,
              },
            }}
          />
          <Pagination
            page={activePage}
            onChange={setActivePage}
            total={
              filteredUnverifiedUsers.length < +rowsPerPage
                ? 1
                : Math.ceil(filteredUnverifiedUsers.length / +rowsPerPage)
            }
            size="sm"
          />
        </Group>
      </Group>
      {filteredUnverifiedUsers.length > 0 ? (
        <Table>
          <thead>
            <tr>
              <th>{t("customerInvitesModal.user")}</th>
              <th>{t("customerInvitesModal.email")}</th>
              <th>{t("customerInvitesModal.phone")}</th>
              <th align="right" style={{ textAlign: "right" }}>
                {t("customerInvitesModal.actions")}
              </th>
            </tr>
          </thead>
          <tbody>{rows}</tbody>
        </Table>
      ) : (
        <Text size="sm" weight={600} color="dimmed" align="center">
          {t("profile.empty")}
        </Text>
      )}
    </>
  );
};

export default function UnverifiedUsers() {
  const {
    isLoading,
    loadingLevel,
    isOpenUnverifiedUsersModal,
    setIsOpenUnverifiedUsersModal,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      isOpenUnverifiedUsersModal: state.isOpenUnverifiedUsersModal!,
      setIsOpenUnverifiedUsersModal: state.setIsOpenUnverifiedUsersModal!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <Modal
      opened={isOpenUnverifiedUsersModal}
      onClose={() => setIsOpenUnverifiedUsersModal(false)}
      centered
      title={t("customerInvitesModal.title")}
      transition={isOpenUnverifiedUsersModal ? "slide-down" : "slide-up"}
      size={800}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <UnverifiedUsersModalContent />
    </Modal>
  );
}
