import { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Button,
  Group,
  Paper,
  Text,
  Stack,
  Textarea,
  LoadingOverlay,
  FileButton,
  SimpleGrid,
  Image,
  CloseButton,
  Select,
} from "@mantine/core";
import { PaperClipIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { useElementSize } from "@mantine/hooks";
import { CC } from "~types";
import { isArray, isString } from "lodash";
import { showNotification } from "@mantine/notifications";

const ImagesOverview: CC<{
  images: (File | [number, string])[];
  setImages: (images: (File | [number, string])[]) => void;
}> = ({ images, setImages }) => {
  const { ref, width } = useElementSize();

  return (
    <SimpleGrid
      cols={width >= 680 && width > 0 ? 3 : width > 420 ? 2 : 1}
      ref={ref}
    >
      {images.map((image: File | [number, string], i: number) => {
        const blob = new Blob([image as File], { type: "image/jpeg" });
        const blobURL = URL.createObjectURL(blob);

        return (
          <Paper
            key={`image-${i}`}
            p={8}
            sx={(theme) => ({
              background:
                theme.colorScheme === "dark"
                  ? theme.colors.dark[9]
                  : theme.colors.gray[2],
            })}
          >
            <Group noWrap spacing={8}>
              <Image
                height={36}
                width={36}
                src={isArray(image) ? image[1] : blobURL}
                radius={8}
                alt="preview"
                withPlaceholder
              />
              <Text
                size="xs"
                sx={{
                  width: "100%",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                }}
              >
                {isArray(image) ? image[1] : image.name}
              </Text>
              <CloseButton
                size="sm"
                onClick={() =>
                  setImages(images.filter((e, index) => index !== i))
                }
              />
            </Group>
          </Paper>
        );
      })}
    </SimpleGrid>
  );
};

const SharePostModal = function () {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    postModal,
    setPostModal,
    blankPost,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      postModal: state.postModal!,
      setPostModal: state.setPostModal!,
      blankPost: state.computed!.blankPost!,
    }),
    shallow
  );
  const { actionUpdatePosts } = useStore(
    "posts",
    (state) => ({
      actionUpdatePosts: state.actionUpdatePosts!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { files, actionUploadFiles } = useStore(
    "files",
    (state) => ({
      files: state.files!,
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const [images, setImages] = useState<(File | [number, string])[]>([]);
  const [selectedOrganization, setSelectedOrganization] = useState<string>("");

  const handleSelect = useCallback(
    (data: File[]) => {
      const dataList: File[] = [];

      data.forEach((e) => {
        if (e.size >= 10485760) {
          showNotification({
            color: "red",
            title: t("largeFile.title"),
            message: t("largeFile.message"),
            autoClose: 3000,
            styles: {
              root: {
                zIndex: 400,
              },
            },
          });
        } else {
          dataList.push(e);
        }
      });

      if (postModal.type === "story") {
        setImages([dataList[0]]);
      } else {
        setImages([...images, ...dataList].slice(0, 10));
      }
    },
    [images, postModal.type, t]
  );

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setPostModal({
      isOpen: false,
      data: blankPost,
    });
    setImages([]);
    setSelectedOrganization("");
  }, [isLoading, loadingLevel, setPostModal, blankPost]);

  const sharePost = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      let data: {
        id?: number;
        type: "post" | "story";
        description: string;
        files: number[];
        user: number;
        organization?: number;
      } = {
        type: postModal.type,
        description: postModal.data.description || "",
        files: images.filter((e) => isArray(e)).map((e: any) => e[0]),
        user: postModal.data.userId || activeUserId,
        organization:
          selectedOrganization === "" ? undefined : +selectedOrganization,
      };

      if (images.length > 0) {
        const imageIds: number[] = await actionUploadFiles(
          (images.filter((e) => !isArray(e)) as File[]).map(
            (imageFile: File) => ({
              userId: activeUserId,
              file: imageFile,
              type: "post",
            })
          ),
          "post",
          activeUserId
        );

        await Promise.all(
          imageIds.map((imageId: number) => {
            data.files.push(imageId);
          })
        );
      }

      if (postModal.modalType === "edit") {
        data.id = postModal.data.id;
      }

      await actionUpdatePosts([data], false, selectedOrganization === "");
    } finally {
      setIsLoading(false);
      closeModal();
    }
  }, [actionUpdatePosts, actionUploadFiles, activeUserId, closeModal, images, postModal.data.description, postModal.data.id, postModal.data.userId, postModal.modalType, postModal.type, selectedOrganization, setIsLoading, setLoadingLevel]);

  useEffect(() => {
    if (postModal.isOpen && postModal.modalType === "edit") {
      let postImages: [number, string][] = [];

      postModal.data.fileIds?.map((id) => {
        const file = files.find((e) => e[0] === id) || [];

        postImages.push([file[0], file[2]] as [number, string]);
      });

      setImages(postImages);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [postModal.isOpen]);

  return (
    <Modal
      opened={postModal.isOpen}
      onClose={() => closeModal()}
      title={t(`sharePostModal.${postModal.type}Title`)}
      centered
      transition={postModal.isOpen ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
      size={720}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <Stack spacing="md">
        {/* {sharePostModalData.taskId!.length > 0 ? (
          <Task taskId={sharePostModalData.taskId![0]} />
        ) : (
          <Menu
            position="bottom-start"
            transition="fade"
            width={680}
            disabled={isLoading}
          >
            <Menu.Target>
              <Paper
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[7]
                      : theme.colors.gray[0],
                  border: `2px dashed ${
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[2]
                  }`,
                  padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
                  cursor: "pointer",
                  transition: "all .2s ease",
                  ":hover": {
                    filter: "brightness(.9)",
                  },
                })}
              >
                <Group>
                  <PlusSmallIcon style={{ width: 24, height: 24 }} />
                  <Text color="dimmed" size="md">
                    {t("sharePostModal.addTask")}
                  </Text>
                </Group>
              </Paper>
            </Menu.Target>
            <Menu.Dropdown>
              {tasks.map((task: TaskType, i: number) => (
                <Menu.Item
                  key={"task-" + i}
                  onClick={() => {
                    setSharePostModalData({ taskId: [task.id] });
                  }}
                >
                  <Group spacing="sm" align="center">
                    <Box
                      sx={(theme) => ({
                        width: 8,
                        height: 8,
                        borderRadius: "50%",
                        background:
                          theme.colors[priorityColors[task.priority]][9],
                      })}
                    />
                    <Title order={5} sx={{ lineHeight: 1 }}>
                      {task.title}
                    </Title>
                    <Text color="dimmed" size="xs" weight={600}>
                      {formatDate(task.start)} - {formatDate(task.end)}
                    </Text>
                  </Group>
                </Menu.Item>
              ))}
            </Menu.Dropdown>
          </Menu>
        )} */}

        {images.length > 0 && (
          <ImagesOverview images={images} setImages={setImages} />
        )}

        <Textarea
          placeholder={t("sharePostModal.description")}
          minRows={2}
          disabled={isLoading}
          value={postModal.data.description}
          onChange={(event: any) =>
            setPostModal({
              data: {
                ...postModal.data,
                description: event.target.value,
              },
            })
          }
        />

        <Group position="apart">
          <Group>
            <FileButton
              multiple
              onChange={(images) => handleSelect(images)}
              accept="image/png,image/jpeg,image/webp"
            >
              {(props) => (
                <Button
                  {...props}
                  variant="light"
                  color="gray"
                  leftIcon={<PaperClipIcon style={{ width: 16, height: 16 }} />}
                  disabled={isLoading}
                >
                  <Text color="dimmed">{t("sharePostModal.image")} </Text>
                </Button>
              )}
            </FileButton>

            {postModal.type === "post" && (
              <Select
                value={`${selectedOrganization}`}
                data={[
                  {
                    value: "",
                    label: t("none"),
                  },
                  ...customers
                    .filter((e) => e.personIds.includes(activeUserId))
                    .map((e) => ({
                      value: `${e.id}`,
                      label: e.fullName,
                    })),
                ]}
                onChange={(id) => {
                  if (isString(id)) {
                    setSelectedOrganization(id);
                  }
                }}
                styles={{
                  input: {
                    width: 130,
                  },
                }}
              />
            )}
          </Group>

          <Button
            onClick={() => sharePost()}
            disabled={
              postModal.type === "story"
                ? images.length !== 1
                : images.length === 0
            }
          >
            {t("share")}
          </Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default SharePostModal;
