import React from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  ActionIcon,
  Avatar,
  Badge,
  Group,
  Paper,
  Text,
  Title,
} from "@mantine/core";
import { XMarkIcon } from "@heroicons/react/24/outline";
import { TaskTagType } from "~utils/types/Task";
import ActiveAvatar from "~components/ActiveAvatar";

interface PropType {
  taskId: number;
}

export default function Task(props: PropType) {
  const { taskId } = props;
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { tasks } = useStore(
    "tasks",
    (state) => ({
      tasks: state.tasks!,
    }),
    shallow
  );

  const activeTask = tasks.find((e) => e.id === taskId)!;

  return (
    <Paper
      sx={(theme) => ({
        // background: `
        //   repeating-linear-gradient(
        //     45deg,
        //     ${theme.colorScheme === "dark" ? theme.colors.dark[8] : theme.colors.gray[3]},
        //     ${theme.colorScheme === "dark" ? theme.colors.dark[8] : theme.colors.gray[3]} 10px,
        //     ${theme.colorScheme === "dark" ? theme.colors.dark[9] : theme.colors.gray[2]} 10px,
        //     ${theme.colorScheme === "dark" ? theme.colors.dark[9] : theme.colors.gray[2]} 20px
        //   )`,
        border: `2px dashed ${
          theme.colorScheme === "dark"
            ? theme.colors.dark[6]
            : theme.colors.gray[2]
        }`,
        padding: `${theme.spacing.xs}px ${theme.spacing.md}px`,
      })}
    >
      <Group position="apart">
        <Group align="center" spacing="lg">
          <Title
            order={5}
            sx={(theme) => ({
              cursor: "pointer",
              transition: "color .2s ease",
              ":hover": { color: theme.colors.gray[6] },
              lineHeight: 1,
            })}
          >
            {activeTask.title}
          </Title>

          <Group spacing={4}>
            <Text size="xs" color="dimmed">
              Contributors:
            </Text>
            <Avatar.Group>
              {activeTask.contributorIds.map((id: number, i: number) => {
                return (
                  <ActiveAvatar
                    key={"taskContributor-" + i}
                    userId={id}
                    size="sm"
                    radius="xl"
                  />
                );
              })}
            </Avatar.Group>
          </Group>

          {/* <Group>
            {(activeTask.tags || []).map((tag: TaskTagType, i: number) => (
              <Badge key={"taskTag-" + i} color={tag.color}>
                {tag.label}
              </Badge>
            ))}
          </Group> */}
        </Group>

        {/* <ActionIcon
          variant="light"
          onClick={() => setSharePostModalData({ taskId: [] })}
        >
          <XMarkIcon style={{ width: 16, height: 16 }} />
        </ActionIcon> */}
      </Group>
    </Paper>
  );
}
