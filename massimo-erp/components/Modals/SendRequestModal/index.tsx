import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Stack,
  Grid,
  Input,
  NumberInput,
  Textarea,
  Button,
  Group,
  Divider,
  Select,
  MultiSelect,
  SimpleGrid,
  Box,
  Paper,
  Avatar,
  ActionIcon,
  LoadingOverlay,
  Text,
  MultiSelectValueProps,
  ThemeIcon,
  CloseButton,
  Tooltip,
} from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { DateRangePicker, TimeRangeInput } from "@mantine/dates";
import { OfferType, ProjectType, RequestType } from "~utils/types/Project";
import { hexToRGBA, numberFormatter } from "~utils/tools";
import { useElementSize } from "@mantine/hooks";
import {
  InformationCircleIcon,
  PlusIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { UserType } from "~utils/types/User";
import SelectUserModal from "../SelectUserModal";
import { flattenDepth, isEqual, omit, pick, rest, uniq } from "lodash";
import { useTranslation } from "next-i18next";
import ActiveAvatar from "~components/ActiveAvatar";
import { openConfirmModal } from "@mantine/modals";

function Value({
  value,
  label,
  color,
  onRemove,
  classNames,
  ...others
}: MultiSelectValueProps & { value: string; color: string }) {
  return (
    <div {...others}>
      <Box
        sx={(theme) => ({
          display: "flex",
          cursor: "default",
          alignItems: "center",
          backgroundColor: hexToRGBA(color, 0.1),
          paddingLeft: 10,
          borderRadius: 4,
        })}
      >
        <Box sx={{ lineHeight: 1, fontSize: 12 }}>{label}</Box>
        <CloseButton
          onMouseDown={onRemove}
          variant="transparent"
          size={22}
          iconSize={14}
          tabIndex={-1}
        />
      </Box>
    </div>
  );
}

export default function SendRequestModal() {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
      setCustomer: state.setCustomer!,
    }),
    shallow
  );
  const { requests, addRequest, actionUpdateMultipleRequests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
      addRequest: state.addRequest!,
      actionUpdateMultipleRequests: state.actionUpdateMultipleRequests!,
    }),
    shallow
  );
  const { departments, filteredDepartments, actionGetDepartments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
      filteredDepartments: state.filteredDepartments!,
      actionGetDepartments: state.actionGetDepartments!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    sendRequestModal,
    setSendRequestModal,
    setIsOpenSelectUserModal,
    blankRequest,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      sendRequestModal: state.sendRequestModal!,
      setSendRequestModal: state.setSendRequestModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
      blankRequest: state.computed!.blankRequest!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { ref, width } = useElementSize();
  const [userModalType, setUserModalType] = useState<"allowed" | "restricted">(
    "allowed"
  );
  const [allowedUsers, setAllowedUsers] = useState<number[]>([]);
  const [restrictedUsers, setRestrictedUsers] = useState<number[]>([]);
  const [departmentIds, setDepartmentIds] = useState<number[]>([]);

  const activeUser = users.find((user) => user.id === activeUserId);
  const isAdmin =
    (roles.find((role) => role.id === activeUser?.defaultRoleId[0])?.weight ||
      0) < 0;

  useEffect(() => {
    setRestrictedUsers(
      restrictedUsers.filter((e: number) =>
        uniq(
          flattenDepth(
            departments
              .filter((e) => departmentIds.includes(e.id))
              .map((e) => e.userIds),
            2
          )
        ).includes(e)
      )
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [departmentIds]);

  const addUser = useCallback(
    (type: string, id: number) => {
      if (type === "allowed") {
        setAllowedUsers([...allowedUsers, id]);
        setRestrictedUsers(restrictedUsers.filter((e) => e !== id));
      } else if (type === "restricted") {
        setRestrictedUsers([...restrictedUsers, id]);
        setAllowedUsers(allowedUsers.filter((e) => e !== id));
      }
    },
    [allowedUsers, restrictedUsers]
  );

  const removeUser = useCallback(
    (type: string, id: number) => {
      if (type === "allowed") {
        setAllowedUsers(allowedUsers.filter((e) => e !== id));
      } else if (type === "restricted") {
        setRestrictedUsers(restrictedUsers.filter((e) => e !== id));
      }
    },
    [allowedUsers, restrictedUsers]
  );

  const openSelectUserModal = useCallback(
    (type: "allowed" | "restricted") => {
      setUserModalType(type);
      setIsOpenSelectUserModal(true);
    },
    [setUserModalType, setIsOpenSelectUserModal]
  );

  const closeModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setSendRequestModal({
      isOpen: false,
      data: blankRequest,
    });
    setAllowedUsers([]);
    setRestrictedUsers([]);
    setDepartmentIds([]);
  }, [isLoading, loadingLevel, setSendRequestModal, blankRequest]);

  const sendRequest = useCallback(async () => {
    if (
      sendRequestModal.data.name === "" ||
      sendRequestModal.data.budget === 0
    ) {
      return;
    }

    setIsLoading(true);
    setLoadingLevel(1);

    try {
      const newRequest = await actionUpdateMultipleRequests([
        omit(sendRequestModal.data, ["id"]),
      ]);

      if (isAdmin) {
        const newProject: Omit<ProjectType, "id"> = {
          offerIds: ["$offers.-1.id"] as any,
          requestId: newRequest[0].id,
          roleIds: [],
          departmentIds,
          allowedUserIds: allowedUsers,
          restrictedUserIds: restrictedUsers,
          priority: "Medium",
        };

        const newOffer: Omit<OfferType, "id"> = {
          ...(pick(sendRequestModal.data, [
            "name",
            "description",
            "budget",
            "start",
            "end",
          ]) as any),
          status: "Offer",
        };

        await actionUpdateMultipleRequests(
          [
            {
              ...sendRequestModal.data,
              id: newRequest[0].id,
              approverId: activeUserId,
              projectId: "$projects.-1.id" as any,
            },
          ],
          {
            projects: [newProject],
            offers: [newOffer],
            chats: [
              {
                type: "offer",
                ownerId: activeUserId,
                offerId: "$offers.-1.id",
              },
            ],
          }
        );
      }
    } finally {
      setIsLoading(false);
      closeModal();
    }
  }, [
    actionUpdateMultipleRequests,
    activeUserId,
    allowedUsers,
    closeModal,
    departmentIds,
    isAdmin,
    restrictedUsers,
    sendRequestModal.data,
    setIsLoading,
    setLoadingLevel,
  ]);

  const confirmModal = useCallback(() => {
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });
  }, [closeModal, t]);

  useEffect(() => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      actionGetDepartments(1000, 0);
    } finally {
      setIsLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Modal
      title={t("sendRequestModal.title")}
      opened={sendRequestModal.isOpen}
      onClose={() => {
        if (
          !isEqual(
            omit(sendRequestModal.data, [
              "id",
              "start",
              "end",
              "timeRange",
              "departmentIds",
            ]),
            omit(blankRequest, [
              "id",
              "start",
              "end",
              "timeRange",
              "departmentIds",
            ])
          ) ||
          departmentIds.length > 0 ||
          allowedUsers.length > 0 ||
          restrictedUsers.length > 0
        ) {
          confirmModal();
        } else {
          closeModal();
        }
      }}
      centered
      transition={sendRequestModal.isOpen ? "slide-down" : "slide-up"}
      size={800}
      zIndex={1000}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <SelectUserModal
        userList={
          userModalType === "restricted"
            ? uniq(
                flattenDepth(
                  departments
                    .filter((e) => departmentIds.includes(e.id))
                    .map((e) => e.userIds),
                  2
                )
              )
            : departmentIds.length > 0
            ? users
                .filter(
                  (user: UserType) =>
                    !user.departmentIds?.some((id: number) =>
                      departmentIds.includes(id)
                    )
                )
                .map((e) => e.id)
            : undefined
        }
        onesList={userModalType === "allowed" ? allowedUsers : restrictedUsers}
        handleSelect={(id: number) => addUser(userModalType, id)}
        handleRemove={(id: number) => removeUser(userModalType, id)}
      />
      <Stack ref={ref}>
        <Grid>
          <Grid.Col span={width < 550 && width > 0 ? 12 : 6}>
            <Input.Wrapper
              label={t("sendRequestModal.titleInput.label")}
              id="newRequestTitleInput"
            >
              <Input
                id="newRequestTitleInput"
                placeholder={t("sendRequestModal.titleInput.placeholder")}
                disabled={isLoading && loadingLevel === 1}
                value={sendRequestModal.data.name}
                onChange={(event: any) =>
                  setSendRequestModal({
                    data: {
                      ...sendRequestModal.data,
                      name: event.target.value,
                    },
                  })
                }
              />
            </Input.Wrapper>
          </Grid.Col>
          <Grid.Col span={width < 550 && width > 0 && width > 0 ? 12 : 6}>
            <Select
              value={
                sendRequestModal.isCustomer !== undefined
                  ? `${sendRequestModal.isCustomer}`
                  : `${sendRequestModal.data.customerId}`
              }
              disabled={sendRequestModal.isCustomer !== undefined}
              label={t("sendRequestModal.customer")}
              placeholder={t("sendRequestModal.customer")}
              data={customers
                .filter((e) => {
                  return isAdmin || (e.personIds || []).includes(activeUserId);
                })
                .map((e) => ({
                  value: `${e.id}`,
                  label: e.fullName,
                }))}
              onChange={(value: string) =>
                setSendRequestModal({
                  data: {
                    ...sendRequestModal.data,
                    customerId: +value,
                  },
                })
              }
            />
          </Grid.Col>
          {isAdmin && (
            <Grid.Col span={width < 550 && width > 0 ? 12 : 6}>
              <MultiSelect
                data={filteredDepartments
                  .filter((e) => e.isVisible)
                  .map((e) => ({
                    value: `${e.id}`,
                    label: e.label,
                    color: e.color,
                  }))}
                onChange={(value) => setDepartmentIds(value.map((e) => +e))}
                valueComponent={Value}
                disabled={isLoading && loadingLevel === 1}
                label={t("sendRequestModal.departments.label")}
                placeholder={t("sendRequestModal.departments.placeholder")}
              />
            </Grid.Col>
          )}
          <Grid.Col span={width < 550 && width > 0 ? 12 : 6}>
            <NumberInput
              label={t("sendRequestModal.price")}
              hideControls
              disabled={isLoading && loadingLevel === 1}
              value={sendRequestModal.data.budget}
              onChange={(value: number) =>
                setSendRequestModal({
                  data: {
                    ...sendRequestModal.data,
                    budget: value,
                  },
                })
              }
              parser={(value) => value!.replace(/\₺\s?|(,*)/g, "")}
              formatter={(value) => numberFormatter(value as string)}
            />
          </Grid.Col>
          <Grid.Col span={width < 550 && width > 0 ? 12 : 6}>
            <DateRangePicker
              allowSingleDateInRange
              label={t("offersModal.dateRange")}
              placeholder={t("offersModal.dateRange")}
              disabled={isLoading && loadingLevel === 1}
              defaultValue={[new Date(), new Date()]}
              value={[sendRequestModal.data.start, sendRequestModal.data.end]}
              onChange={(value: [Date, Date]) =>
                setSendRequestModal({
                  data: {
                    ...sendRequestModal.data,
                    start: value[0],
                    end: value[1],
                  },
                })
              }
            />
          </Grid.Col>
          <Grid.Col span={width < 550 && width > 0 ? 12 : 6}>
            <TimeRangeInput
              label={t("offersModal.timeRange")}
              placeholder={t("offersModal.timeRange")}
              disabled={isLoading && loadingLevel === 1}
              value={[sendRequestModal.data.start, sendRequestModal.data.end]}
              onChange={([start, end]: [Date, Date]) => {
                const newStartDate = new Date(sendRequestModal.data.start);
                const newEndDate = new Date(sendRequestModal.data.end);

                if (start) {
                  newStartDate.setHours(start.getHours());
                  newStartDate.setMinutes(start.getMinutes());
                } else {
                  newStartDate.setHours(0);
                  newStartDate.setMinutes(0);
                }

                if (end) {
                  newEndDate.setHours(end.getHours());
                  newEndDate.setMinutes(end.getMinutes());
                } else {
                  newStartDate.setHours(23);
                  newStartDate.setMinutes(59);
                }

                setSendRequestModal({
                  data: {
                    ...sendRequestModal.data,
                    start: newStartDate,
                    end: newEndDate,
                  },
                });
              }}
            />
          </Grid.Col>
          <Grid.Col span={12}>
            <Textarea
              placeholder={t("sendRequestModal.description.placeholder")}
              label={t("sendRequestModal.description.label")}
              autosize
              minRows={2}
              disabled={isLoading && loadingLevel === 1}
              maxRows={4}
              value={sendRequestModal.data.description}
              onChange={(event) =>
                setSendRequestModal({
                  data: {
                    ...sendRequestModal.data,
                    description: event.currentTarget.value,
                  },
                })
              }
            />
          </Grid.Col>
        </Grid>
        {isAdmin && (
          <>
            <Divider
              color="green"
              label={t("sendRequestModal.allowedUsers")}
              labelPosition="center"
            />
            <SimpleGrid cols={width < 600 && width > 0 ? 1 : 3}>
              {users
                .filter((e) => allowedUsers.includes(e.id))
                .map((user: UserType, i: number) => (
                  <Paper
                    key={user.id}
                    sx={(theme) => ({
                      position: "relative",
                      background:
                        theme.colorScheme === "dark"
                          ? theme.colors.dark[6]
                          : theme.colors.gray[1],
                      padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                    })}
                  >
                    <Group
                      noWrap
                      align="center"
                      sx={{
                        position: "relative",
                        width: "100%",
                        height: "100%",
                      }}
                    >
                      <ActiveAvatar userId={user.id} radius="xl" />
                      <Tooltip label={`${user.name} ${user.surname}`}>
                        <Text
                          size="sm"
                          weight={600}
                          sx={{
                            width: "100%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                        >
                          {user.name} {user.surname}
                        </Text>
                      </Tooltip>
                      <ActionIcon
                        size="sm"
                        onClick={() => removeUser("allowed", user.id)}
                      >
                        <XMarkIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                ))}
              {allowedUsers.length !== users.length && (
                <Box
                  sx={(theme) => ({
                    minHeight: 58,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: theme.radius.md,
                    border: `2px dashed ${
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[6]
                        : theme.colors.gray[2]
                    }`,
                    cursor: "pointer",
                    transition: "filter .2s ease",
                    ":hover": {
                      filter: "brightness(.9)",
                    },
                  })}
                  onClick={() => openSelectUserModal("allowed")}
                >
                  <PlusIcon style={{ width: 24, height: 24 }} />
                </Box>
              )}
            </SimpleGrid>
            <Divider
              color="red"
              label={t("sendRequestModal.restrictedUsers")}
              labelPosition="center"
            />
            <SimpleGrid cols={width < 600 && width > 0 ? 1 : 3}>
              {users
                .filter((e) => restrictedUsers.includes(e.id))
                .map((user: UserType, i: number) => (
                  <Paper
                    key={user.id}
                    sx={(theme) => ({
                      background:
                        theme.colorScheme === "dark"
                          ? theme.colors.dark[6]
                          : theme.colors.gray[1],
                      padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                    })}
                  >
                    <Group noWrap align="center" sx={{ height: "100%" }}>
                      <ActiveAvatar userId={user.id} radius="xl" />
                      <Tooltip label={`${user.name} ${user.surname}`}>
                        <Text
                          size="sm"
                          weight={600}
                          sx={{
                            width: "100%",
                            whiteSpace: "nowrap",
                            textOverflow: "ellipsis",
                            overflow: "hidden",
                          }}
                        >
                          {user.name} {user.surname}
                        </Text>
                      </Tooltip>
                      <ActionIcon
                        size="sm"
                        onClick={() => removeUser("restricted", user.id)}
                      >
                        <XMarkIcon style={{ width: 16, height: 16 }} />
                      </ActionIcon>
                    </Group>
                  </Paper>
                ))}
              {restrictedUsers.length !== users.length && (
                <Box
                  sx={(theme) => ({
                    minHeight: 58,
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: theme.radius.md,
                    border: `2px dashed ${
                      theme.colorScheme === "dark"
                        ? theme.colors.dark[6]
                        : theme.colors.gray[2]
                    }`,
                    cursor: "pointer",
                    transition: "filter .2s ease",
                    ":hover": {
                      filter: "brightness(.9)",
                    },
                  })}
                  onClick={() => openSelectUserModal("restricted")}
                >
                  <PlusIcon style={{ width: 24, height: 24 }} />
                </Box>
              )}
            </SimpleGrid>
          </>
        )}
        <Divider orientation="horizontal" />
        <Group position="apart">
          {isAdmin ? (
            <Group spacing={8}>
              <ThemeIcon variant="light">
                <InformationCircleIcon style={{ width: 20, height: 20 }} />
              </ThemeIcon>
              <Text size="xs" weight={600} color="dimmed">
                {t("requestInfo")}
              </Text>
            </Group>
          ) : (
            <Group></Group>
          )}
          <Group>
            <Button variant="light" color="gray" onClick={() => closeModal()}>
              {t("cancel")}
            </Button>
            <Button onClick={() => sendRequest()}>{t("send")}</Button>
          </Group>
        </Group>
      </Stack>
    </Modal>
  );
}
