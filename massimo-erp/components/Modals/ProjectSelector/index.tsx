import {
  ArrowRightIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";
import {
  Modal,
  SimpleGrid,
  Box,
  Stack,
  Group,
  Skeleton,
  Text,
  Select,
  Pagination,
  Button,
  Divider,
  Input,
  Tooltip,
} from "@mantine/core";
import { useElementSize, useForceUpdate, useMediaQuery } from "@mantine/hooks";
import { closeModal } from "@mantine/modals";
import { orderBy } from "lodash";
import { useTranslation } from "next-i18next";
import React, { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { CC } from "~types";
import { useStore } from "~utils/store";
import { hexToRGBA } from "~utils/tools";
import { ProjectType } from "~utils/types/Project";

const ProjectSelectorModalContent: CC<{
  closeModal: () => void;
}> = function ({ closeModal }) {
  const { selectedProjectId, activeUserId, setSelectedProject } = useStore(
    "global",
    (state) => ({
      selectedProjectId: state.selectedProjectId!,
      setSelectedProject: state.setSelectedProject!,
      activeUserId: state.activeUserId,
    }),
    shallow
  );
  const { setSendRequestModal, setIsOpenProjectSelectorModal } = useStore(
    "temp",
    (state) => ({
      setSendRequestModal: state.setSendRequestModal!,
      setIsOpenProjectSelectorModal: state.setIsOpenProjectSelectorModal!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { roles } = useStore(
    "roles",
    (state) => ({
      roles: state.roles!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 520px)");
  const { width, ref } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("9");
  const [activePage, setActivePage] = useState(1);

  const activeUser = users.find((user) => user.id === activeUserId);
  const activeUserDefaultRoleWeight = roles.find(
    (role) => role.id === activeUser?.defaultRoleId[0]
  )?.weight;
  const activeProject = projects.find(
    (project) => project.id === selectedProjectId
  );

  const handleSelect = useCallback(
    (id: number) => {
      setSelectedProject(id);
      closeModal();
    },
    [setSelectedProject, closeModal]
  );

  const deselect = useCallback(() => {
    setSelectedProject(undefined);
    closeModal();
  }, [closeModal, setSelectedProject]);

  useEffect(() => {
    if (activeProject) {
      if (activeProject.restrictedUserIds?.includes(activeUser?.id!)) {
        setSelectedProject();
      }
    }
  }, [activeProject, activeUser?.id, setSelectedProject]);

  return (
    <Stack ref={ref}>
      <Group
        position={width < 520 && width !== 0 ? "center" : "apart"}
        align="center"
      >
        <Group>
          <Text size="xs" weight={600} color="dimmed">
            {t("rowPerPage")}
          </Text>
          <Select
            size="xs"
            value={rowsPerPage}
            onChange={(value: string) => setRowsPerPage(value)}
            data={[
              { value: "9", label: "9" },
              { value: "18", label: "18" },
              { value: "36", label: "36" },
            ]}
            sx={{
              width: 100,
            }}
            transition="fade"
            transitionDuration={200}
          />
        </Group>
        <Group position="center">
          <Input
            size="xs"
            icon={<MagnifyingGlassIcon style={{ width: 16, height: 16 }} />}
            placeholder={t("search")}
            styles={{
              input: {
                width: 140,
              },
            }}
          />
          <Pagination
            page={activePage}
            onChange={setActivePage}
            total={
              projects.length < +rowsPerPage
                ? 1
                : Math.ceil(projects.length / +rowsPerPage)
            }
            size="sm"
          />
        </Group>
      </Group>

      <SimpleGrid
        cols={!matches || (width > 0 && width >= 760) ? 3 : width > 520 ? 2 : 1}
      >
        {orderBy(
          projects.filter((x) => {
            return !activeUser?.restrictedProjectIds.includes(x.id);
          }),
          "id"
        ).map((project: ProjectType, i: number) => {
          const lastOffer = offers.filter(
            (e) => e.id === project.offerIds[project.offerIds.length - 1]
          )[0];
          const lastRequest = requests.filter(
            (e) => e.id === project.requestId
          )[0];
          const isActive = project.id === selectedProjectId;
          const activeCustomer = customers.find(
            (e) => e.id === lastRequest?.customerId
          )?.fullName;

          return (
            <Box
              key={"project-" + i}
              sx={(theme) => ({
                background: isActive
                  ? hexToRGBA(theme.colors.blue[9], 0.3)
                  : theme.colorScheme === "dark"
                  ? theme.colors.dark[6]
                  : theme.colors.gray[2],
                padding: theme.spacing.md,
                borderRadius: 8,
                cursor: "pointer",
                transition: "filter .2s ease",
                userSelect: "none",
                ":hover": {
                  filter: "brightness(.9)",
                },
              })}
              onClick={() => {
                if (isActive) {
                  return;
                }
                handleSelect(project.id);
              }}
            >
              <Stack
                spacing={8}
                sx={{
                  position: "relative",
                }}
              >
                <Text size="sm" weight={600}>
                  {lastOffer.name}
                </Text>
                <Group
                  spacing={8}
                  sx={{
                    position: "relative",
                    width: "100%",
                  }}
                  noWrap
                >
                  <ArrowRightIcon
                    style={{ width: 16, height: 16, flexShrink: 0 }}
                  />
                  <Tooltip label={activeCustomer}>
                    <Text
                      size="xs"
                      weight={600}
                      color="dimmed"
                      sx={(theme) => ({
                        maxWidth: "100%",
                        whiteSpace: "nowrap",
                        textOverflow: "ellipsis",
                        overflow: "hidden",
                      })}
                    >
                      {activeCustomer}
                    </Text>
                  </Tooltip>
                </Group>
              </Stack>
            </Box>
          );
        })}
      </SimpleGrid>
      <Divider />
      <Group position="apart">
        <Group>
          {(activeUserDefaultRoleWeight || 0) < 0 && (
            <Button
              onClick={() => {
                setIsOpenProjectSelectorModal(false);
                setSendRequestModal({
                  isOpen: true,
                  isCustomer: undefined,
                });
              }}
            >
              {t("createProject")}
            </Button>
          )}
          {selectedProjectId && (
            <Button variant="light" color="gray" onClick={() => deselect()}>
              {t("deselect")}
            </Button>
          )}
        </Group>
        <Button variant="light" color="gray" onClick={() => closeModal()}>
          {t("close")}
        </Button>
      </Group>
    </Stack>
  );
};

export default function ProjectSelectorModal() {
  const { isOpenProjectSelectorModal, setIsOpenProjectSelectorModal } =
    useStore(
      "temp",
      (state) => ({
        isOpenProjectSelectorModal: state.isOpenProjectSelectorModal!,
        setIsOpenProjectSelectorModal: state.setIsOpenProjectSelectorModal!,
      }),
      shallow
    );

  const { t } = useTranslation();

  const closeModal = useCallback(() => {
    setIsOpenProjectSelectorModal(false);
  }, [setIsOpenProjectSelectorModal]);

  return (
    <Modal
      title={t("projectSelector")}
      opened={isOpenProjectSelectorModal}
      onClose={() => closeModal()}
      centered
      transition={isOpenProjectSelectorModal ? "slide-down" : "slide-up"}
      size={800}
      zIndex={2000}
    >
      <ProjectSelectorModalContent closeModal={closeModal} />
    </Modal>
  );
}
