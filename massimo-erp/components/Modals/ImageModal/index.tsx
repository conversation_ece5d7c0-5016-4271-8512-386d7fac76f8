import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import { Modal, Image } from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";

const ImageModal = function () {
  const matches = useMediaQuery("(max-width: 992px)");

  const { imageModal, setImageModal } = useStore(
    "temp",
    (state) => ({
      imageModal: state.imageModal!,
      setImageModal: state.setImageModal!,
    }),
    shallow
  );

  return (
    <Modal
      opened={imageModal !== undefined}
      onClose={() => setImageModal(undefined)}
      onClick={() => setImageModal(undefined)}
      centered
      transition={imageModal !== undefined ? "slide-down" : "slide-up"}
      overflow="outside"
      size={matches ? "100%" : "80%"}
      withCloseButton={false}
      sx={{
        ".mantine-Modal-modal": {
          padding: 0,
        },
        zIndex: 100000000,
      }}
    >
      <Image
        width={"100%"}
        src={imageModal}
        alt="img"
        onClick={() => setImageModal(undefined)}
        withPlaceholder
      />
    </Modal>
  );
};

export default ImageModal;
