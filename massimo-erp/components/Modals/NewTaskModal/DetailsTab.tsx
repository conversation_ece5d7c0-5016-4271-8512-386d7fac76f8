import { forwardRef } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Stack,
  Select,
  Input,
  Group,
  MultiSelect,
  Text,
  Grid,
  TextInput,
} from "@mantine/core";
import { DateRangePicker, TimeRangeInput } from "@mantine/dates";
import { TaskPriorityType, TaskTagType, TaskType } from "~utils/types/Task";
import { useElementSize, useMediaQuery } from "@mantine/hooks";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { UseFormReturnType } from "@mantine/form";

interface ItemProps extends React.ComponentPropsWithoutRef<"div"> {
  label: string;
  customer: string;
}

const SelectItem = forwardRef<HTMLDivElement, ItemProps>(function Item(
  { label, customer, ...others }: ItemProps,
  ref
) {
  return (
    <div ref={ref} {...others}>
      <Stack spacing={4}>
        <Text size="sm" weight={600}>
          {label}
        </Text>
        <Text size="xs">{customer}</Text>
      </Stack>
    </div>
  );
});

const DetailsTab: CC<{
  form: UseFormReturnType<Partial<TaskType>>;
}> = function ({ form }) {
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { tags } = useStore(
    "data",
    (state) => ({
      tags: state.tags!,
    }),
    shallow
  );
  const {
    isLoading,
    isOpenNewTaskModal,
    setIsOpenNewTaskModal,
    newTaskModalData,
    setNewTaskModalData,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      isOpenNewTaskModal: state.isOpenNewTaskModal!,
      setIsOpenNewTaskModal: state.setIsOpenNewTaskModal!,
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId,
    }),
    shallow
  );

  const activeUser = users.find((user) => user.id === activeUserId);
  const { t } = useTranslation();
  const { ref, width } = useElementSize();

  return (
    <Stack ref={ref}>
      <Grid>
        <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <Select
            withAsterisk
            label={t("newTask.project.label")}
            placeholder={t("newTask.project.placeholder")}
            itemComponent={SelectItem}
            data={projects
              .filter((project) => {
                return !project.restrictedUserIds?.includes(activeUser?.id!);
              })
              .map((project) => {
                const lastOffer = offers.filter(
                  (o) => o.id === project.offerIds[project.offerIds.length - 1]
                )[0];
                const activeRequest = requests.filter(
                  (e) => e.id === project.requestId
                )[0];

                return {
                  value: `${project.id}`,
                  label: lastOffer.name,
                  customer: customers.find(
                    (c) => c.id === activeRequest?.customerId
                  )?.fullName,
                };
              })}
            // value={`${newTaskModalData.projectId}`}
            // onChange={(value: string) =>
            //   setNewTaskModalData({
            //     projectId: +value,
            //   })
            // }
            {...form.getInputProps("projectId")}
            disabled={isLoading}
            transition="fade"
            transitionDuration={200}
          />
        </Grid.Col>
        <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <TextInput
            withAsterisk
            label={t("newTask.taskTitle.label")}
            placeholder={t("newTask.taskTitle.placeholder")}
            disabled={isLoading}
            // value={newTaskModalData.title}
            // onChange={(event: any) =>
            //   setNewTaskModalData({
            //     title: event.target.value,
            //   })
            // }
            {...form.getInputProps("title")}
          />
        </Grid.Col>
        {/* <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <MultiSelect
            label={t("newTask.tags.label")}
            data={tags}
            placeholder={t("newTask.tags.placeholder")}
            disabled={isLoading}
            value={newTaskModalData.tags.map((v: TaskTagType) => v.value)}
            onChange={(value: string[]) =>
              setNewTaskModalData({
                tags: value.map(
                  (v1) => tags.filter((v2) => v2.value === v1)[0]
                ),
              })
            }
            transition="fade"
            transitionDuration={200}
          />
        </Grid.Col> */}
        <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <DateRangePicker
            allowSingleDateInRange
            withAsterisk
            label={t("newTask.dateRange.label")}
            placeholder={t("newTask.dateRange.label")}
            disabled={isLoading}
            value={[newTaskModalData.start, newTaskModalData.end]}
            onChange={(value) =>
              setNewTaskModalData({
                start: value[0]!,
                end: value[1]!,
              })
            }
          />
        </Grid.Col>
        <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <TimeRangeInput
            withAsterisk
            label={t("newTask.timeRange.label")}
            disabled={isLoading}
            value={[newTaskModalData.start, newTaskModalData.end]}
            onChange={(value) => {
              const [start, end] = value;

              const newStartDate = new Date(+newTaskModalData.start);
              const newEndDate = new Date(+newTaskModalData.end);

              if (start) {
                newStartDate.setHours(start.getHours());
                newStartDate.setMinutes(start.getMinutes());
              } else {
                newStartDate.setHours(0);
                newStartDate.setMinutes(0);
              }

              if (end) {
                newEndDate.setHours(end.getHours());
                newEndDate.setMinutes(end.getMinutes());
              } else {
                newStartDate.setHours(23);
                newStartDate.setMinutes(59);
              }

              setNewTaskModalData({
                start: newStartDate,
                end: newEndDate,
              });
            }}
            clearable
          />
        </Grid.Col>
        <Grid.Col span={width <= 500 && width > 0 ? 12 : 6}>
          <Select
            withAsterisk
            label={t("newTask.priority.label")}
            placeholder={t("newTask.priority.placeholder")}
            data={["Very Low", "Low", "Medium", "High", "Very High"].map(
              (e) => ({
                value: e,
                label: t(`priority.${e}`),
              })
            )}
            disabled={isLoading}
            // value={newTaskModalData.priority}
            // onChange={(value: TaskPriorityType) =>
            //   setNewTaskModalData({
            //     priority: value,
            //   })
            // }
            {...form.getInputProps("priority")}
            transition="fade"
            transitionDuration={200}
          />
        </Grid.Col>
      </Grid>
    </Stack>
  );
};

export default DetailsTab;
