import dynamic from "next/dynamic";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

const DynamicTextEditor = dynamic(() => import("@mantine/rte"), {
  ssr: false,
});

const DescriptionTab = function () {
  const { newTaskModalData, setNewTaskModalData } = useStore(
    "temp",
    (state) => ({
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
    }),
    shallow
  );

  return (
    <DynamicTextEditor
      value={newTaskModalData.description}
      onChange={(value: string) => setNewTaskModalData({ description: value })}
    />
  );
};

export default DescriptionTab;
