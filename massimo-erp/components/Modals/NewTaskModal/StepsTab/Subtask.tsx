import { useCallback, useEffect, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";
import { TaskContentType } from "~utils/types/Task";

import {
  Bars3BottomRightIcon,
  ChevronDownIcon,
  PlusSmallIcon,
  XMarkIcon,
  CalendarIcon,
  TrashIcon,
} from "@heroicons/react/24/outline";
import {
  Stack,
  Group,
  ThemeIcon,
  Input,
  ActionIcon,
  Box,
  Space,
  Title,
  Tooltip,
  Collapse,
  Text,
  Avatar,
  Divider,
  Menu,
} from "@mantine/core";
import { formatDate } from "~utils/tools";
import { RangeCalendar } from "@mantine/dates";
import { getHotkeyHandler } from "@mantine/hooks";
import { cloneDeep, findLast } from "lodash";
import { UserType } from "~utils/types/User";
import ActiveAvatar from "~components/ActiveAvatar";
import { MessageType } from "~utils/types/Chat";
import { useTranslation } from "next-i18next";

interface PropType {
  content: {
    type: string;
    subtasks: {
      id: number;
      completed: boolean;
      label: string;
    }[];
    contributors: number[];
    start: Date;
    end: Date;
  };
  setSubtask: (data: any) => void;
  removeSubtasks: () => void;
}

const Subtask = function (props: PropType) {
  const { content, setSubtask, removeSubtasks } = props;
  const { t } = useTranslation();

  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const [subtasksCollapsed, setSubtasksCollapsed] = useState(true);
  const [newSubtask, setNewSubtask] = useState("");

  const addNewSubtask = useCallback(() => {
    if (newSubtask === "") {
      return;
    }

    setSubtask({
      content: {
        ...content,
        subtasks: [
          ...content.subtasks,
          {
            id: content.subtasks.length,
            completed: false,
            label: newSubtask,
          },
        ],
      },
    });

    setNewSubtask("");
  }, [newSubtask, setSubtask, content]);

  const updateSubtask = useCallback(
    (i: number, value: string) => {
      let newContent = content.subtasks;
      newContent[i].label = value;
      setSubtask({
        content: {
          ...content,
          subtasks: newContent,
        },
      });
    },
    [content, setSubtask]
  );

  const deleteSubTask = useCallback(
    (i: number) => {
      let newContent = content.subtasks;
      newContent.splice(i, 1);
      setSubtask({
        content: {
          ...content,
          subtasks: newContent,
        },
      });
    },
    [content, setSubtask]
  );

  const changeDateList = useCallback(
    ([start, end]: Date[]) => {
      setSubtask({
        content: {
          ...content,
          start,
          end,
        },
      });
    },
    [content, setSubtask]
  );

  return (
    <Stack sx={{ width: "100%" }}>
      <Group position="apart">
        <Group>
          <ActionIcon
            onClick={() => setSubtasksCollapsed(!subtasksCollapsed)}
            size={24}
          >
            <ChevronDownIcon
              style={{
                width: 16,
                height: 16,
                transform: subtasksCollapsed ? "rotate(180deg)" : "",
                transition: "transform .2s ease",
              }}
            />
          </ActionIcon>
          <Title order={6}>{t("newTask.subtasks")}</Title>
        </Group>

        <Group>
          {/* <Group align="center" spacing={8}>
            <Text size="xs" color="dimmed">
              Contributors
            </Text>
            {content.contributors!.map((userId: number, i: number) => {
              const activeUser = users.find((e) => e.id === userId)!;

              return (
                <Tooltip
                  key={"subtaskContributor-" + i}
                  label={`${activeUser.name} ${activeUser.surname}`}
                >
                  <ActiveAvatar
                    userId={userId}
                    size="sm"
                    radius="xl"
                    // onClick={() => removeContributor(i)}
                  />
                </Tooltip>
              );
            })}
            <Menu shadow="md" width={150} transition="fade">
              <Menu.Target>
                <Avatar radius="xl" size="sm">
                  <ActionIcon>
                    <PlusSmallIcon style={{ width: 12, height: 12 }} />
                  </ActionIcon>
                </Avatar>
              </Menu.Target>

              <Menu.Dropdown>
                {users
                  .filter((e) => !content.contributors?.includes(e.id))
                  .map((user: UserType, i: number) => {
                    return (
                      <Menu.Item
                        key={"attachableUser-" + i}
                        p={8}
                        // onClick={() => addContributor(user.id)}
                      >
                        <Group pl="md" p={0}>
                          <ActiveAvatar
                            userId={user.id}
                            size="sm"
                            radius="xl"
                          />
                          {user.name} {user.surname}
                        </Group>
                      </Menu.Item>
                    );
                  })}
              </Menu.Dropdown>
            </Menu>
          </Group>
          <Divider orientation="vertical" my={4} /> */}
          {/* <Group align="center" spacing={8}>
            <Text size="xs" color="dimmed">
              {formatDate(content.start)} - {formatDate(content.end)}
            </Text>
            <Menu>
              <Menu.Target>
                <ActionIcon color="blue" variant="light">
                  <CalendarIcon style={{ width: 16, height: 16 }} />
                </ActionIcon>
              </Menu.Target>

              <Menu.Dropdown>
                <RangeCalendar
                  value={[content.start, content.end] as [Date, Date]}
                  onChange={(value: Date[]) => changeDateList(value)}
                />
              </Menu.Dropdown>
            </Menu>
          </Group>
          <Divider orientation="vertical" my={4} /> */}
          <ActionIcon color="dark" onClick={() => removeSubtasks()}>
            <TrashIcon style={{ width: 16, height: 16 }} />
          </ActionIcon>
        </Group>
      </Group>
      <Collapse in={subtasksCollapsed}>
        {content.subtasks.length > 0 && (
          <Stack spacing="xs">
            {content.subtasks.map((subtask: any, i: number) => (
              <Group
                key={"subtask-" + i}
                noWrap
                spacing="sm"
                sx={{ width: "100%" }}
              >
                <ThemeIcon color="blue" variant="light" size={24} radius="xl">
                  <Bars3BottomRightIcon style={{ width: 16, height: 16 }} />
                </ThemeIcon>
                <Input
                  value={subtask.label}
                  onChange={(event: any) =>
                    updateSubtask(i, event.target.value)
                  }
                  size="xs"
                  sx={{ width: "100%" }}
                />
                <ActionIcon
                  color="red"
                  variant="subtle"
                  onClick={() => deleteSubTask(i)}
                >
                  <XMarkIcon style={{ width: 16, height: 16 }} />
                </ActionIcon>
              </Group>
            ))}
          </Stack>
        )}
        <Box style={{ display: "flex" }} mt="sm">
          <Input
            size="xs"
            placeholder="New Subtask Label"
            sx={{ flexGrow: 1 }}
            value={newSubtask}
            onChange={(event: any) => setNewSubtask(event.target.value)}
            onKeyDown={getHotkeyHandler([["Enter", () => addNewSubtask()]])}
          />
          <Space w="sm" />
          <ActionIcon
            color="green"
            variant="light"
            onClick={() => addNewSubtask()}
          >
            <PlusSmallIcon />
          </ActionIcon>
        </Box>
      </Collapse>
    </Stack>
  );
};

export default Subtask;
