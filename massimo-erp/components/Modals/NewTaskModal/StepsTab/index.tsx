import { useCallback, useEffect, useState } from "react";

import {
  ActionIcon,
  Box,
  Checkbox,
  Group,
  Input,
  List,
  Space,
  Stack,
  ThemeIcon,
  Text,
  Button,
} from "@mantine/core";
import {
  Bars3BottomRightIcon,
  CheckCircleIcon,
  PencilIcon,
  PlusSmallIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { TaskContentType, TaskSubtaskType } from "~utils/types/Task";
import Subtask from "./Subtask";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { cloneDeep } from "lodash";
import { useTranslation } from "next-i18next";
import { MessageType } from "~utils/types/Chat";
import { CC } from "~types";

const StepsTab: CC<{
  subtaskMessages: Omit<MessageType, "id" | "createdAt">[];
  setSubtaskMessages: (value: Omit<MessageType, "id" | "createdAt">[]) => void;
}> = function ({ subtaskMessages, setSubtaskMessages }) {
  const { t } = useTranslation();

  // const setSubtasks = useCallback(
  //   (data: any) => {
  //     console.debug(data);
  //     // let copyContents = cloneDeep(newTaskModalData.contents);
  //     // let index = copyContents.findIndex((e) => e.id === data.id);

  //     // copyContents[index] = {
  //     //   ...copyContents[index],
  //     //   ...data,
  //     // };

  //     // setNewTaskModalData({
  //     //   contents: copyContents,
  //     // });
  //   },
  //   [newTaskModalData.contents, setNewTaskModalData]
  // );

  // const removeSubtasks = useCallback(
  //   (subtasks: TaskContentType) => {
  //     let newContent = newTaskModalData.contents.filter(
  //       (e) => e.id !== subtasks.id
  //     );
  //     setNewTaskModalData({
  //       contents: newContent,
  //     });
  //   },
  //   [newTaskModalData.contents, setNewTaskModalData]
  // );

  return (
    <Stack>
      {subtaskMessages.length > 0 ? (
        (subtaskMessages as MessageType[]).map(
          (subtask: MessageType, i: number) => (
            <Subtask
              key={"subtaskContent-" + i}
              content={
                subtask.content as {
                  type: string;
                  subtasks: {
                    id: number;
                    completed: boolean;
                    label: string;
                  }[];
                  contributors: number[];
                  start: Date;
                  end: Date;
                }
              }
              setSubtask={(edited: any) => {
                let d = cloneDeep(subtaskMessages);
                d[i] = { ...d[i], ...edited };

                setSubtaskMessages(d);
              }}
              removeSubtasks={() =>
                setSubtaskMessages(
                  subtaskMessages.filter((e, index) => index !== i)
                )
              }
            />
          )
        )
      ) : (
        <Text align="center" size="sm" weight={600} color="dimmed" my="md">
          {t("newTask.noSubtask")}
        </Text>
      )}
    </Stack>
  );
};

export default StepsTab;
