import { Dispatch, SetStateAction, useCallback } from "react";
import {
  Group,
  Text,
  useMantineTheme,
  Stack,
  Image,
  Divider,
  ThemeIcon,
  CloseButton,
  ScrollArea,
  Paper,
  SimpleGrid,
  Space,
} from "@mantine/core";
import { Dropzone, DropzoneProps, IMAGE_MIME_TYPE } from "@mantine/dropzone";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import {
  ArrowUpTrayIcon,
  DocumentIcon,
  PhotoIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { useElementSize } from "@mantine/hooks";

const FilesTab: CC<{
  files: File[];
  setFiles: Dispatch<SetStateAction<File[]>>;
  images: File[];
  setImages: Dispatch<SetStateAction<File[]>>;
}> = function ({ files, setFiles, images, setImages }) {
  const { newTaskModalData, setNewTaskModalData } = useStore(
    "temp",
    (state) => ({
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
    }),
    shallow
  );

  const { ref, width } = useElementSize();
  const { t } = useTranslation();
  const theme = useMantineTheme();

  const handleDrop = useCallback(
    (rawFiles: File[]) => {
      rawFiles.map((file) => {
        if (["image/png", "image/jpeg", "image/webp"].includes(file.type)) {
          setImages((imgs) => [...imgs, file]);
        } else {
          setFiles((fls) => [...fls, file]);
        }
      });
    },
    [setFiles, setImages]
  );

  return (
    <Stack ref={ref}>
      <Dropzone
        onDrop={(files) => handleDrop(files)}
        onReject={(files) => console.debug("rejected files", files)}
        multiple
        maxFiles={20}
        maxSize={1024 * 1024 * 10}
      >
        <Group
          position="center"
          spacing="xl"
          style={{ minHeight: 220, pointerEvents: "none" }}
        >
          <Dropzone.Accept>
            <ArrowUpTrayIcon
              style={{
                width: 50,
                height: 50,
                color:
                  theme.colors[theme.primaryColor][
                    theme.colorScheme === "dark" ? 4 : 6
                  ],
              }}
            />
          </Dropzone.Accept>
          <Dropzone.Reject>
            <XMarkIcon
              style={{
                width: 50,
                height: 50,
                color:
                  theme.colors[theme.primaryColor][
                    theme.colorScheme === "dark" ? 4 : 6
                  ],
              }}
            />
          </Dropzone.Reject>
          <Dropzone.Idle>
            <PhotoIcon
              style={{
                width: 50,
                height: 50,
              }}
            />
          </Dropzone.Idle>

          <div>
            <Text size="xl" inline>
              {t("newTask.filesTab.title")}
            </Text>
            <Text size="sm" color="dimmed" inline mt={7}>
              {t("newTask.filesTab.text")}
            </Text>
          </div>
        </Group>
      </Dropzone>

      {(images.length > 0 || files.length > 0) && (
        <Paper
          sx={(theme) => ({
            padding: 12,
            background:
              theme.colorScheme === "dark"
                ? theme.colors.dark[8]
                : theme.colors.gray[2],
            boxShadow: `0 -1px 3px rgba(0, 0, 0, 0.05), rgba(0, 0, 0, 0.05) 0px -20px 25px -5px, rgba(0, 0, 0, 0.04) 0px -10px 10px -5px`,
            zIndex: 10,
          })}
        >
          <ScrollArea
            offsetScrollbars
            scrollbarSize={5}
            styles={{
              viewport: {
                maxHeight: 170,
              },
            }}
          >
            {images.length > 0 && (
              <Text size="xs" weight={600} color="dimmed" mb={8}>
                {t("images")}
              </Text>
            )}
            <SimpleGrid
              cols={
                width > 1200
                  ? 5
                  : width > 1000
                  ? 4
                  : width > 800
                  ? 3
                  : width > 600
                  ? 2
                  : 1
              }
              sx={{ position: "relative" }}
            >
              {images.map((image: File, i: number) => {
                const blob = new Blob([image], { type: "image/jpeg" });
                const blobURL = URL.createObjectURL(blob);

                return (
                  <Paper key={`image-${i}`} p={8}>
                    <Group noWrap spacing={8}>
                      <Image
                        height={36}
                        width={36}
                        src={blobURL}
                        radius={8}
                        alt="preview"
                        withPlaceholder
                      />
                      <Text
                        size="xs"
                        sx={{
                          width: "100%",
                          whiteSpace: "nowrap",
                          textOverflow: "ellipsis",
                          overflow: "hidden",
                        }}
                      >
                        {image.name}
                      </Text>
                      <CloseButton
                        size="sm"
                        onClick={() =>
                          setImages(images.filter((e, index) => index !== i))
                        }
                      />
                    </Group>
                  </Paper>
                );
              })}
            </SimpleGrid>
            <Space h="xs" />
            {files.length > 0 && (
              <Text size="xs" weight={600} color="dimmed" mb={8}>
                {t("files")}
              </Text>
            )}
            <SimpleGrid
              cols={
                width > 1200
                  ? 5
                  : width > 1000
                  ? 4
                  : width > 800
                  ? 3
                  : width > 600
                  ? 2
                  : 1
              }
              sx={{ position: "relative" }}
            >
              {files.map((image: File, i: number) => {
                return (
                  <Paper key={`image-${i}`} p={8}>
                    <Group noWrap spacing={8}>
                      <ThemeIcon variant="light" size={36}>
                        <DocumentIcon style={{ width: 20, height: 20 }} />
                      </ThemeIcon>
                      <Text
                        size="xs"
                        sx={{
                          width: "100%",
                          whiteSpace: "nowrap",
                          textOverflow: "ellipsis",
                          overflow: "hidden",
                        }}
                      >
                        {image.name}
                      </Text>
                      <CloseButton
                        size="sm"
                        onClick={() =>
                          setFiles(files.filter((e, index) => index !== i))
                        }
                      />
                    </Group>
                  </Paper>
                );
              })}
            </SimpleGrid>
          </ScrollArea>
        </Paper>
      )}
    </Stack>
  );
};

export default FilesTab;
