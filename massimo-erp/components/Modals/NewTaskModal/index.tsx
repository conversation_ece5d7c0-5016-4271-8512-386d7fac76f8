import {
  Dispatch,
  SetStateAction,
  useCallback,
  useState,
  useEffect,
} from "react";
import shallow from "zustand/shallow";

import { useStore } from "~utils/store";
import { TaskType } from "~utils/types/Task";

import {
  Modal,
  Text,
  Tabs,
  Space,
  Group,
  Button,
  SegmentedControl,
  ScrollArea,
  Divider,
  LoadingOverlay,
} from "@mantine/core";
import { useMediaQuery } from "@mantine/hooks";
import { showNotification } from "@mantine/notifications";

import DetailsTab from "./DetailsTab";
import DescriptionTab from "./DescriptionTab";
import FilesTab from "./FilesTab";
import StepsTab from "./StepsTab";
import RecurrenceTab from "./RecurrenceTab";
import OtherTab from "./OtherTab";
import { isEqual, isNumber, isString, omit } from "lodash";
import { openConfirmModal } from "@mantine/modals";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { MessageType } from "~utils/types/Chat";
import SelectUserModal from "../SelectUserModal";
import { useForm } from "@mantine/form";

const NewTaskModalContent: CC<{
  activeTab: number;
  setActiveTab: Dispatch<SetStateAction<number>>;
  saveTask: (values: Partial<TaskType>) => void;
  subtaskMessages: any;
  setSubtaskMessages: any;
  files: File[];
  setFiles: Dispatch<SetStateAction<File[]>>;
  images: File[];
  setImages: Dispatch<SetStateAction<File[]>>;
}> = function ({
  saveTask,
  activeTab,
  setActiveTab,
  subtaskMessages,
  setSubtaskMessages,
  files,
  setFiles,
  images,
  setImages,
}) {
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );
  const { projects } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const {
    isLoading,
    newTaskModalData,
    setNewTaskModalData,
    setIsOpenSelectUserModal,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const form = useForm<Partial<TaskType>>({
    initialValues: {
      priority: "",
      projectId: undefined,
      title: "",
    },

    validate: {
      priority: (value) => (value !== "" ? null : t("newTask.errors.priority")),
      projectId: (value) =>
        isString(value) ? null : t("newTask.errors.projectId"),
      title: (value) => (value !== "" ? null : t("newTask.errors.title")),
    },
  });

  const tabs = [
    "Details",
    "Description",
    "Files",
    "Steps",
    "Recurrence",
    "Other",
  ];

  const addSubtasksMessage = useCallback(() => {
    const newMessage: Omit<MessageType, "id" | "createdAt"> = {
      type: "text",
      ownerId: activeUserId,
      chatId: "$chats.-1.id",
      content: {
        type: "subtask",
        subtasks: [],
        contributors: [],
        start: new Date(),
        end: new Date(),
      },
    };

    try {
      setSubtaskMessages([...subtaskMessages, newMessage]);
    } catch {}
  }, [activeUserId, setSubtaskMessages, subtaskMessages]);

  const activeProject = projects.find(
    (e) => e.id === newTaskModalData.projectId
  );

  return (
    <>
      <SelectUserModal
        userList={
          !!activeProject
            ? users
                .filter(
                  (e) =>
                    !(
                      activeProject?.departmentIds?.some((d) =>
                        e.departmentIds.includes(d)
                      ) || activeProject?.allowedUserIds.includes(e.id)
                    ) && !activeProject.restrictedUserIds.includes(e.id)
                )
                .map((e) => e.id)
            : users.map((e) => e.id)
        }
        onesList={newTaskModalData.assigneeIds}
        handleSelect={(id) =>
          setNewTaskModalData({
            assigneeIds: [...newTaskModalData.assigneeIds, id],
          })
        }
        handleRemove={(id) =>
          setNewTaskModalData({
            assigneeIds: newTaskModalData.assigneeIds.filter((e) => e !== id),
          })
        }
      />
      <form
        onSubmit={form.onSubmit(
          (values) => saveTask(values),
          (validationErrors, _values, _event) => {
            setActiveTab(0);
          }
        )}
      >
        <Tabs variant="default" value={tabs[activeTab]}>
          <ScrollArea offsetScrollbars scrollbarSize={5}>
            <Group position="center">
              <SegmentedControl
                color="blue"
                value={tabs[activeTab]}
                onChange={(value: string) => setActiveTab(tabs.indexOf(value))}
                data={tabs.map((e) => ({
                  value: e,
                  label: t(`newTask.tabs.${e}`),
                }))}
              />
            </Group>
          </ScrollArea>

          <Space h="md" />

          <Tabs.Panel value="Details">
            <DetailsTab form={form} />
          </Tabs.Panel>

          <Tabs.Panel value="Description">
            <DescriptionTab />
          </Tabs.Panel>

          <Tabs.Panel value="Files">
            <FilesTab
              files={files}
              setFiles={setFiles}
              images={images}
              setImages={setImages}
            />
          </Tabs.Panel>

          <Tabs.Panel value="Steps">
            <StepsTab
              subtaskMessages={subtaskMessages}
              setSubtaskMessages={setSubtaskMessages}
            />
          </Tabs.Panel>

          <Tabs.Panel value="Recurrence">
            <RecurrenceTab />
          </Tabs.Panel>

          <Tabs.Panel value="Other">
            <OtherTab />
          </Tabs.Panel>
        </Tabs>

        <Divider my="md" />

        <Group position="apart">
          <Group>
            <Button onClick={() => setIsOpenSelectUserModal(true)}>
              {t("taskAssignees")}
            </Button>

            {activeTab === tabs.indexOf("Steps") && (
              <Button onClick={() => addSubtasksMessage()}>
                {t("newTask.newSubtasks")}
              </Button>
            )}
          </Group>
          <Group>
            {activeTab > 0 && (
              <Button
                variant="light"
                color="gray"
                onClick={() => setActiveTab((value: number) => value - 1)}
              >
                {t("back")}
              </Button>
            )}
            {activeTab === tabs.length - 1 ? (
              <Button
                color="blue"
                type="submit"
                loading={isLoading}
                loaderProps={{ variant: "bars" }}
              >
                {t("save")}
              </Button>
            ) : (
              <Button
                color="blue"
                onClick={() => setActiveTab((value: number) => value + 1)}
              >
                {t("next")}
              </Button>
            )}
          </Group>
        </Group>
      </form>
    </>
  );
};

const NewTaskModal = function () {
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    isOpenNewTaskModal,
    setIsOpenNewTaskModal,
    newTaskModalData,
    setNewTaskModalData,
    blankTask,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      isOpenNewTaskModal: state.isOpenNewTaskModal!,
      setIsOpenNewTaskModal: state.setIsOpenNewTaskModal!,
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
      blankTask: state.computed!.blankTask!,
    }),
    shallow
  );
  const { actionUpdateMultipleTask } = useStore(
    "tasks",
    (state) => ({
      actionUpdateMultipleTask: state.actionUpdateMultipleTask!,
    }),
    shallow
  );
  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );
  const { actionUploadFiles } = useStore(
    "files",
    (state) => ({
      actionUploadFiles: state.actionUploadFiles!,
    }),
    shallow
  );
  const { actionSendNotifications } = useStore(
    "notifications",
    (state) => ({
      actionSendNotifications: state.actionSendNotifications!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const matches = useMediaQuery("(max-width: 992px)");
  const [activeTab, setActiveTab] = useState<number>(0);

  const [subtaskMessages, setSubtaskMessages] = useState<
    Omit<MessageType, "id" | "createdAt">[]
  >([]);

  const [files, setFiles] = useState<File[]>([]);
  const [images, setImages] = useState<File[]>([]);

  const closeNewTaskModal = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    setIsOpenNewTaskModal(false);

    setTimeout(() => {
      setNewTaskModalData(blankTask);
      setSubtaskMessages([]);
      setActiveTab(0);
    }, 300);
  }, [
    blankTask,
    isLoading,
    loadingLevel,
    setIsOpenNewTaskModal,
    setNewTaskModalData,
  ]);

  const saveTask = useCallback(
    async (values: Partial<TaskType>) => {
      setIsLoading(true);
      setLoadingLevel(1);

      const formValues = values;

      formValues.projectId = +(formValues.projectId || 0);

      try {
        const newMessages: Partial<MessageType>[] = [];

        if (images.length > 0) {
          const imageIds = await actionUploadFiles(
            images.map((imageFile) => ({
              userId: activeUserId,
              file: imageFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            imageIds.map((imageId) => {
              // return actionSendMessage({
              //   type: "image",
              //   ownerId: activeUserId,
              //   chatId: "$chats.-1.id",
              //   content: {
              //     fileId: imageId,
              //   },
              // });
              newMessages.push({
                type: "image",
                ownerId: activeUserId,
                chatId: "$chats.-1.id",
                content: {
                  fileId: imageId,
                },
              });
            })
          );
        }

        if (files.length > 0) {
          const fileIds = await actionUploadFiles(
            files.map((fileFile) => ({
              userId: activeUserId,
              file: fileFile,
              type: "attachment",
            })),
            "attachment",
            activeUserId
          );

          await Promise.all(
            fileIds.map((fileId) => {
              // return actionSendMessage({
              //   type: "file",
              //   ownerId: activeUserId,
              //   chatId: "$chats.-1.id",
              //   content: {
              //     fileId: fileId,
              //   },
              // });
              newMessages.push({
                type: "file",
                ownerId: activeUserId,
                chatId: "$chats.-1.id",
                content: {
                  fileId: fileId,
                },
              });
            })
          );
        }

        const newTask = await actionUpdateMultipleTask(
          [
            {
              ...newTaskModalData,
              ...formValues,
              contributorIds: [activeUserId],
            },
          ],
          {
            chats: [
              {
                type: "task",
                ownerId: activeUserId,
                taskId: "$.-1.id",
              },
            ],
            messages: [...subtaskMessages, ...newMessages],
          }
        );

        Promise.all(
          newTask[0].assigneeIds.map(async (userId) => {
            await actionSendNotifications([
              {
                target: "tasks",
                value: `${newTask[0].id}`,
                user: userId,
              },
            ]);
          })
        );

        showNotification({
          color: "green",
          title: t("successful.title"),
          message: t("successful.taskCreate"),
          autoClose: 3000,
        });
      } finally {
        closeNewTaskModal();

        setIsLoading(false);
      }
    },
    [
      newTaskModalData,
      setIsLoading,
      setLoadingLevel,
      images,
      files,
      actionUpdateMultipleTask,
      activeUserId,
      subtaskMessages,
      actionSendNotifications,
      t,
      actionUploadFiles,
      closeNewTaskModal,
    ]
  );

  const confirmModal = () =>
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeNewTaskModal(),
      style: {
        zIndex: 10000,
      },
    });

  return (
    <Modal
      title={t("newTask.title")}
      opened={isOpenNewTaskModal}
      onClose={() => {
        if (isLoading && loadingLevel === 1) {
          return;
        }

        if (
          isEqual(
            omit(newTaskModalData, ["start", "end", "tags", "contents"]),
            omit(blankTask, ["start", "end", "tags", "contents"])
          ) &&
          subtaskMessages.length === 0
        ) {
          closeNewTaskModal();
        } else {
          confirmModal();
        }
      }}
      centered
      transition={isOpenNewTaskModal ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
      size={matches ? "calc(100vw - 36px)" : 750}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <NewTaskModalContent
        saveTask={saveTask}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        subtaskMessages={subtaskMessages}
        setSubtaskMessages={setSubtaskMessages}
        files={files}
        setFiles={setFiles}
        images={images}
        setImages={setImages}
      />
    </Modal>
  );
};

export default NewTaskModal;
