import { Stack, Checkbox, Alert } from "@mantine/core";
import { InformationCircleIcon } from "@heroicons/react/24/outline";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useTranslation } from "next-i18next";

const OtherTab = function () {
  const { isLoading, newTaskModalData, setNewTaskModalData } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
    }),
    shallow
  );

  const { t } = useTranslation();

  return (
    <Stack>
      <Checkbox
        label={t("newTask.onlyAssignees")}
        checked={newTaskModalData.private}
        disabled={isLoading}
        onChange={() =>
          setNewTaskModalData({
            private: !newTaskModalData.private,
          })
        }
      />
      <Alert icon={<InformationCircleIcon style={{ width: 20, height: 20 }} />}>
        {t("newTask.onlyAssigneesMessage")}
      </Alert>
      <Checkbox
        label={t("newTask.customerSee")}
        checked={newTaskModalData.customerPrivate}
        disabled={isLoading}
        onChange={() =>
          setNewTaskModalData({
            customerPrivate: !newTaskModalData.customerPrivate,
          })
        }
      />
      <Alert icon={<InformationCircleIcon style={{ width: 20, height: 20 }} />}>
        {t("newTask.customerSeeMessage")}
      </Alert>
    </Stack>
  );
};

export default OtherTab;
