import { ChangeEvent, useEffect, useState } from "react";

import {
  Stack,
  Text,
  Select,
  Input,
  Divider,
  MultiSelect,
  SimpleGrid,
  Switch,
} from "@mantine/core";
import { useStore } from "~utils/store";
import shallow from "zustand/shallow";
import { useTranslation } from "next-i18next";
import { useMediaQuery, useSetState } from "@mantine/hooks";

const EVERY = ["Year", "Month", "Week", "Day", "Hour", "Minute"];

const MONTHS = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const WEEKDAYS = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

const RecurrenceTab = function () {
  const { t } = useTranslation("common");

  const { newTaskModalData, setNewTaskModalData } = useStore(
    "temp",
    (state) => ({
      newTaskModalData: state.newTaskModalData!,
      setNewTaskModalData: state.setNewTaskModalData!,
    }),
    shallow
  );

  const matches = useMediaQuery("(max-width: 500px)");
  const [isRecurrence, setIsRecurrence] = useState(false);

  const [cron, setCron] = useState("");
  const [state, setState] = useSetState({
    every: "Year", // year, month week day hour minute
    everyIndex: ["Minute", "Hour", "Day", "Week", "Month", "Year"],
    months: ["Every"], // 0-11
    days: ["Every"], // 0-30
    weekDays: ["Every"], // monday, tuesday ...
    hours: ["Every"], // 0-23
    minutes: ["Every"], // 0-59
  });

  useEffect(() => {
    let minutes = "0";
    let hours = "*";
    let days = "*";
    let months = "*";
    let weekDays = "*";

    if (!state.hours.includes("Every")) {
      hours = state.hours.join(",");
    }

    if (!state.days.includes("Every")) {
      days = state.days.join(",");
    }

    if (!state.months.includes("Every")) {
      months = state.months.map((x) => MONTHS.indexOf(x) + 1).join(",");
    }

    if (!state.weekDays.includes("Every")) {
      weekDays = state.weekDays.map((x) => WEEKDAYS.indexOf(x) + 1).join(",");
    }

    setCron(
      `${minutes || "0"} ${hours || "*"} ${days || "*"} ${months || "*"} ${
        weekDays || "*"
      }`
    );
  }, [state]);

  return (
    // TODO: Input labels fixes
    <Stack>
      <Switch
        checked={isRecurrence}
        onChange={(event) => setIsRecurrence(event.currentTarget.checked)}
        label={t("newTask.recurrenceTab.switchLabel")}
      />
      {isRecurrence && (
        <>
          <SimpleGrid cols={matches ? 1 : 2}>
            <Stack spacing={4}>
              <Text size="sm" weight={600} color="dimmed">
                {t("newTask.recurrenceTab.Every")}
              </Text>
              <Select
                data={EVERY.map((e) => ({
                  label: t(`newTask.recurrenceTab.${e}`),
                  value: e,
                }))}
                value={state.every}
                searchable
                onChange={(value) => {
                  setState({ every: value || "Year" });
                }}
                sx={{ width: "100%" }}
              />
            </Stack>

            {state.everyIndex.indexOf(state.every) > 4 && (
              <Stack spacing={4}>
                <Text size="sm" weight={600} color="dimmed">
                  {t("newTask.recurrenceTab.in")}
                </Text>
                <MultiSelect
                  size="sm"
                  data={[
                    {
                      label: t("newTask.recurrenceTab.every"),
                      value: "Every",
                    },
                    ...(state.months.includes("Every")
                      ? []
                      : MONTHS.map((e) => ({
                          label: t(`newTask.recurrenceTab.${e}`),
                          value: e,
                        }))),
                  ]}
                  value={state.months}
                  searchable
                  onChange={(value) => {
                    if (!value.includes("Every")) {
                      setState({ months: value || "" });
                    } else {
                      setState({ months: ["Every"] });
                    }
                  }}
                  sx={{ width: "100%" }}
                />
              </Stack>
            )}

            {state.everyIndex.indexOf(state.every) > 3 && (
              <Stack spacing={4}>
                <Text size="sm" weight={600} color="dimmed">
                  {t("newTask.recurrenceTab.on")}
                </Text>
                <MultiSelect
                  size="sm"
                  data={[
                    {
                      label: t("newTask.recurrenceTab.every"),
                      value: "Every",
                    },
                    ...(state.days.includes("Every")
                      ? []
                      : Array.from<number>({
                          length: 31,
                        })
                          .fill(1)
                          .map((v, i) => `${v + i}`)),
                  ]}
                  value={state.days}
                  searchable
                  onChange={(value) => {
                    if (!value.includes("Every")) {
                      setState({ days: value || "" });
                    } else {
                      setState({ days: ["Every"] });
                    }
                  }}
                  sx={{ width: "100%" }}
                />
              </Stack>
            )}

            {state.everyIndex.indexOf(state.every) > 2 && (
              <Stack spacing={4}>
                <Text size="sm" weight={600} color="dimmed">
                  {t("newTask.recurrenceTab.and")}
                </Text>
                <MultiSelect
                  size="sm"
                  searchable
                  data={[
                    {
                      label: t("newTask.recurrenceTab.every"),
                      value: "Every",
                    },
                    ...(state.weekDays.includes("Every")
                      ? []
                      : WEEKDAYS.map((e) => ({
                          label: t(`newTask.recurrenceTab.${e}`),
                          value: e,
                        }))),
                  ]}
                  value={state.weekDays}
                  onChange={(value) => {
                    if (!value.includes("Every")) {
                      setState({ weekDays: value || "" });
                    } else {
                      setState({ weekDays: ["Every"] });
                    }
                  }}
                  sx={{ width: "100%" }}
                />
              </Stack>
            )}

            {state.everyIndex.indexOf(state.every) > 1 && (
              <Stack spacing={4}>
                <Text size="sm" weight={600} color="dimmed">
                  {t("newTask.recurrenceTab.hours")}
                </Text>
                <MultiSelect
                  size="sm"
                  data={[
                    {
                      label: t("newTask.recurrenceTab.every"),
                      value: "Every",
                    },
                    ...(state.hours.includes("Every")
                      ? []
                      : Array.from<number>({
                          length: 24,
                        })
                          .fill(1)
                          .map((v, i) => `${v + i}`)),
                  ]}
                  value={state.hours}
                  searchable
                  onChange={(value) => {
                    if (!value.includes("Every")) {
                      setState({ hours: value || "" });
                    } else {
                      setState({ hours: ["Every"] });
                    }
                  }}
                  sx={{ width: "100%" }}
                />
              </Stack>
            )}
          </SimpleGrid>
          <Divider
            label={t("newTask.recurrenceTab.or")}
            labelPosition="center"
          />
          <Input
            value={cron}
            onChange={(e: ChangeEvent<HTMLInputElement>) =>
              setCron(e.target.value)
            }
          />
        </>
      )}
    </Stack>
  );
};

export default RecurrenceTab;
