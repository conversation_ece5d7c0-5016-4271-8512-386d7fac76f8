import { useCallback, useState } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Stack,
  Group,
  Button,
  Text,
  Divider,
  Avatar,
  CloseButton,
  Anchor,
  Badge,
  Box,
  Paper,
  ActionIcon,
  SimpleGrid,
  Menu,
  LoadingOverlay,
  useMantineTheme,
} from "@mantine/core";
import {
  formatDate,
  formatTime,
  hexToRGBA,
  numberFormatter,
} from "~utils/tools";
import { OfferType, ProjectType } from "~utils/types/Project";
import {
  PlusIcon,
  PlusSmallIcon,
  XMarkIcon,
} from "@heroicons/react/24/outline";
import { useElementSize } from "@mantine/hooks";
import { UserType } from "~utils/types/User";
import SelectUserModal from "../SelectUserModal";
import { flattenDepth, isEqual, omit, pick, uniq } from "lodash";
import { useEffect } from "react";
import { openConfirmModal } from "@mantine/modals";
import { useTranslation } from "next-i18next";
import { CC } from "~types";
import { CustomerType } from "~utils/types/Customer";
import Users from "~pages/admin/users";
import ActiveAvatar from "~components/ActiveAvatar";
import { useRouter } from "next/router";

const RequestViewModalContent: CC<{
  allowedUserIds: number[];
  restrictedUserIds: number[];
  departmentIds: number[];
  setDepartmentIds: (value: number[]) => void;
  removeUser: (type: "allowed" | "restricted", id: number) => void;
  setUserModalType: any;
  closeModal: () => void;
  handleClose: () => void;
  updateRequests: () => Promise<unknown>;
}> = function ({
  allowedUserIds,
  restrictedUserIds,
  departmentIds,
  setDepartmentIds,
  removeUser,
  setUserModalType,
  closeModal,
  handleClose,
  updateRequests,
}) {
  const {
    setIsLoading,
    setLoadingLevel,
    requestViewModal,
    setRequestViewModal,
    setIsOpenSelectUserModal,
    blankOffer,
  } = useStore(
    "temp",
    (state) => ({
      setIsLoading: state.setIsLoading!,
      setLoadingLevel: state.setLoadingLevel!,
      requestViewModal: state.requestViewModal!,
      setRequestViewModal: state.setRequestViewModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
      blankOffer: state.computed!.blankOffer!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );
  const { customers } = useStore(
    "customers",
    (state) => ({
      customers: state.customers!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );
  const { projects, addProject } = useStore(
    "projects",
    (state) => ({
      projects: state.projects!,
      addProject: state.addProject!,
    }),
    shallow
  );
  const { actionUpdateMultipleRequests } = useStore(
    "requests",
    (state) => ({
      actionUpdateMultipleRequests: state.actionUpdateMultipleRequests!,
    }),
    shallow
  );

  const { push } = useRouter();
  const { t } = useTranslation();
  const theme = useMantineTheme();
  const { ref, width } = useElementSize();

  const activeCustomer = customers.find(
    (e) => e.id === requestViewModal.data.customerId
  ) as CustomerType | undefined;

  const openSelectUserModal = useCallback(
    (type: "allowed" | "restricted") => {
      setUserModalType(type);
      setIsOpenSelectUserModal(true);
    },
    [setIsOpenSelectUserModal, setUserModalType]
  );

  const { activeUserId } = useStore(
    "global",
    (state) => ({
      activeUserId: state.activeUserId!,
    }),
    shallow
  );

  const rejectRequest = useCallback(() => {}, []);
  const acceptRequest = useCallback(async () => {
    setIsLoading(true);
    setLoadingLevel(1);

    try {
      const newProject: Omit<ProjectType, "id"> = {
        offerIds: ["$offers.-1.id"] as any,
        requestId: requestViewModal.data.id,
        roleIds: [],
        departmentIds,
        allowedUserIds,
        restrictedUserIds,
        priority: "Medium",
      };

      const newOffer: Omit<OfferType, "id"> = {
        ...(pick(requestViewModal.data, [
          "name",
          "description",
          "budget",
          "start",
          "end",
        ]) as any),
        status: "Offer",
      };

      await actionUpdateMultipleRequests(
        [
          {
            ...requestViewModal.data,
            approverId: activeUserId,
            projectId: "$projects.-1.id" as any,
          },
        ],
        {
          projects: [newProject],
          offers: [newOffer],
          chats: [
            {
              type: "offer",
              ownerId: activeUserId,
              offerId: "$offers.-1.id",
            },
          ],
        }
      );
    } finally {
      closeModal();
      setIsLoading(false);

      await updateRequests();
    }
  }, [
    setIsLoading,
    setLoadingLevel,
    requestViewModal.data,
    departmentIds,
    allowedUserIds,
    restrictedUserIds,
    actionUpdateMultipleRequests,
    activeUserId,
    updateRequests,
    closeModal,
  ]);

  const activeUser = users.find((user) => user.id === activeUserId);

  return (
    <>
      <Group position="apart" mb="md" ref={ref}>
        <Group spacing="sm">
          <Avatar src={activeCustomer?.avatar} radius="xl" />
          <Anchor
            size="sm"
            weight={600}
            sx={{
              color:
                theme.colorScheme === "dark"
                  ? theme.colors.gray[4]
                  : theme.colors.dark[4],
            }}
          >
            {activeCustomer?.fullName}
          </Anchor>
        </Group>
        <CloseButton onClick={() => handleClose()} />
      </Group>
      <Stack>
        <Stack spacing={8}>
          <Text size="lg" weight={600}>
            {requestViewModal.data.name}
          </Text>
          {requestViewModal.data.description !== "" && (
            <Text size="md" color="dimmed">
              {requestViewModal.data.description}
            </Text>
          )}
          <Group spacing="lg">
            <Group spacing={8}>
              <Text size="xs" weight={600} color="dimmed">
                {t("budget")}
              </Text>
              <Text size="lg" weight={600}>
                {`₺ ${requestViewModal.data.budget}`.replace(
                  /\B(?=(\d{3})+(?!\d))/g,
                  ","
                )}
              </Text>
            </Group>
            <Group spacing={8}>
              <Text size="xs" weight={600} color="dimmed">
                {t("offersModal.dateRange")}
              </Text>
              <Text size="sm" weight={600}>
                {formatDate(new Date(requestViewModal.data.start))},{" "}
                {formatTime(new Date(requestViewModal.data.start))} -{" "}
                {formatDate(new Date(requestViewModal.data.end))},{" "}
                {formatTime(new Date(requestViewModal.data.end))}
              </Text>
            </Group>
          </Group>
          <Group spacing="sm">
            <Text size="xs" weight={600} color="dimmed">
              {t("Departments")}
            </Text>
            {departmentIds?.map((id: number, i: number) => {
              const activeDepartment = departments.filter(
                (e) => e.id === id
              )[0];
              return (
                <Badge
                  key={"department-" + i}
                  styles={{
                    root: {
                      background: hexToRGBA(activeDepartment.color, 0.1),
                      color: activeDepartment.color,
                      textTransform: "capitalize",
                    },
                  }}
                  rightSection={
                    <ActionIcon
                      size="xs"
                      radius="xl"
                      variant="transparent"
                      onClick={() =>
                        setDepartmentIds(departmentIds.filter((e) => e !== id))
                      }
                    >
                      <XMarkIcon style={{ width: 10, height: 10 }} />
                    </ActionIcon>
                  }
                  pr={4}
                >
                  {activeDepartment.label}
                </Badge>
              );
            })}
            {departmentIds?.length !== departments.length && (
              <Menu
                shadow="md"
                width={200}
                offset={12}
                position="bottom-end"
                transition="fade"
              >
                <Menu.Target>
                  <ActionIcon size="xs">
                    <PlusSmallIcon style={{ width: 16, height: 16 }} />
                  </ActionIcon>
                </Menu.Target>
                <Menu.Dropdown>
                  {departments
                    .filter((e) =>
                      !departmentIds?.includes(e.id) && activeUser
                        ? activeUser.departmentIds?.includes(e.id)
                        : false
                    )
                    .map((department, i) => (
                      <Menu.Item
                        key={"department-" + i}
                        onClick={() =>
                          setDepartmentIds([
                            ...(departmentIds || []),
                            department.id,
                          ])
                        }
                      >
                        {department.label}
                      </Menu.Item>
                    ))}
                </Menu.Dropdown>
              </Menu>
            )}
          </Group>
        </Stack>
        <Divider
          color="green"
          label={t("offersModal.allowedUsers")}
          labelPosition="center"
        />
        <SimpleGrid cols={width < 600 && width > 0 ? 1 : 3}>
          {users
            .filter((e) => allowedUserIds.includes(e.id))
            .map((user: UserType, i: number) => (
              <Paper
                key={user.id}
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[1],
                  padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                })}
              >
                <Group noWrap align="center" sx={{ height: "100%" }}>
                  <ActiveAvatar userId={user.id} radius="xl" />
                  <Group
                    spacing={8}
                    sx={{ width: "100%", height: "100%" }}
                    position="apart"
                  >
                    <Text size="sm" weight={600}>
                      {user.name} {user.surname}
                    </Text>
                    <ActionIcon
                      size="sm"
                      onClick={() => removeUser("allowed", user.id)}
                    >
                      <XMarkIcon style={{ width: 16, height: 16 }} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Paper>
            ))}
          {allowedUserIds.length !== users.length && (
            <Box
              sx={(theme) => ({
                minHeight: 58,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: theme.radius.md,
                border: `2px dashed ${
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[6]
                    : theme.colors.gray[2]
                }`,
                cursor: "pointer",
                transition: "filter .2s ease",
                ":hover": {
                  filter: "brightness(.9)",
                },
              })}
              onClick={() => openSelectUserModal("allowed")}
            >
              <PlusIcon style={{ width: 24, height: 24 }} />
            </Box>
          )}
        </SimpleGrid>
        <Divider
          color="red"
          label={t("offersModal.restrictedUsers")}
          labelPosition="center"
        />
        <SimpleGrid cols={width < 600 && width > 0 ? 1 : 3}>
          {users
            .filter((e) => restrictedUserIds.includes(e.id))
            .map((user: UserType, i: number) => (
              <Paper
                key={user.id}
                sx={(theme) => ({
                  background:
                    theme.colorScheme === "dark"
                      ? theme.colors.dark[6]
                      : theme.colors.gray[1],
                  padding: `${theme.spacing.xs}px ${theme.spacing.sm}px`,
                })}
              >
                <Group noWrap align="center" sx={{ height: "100%" }}>
                  <ActiveAvatar userId={user.id} radius="xl" />
                  <Group
                    spacing={8}
                    sx={{ width: "100%", height: "100%" }}
                    position="apart"
                  >
                    <Text size="sm" weight={600}>
                      {user.name} {user.surname}
                    </Text>
                    <ActionIcon
                      size="sm"
                      onClick={() => removeUser("restricted", user.id)}
                    >
                      <XMarkIcon style={{ width: 16, height: 16 }} />
                    </ActionIcon>
                  </Group>
                </Group>
              </Paper>
            ))}
          {restrictedUserIds.length !== users.length && (
            <Box
              sx={(theme) => ({
                minHeight: 58,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                borderRadius: theme.radius.md,
                border: `2px dashed ${
                  theme.colorScheme === "dark"
                    ? theme.colors.dark[6]
                    : theme.colors.gray[2]
                }`,
                cursor: "pointer",
                transition: "filter .2s ease",
                ":hover": {
                  filter: "brightness(.9)",
                },
              })}
              onClick={() => openSelectUserModal("restricted")}
            >
              <PlusIcon style={{ width: 24, height: 24 }} />
            </Box>
          )}
        </SimpleGrid>
        <Divider />
        <Group position={width < 470 && width > 0 ? "center" : "apart"}>
          <Button
            color="blue"
            onClick={() => {
              setRequestViewModal({
                isOpen: false,
              });
              push("/apps/chat");
            }}
          >
            {t("contactWithCustomer")}
          </Button>
          <Group>
            <Button color="red" onClick={() => rejectRequest()}>
              {t("reject")}
            </Button>
            <Button color="green" onClick={() => acceptRequest()}>
              {t("approve")}
            </Button>
          </Group>
        </Group>
      </Stack>
    </>
  );
};

const RequestViewModal: CC<{ updateRequests: () => Promise<unknown> }> = ({
  updateRequests,
}) => {
  const {
    isLoading,
    loadingLevel,
    requestViewModal,
    setRequestViewModal,
    blankRequest,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      requestViewModal: state.requestViewModal!,
      setRequestViewModal: state.setRequestViewModal!,
      blankRequest: state.computed!.blankRequest!,
    }),
    shallow
  );
  const { departments } = useStore(
    "departments",
    (state) => ({
      departments: state.departments!,
    }),
    shallow
  );
  const { offers } = useStore(
    "offers",
    (state) => ({
      offers: state.offers!,
    }),
    shallow
  );
  const { requests } = useStore(
    "requests",
    (state) => ({
      requests: state.requests!,
    }),
    shallow
  );
  const { users } = useStore(
    "users",
    (state) => ({
      users: state.users!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const [allowedUserIds, setAllowedUserIds] = useState<number[]>([]);
  const [restrictedUserIds, setRestrictedUserIds] = useState<number[]>([]);
  const [departmentIds, setDepartmentIds] = useState<number[]>([]);

  const [userModalType, setUserModalType] = useState<"allowed" | "restricted">(
    "allowed"
  );

  /*useEffect(() => {
    const data = requestViewModal.data;
    setRequestViewModal({
      data: {
        ...data,
        restrictedUserIds: data.restrictedUserIds.filter((e) =>
          uniq(
            flattenDepth(
              departments
                .filter((e) =>
                  requestViewModal.data.departmentIds.includes(e.id)
                )
                .map((e) => e.userIds),
              2
            )
          ).includes(e)
        ),
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [requestViewModal.data.departmentIds]);*/

  const addUser = useCallback(
    (type: "allowed" | "restricted", id: number) => {
      if (type === "allowed") {
        setRestrictedUserIds(restrictedUserIds.filter((e) => e !== id));
        setAllowedUserIds([...allowedUserIds, id]);
      } else if (type === "restricted") {
        setAllowedUserIds(allowedUserIds.filter((e) => e !== id));
        setRestrictedUserIds([...restrictedUserIds, id]);
      }
    },
    [allowedUserIds, restrictedUserIds]
  );

  const removeUser = useCallback(
    (type: "allowed" | "restricted", id: number) => {
      if (type === "allowed") {
        setAllowedUserIds(allowedUserIds.filter((e) => e !== id));
      } else if (type === "restricted") {
        setRestrictedUserIds(restrictedUserIds.filter((e) => e !== id));
      }
    },
    [allowedUserIds, restrictedUserIds]
  );

  const closeModal = useCallback(() => {
    setRequestViewModal({
      isOpen: false,
      data: blankRequest,
    });
    setAllowedUserIds([]);
    setRestrictedUserIds([]);
    setDepartmentIds([]);
  }, [setRequestViewModal, blankRequest]);

  const confirmModal = useCallback(() => {
    openConfirmModal({
      title: t("confirmModal.title"),
      children: <Text size="sm">{t("confirmModal.message")}</Text>,
      labels: { confirm: t("yes"), cancel: t("no") },
      onCancel: () => {},
      onConfirm: () => closeModal(),
      style: {
        zIndex: 10000,
      },
    });
  }, [closeModal, t]);

  const handleClose = useCallback(() => {
    if (isLoading && loadingLevel === 1) {
      return;
    }

    if (
      !isEqual(
        requestViewModal.data,
        requests.filter((e) => e.id === requestViewModal.data.id)[0]
      ) &&
      allowedUserIds.length > 0 &&
      restrictedUserIds.length > 0 &&
      departmentIds.length > 0
    ) {
      confirmModal();
    } else {
      closeModal();
    }
  }, [
    isLoading,
    loadingLevel,
    requestViewModal.data,
    requests,
    allowedUserIds.length,
    restrictedUserIds.length,
    departmentIds.length,
    confirmModal,
    closeModal,
  ]);

  return (
    <Modal
      opened={requestViewModal.isOpen}
      onClose={() => handleClose()}
      centered
      transition={requestViewModal.isOpen ? "slide-down" : "slide-up"}
      withCloseButton={false}
      size={800}
      zIndex={1000}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 1}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <SelectUserModal
        userList={
          userModalType === "restricted"
            ? uniq(
                flattenDepth(
                  departments
                    .filter((e) => departmentIds.includes(e.id))
                    .map((e) => e.userIds),
                  2
                )
              )
            : departmentIds.length === 0
            ? undefined
            : users
                .filter(
                  (user: UserType) =>
                    !user.departmentIds?.some((id: number) =>
                      departmentIds.includes(id)
                    )
                )
                .map((e) => e.id)
        }
        onesList={
          userModalType === "allowed" ? allowedUserIds : restrictedUserIds
        }
        handleSelect={(id: number) => addUser(userModalType, id)}
        handleRemove={(id: number) => removeUser(userModalType, id)}
      />
      <RequestViewModalContent
        allowedUserIds={allowedUserIds}
        restrictedUserIds={restrictedUserIds}
        departmentIds={departmentIds}
        setDepartmentIds={setDepartmentIds}
        removeUser={(type, id) => removeUser(type, id)}
        setUserModalType={setUserModalType}
        closeModal={closeModal}
        handleClose={handleClose}
        updateRequests={updateRequests}
      />
    </Modal>
  );
};

export default RequestViewModal;
