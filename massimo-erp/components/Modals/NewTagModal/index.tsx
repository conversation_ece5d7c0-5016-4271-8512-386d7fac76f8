import { useCallback } from "react";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Text,
  Space,
  Group,
  Button,
  Stack,
  Input,
  ColorSwatch,
  LoadingOverlay,
  useMantineTheme,
} from "@mantine/core";
import { CheckCircleIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";

const NewTagModal = function () {
  const {
    isLoading,
    setIsLoading,
    loadingLevel,
    setLoadingLevel,
    isOpenNewTagModal,
    setIsOpenNewTagModal,
    newTag,
    setNewTag,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      setIsLoading: state.setIsLoading!,
      loadingLevel: state.loadingLevel!,
      setLoadingLevel: state.setLoadingLevel!,
      isOpenNewTagModal: state.isOpenNewTagModal!,
      setIsOpenNewTagModal: state.setIsOpenNewTagModal!,
      newTag: state.newTag!,
      setNewTag: state.setNewTag!,
    }),
    shallow
  );

  const { addTag } = useStore(
    "data",
    (state) => ({
      addTag: state.addTag!,
    }),
    shallow
  );

  const { t } = useTranslation();

  const closeNewTagModal = useCallback(() => {
    if (isLoading) {
      return;
    }

    setIsOpenNewTagModal(false);
    setNewTag({
      value: "",
      label: "",
      color: "",
    });
  }, [isLoading, setIsOpenNewTagModal, setNewTag]);

  const createNewTag = useCallback(() => {
    if (newTag.label === "" || newTag.color === "") {
      return;
    }

    setIsLoading(true);
    setLoadingLevel(2);

    setTimeout(() => {
      addTag(newTag);
      closeNewTagModal();
      setIsLoading(false);
    }, 1000);
  }, [newTag, setIsLoading, setLoadingLevel, addTag, closeNewTagModal]);

  const theme = useMantineTheme();

  const swatches = Object.keys(theme.colors).map((color) => (
    <ColorSwatch
      key={color}
      color={theme.colors[color][6]}
      onClick={() => setNewTag({ color: color })}
      sx={{
        width: 24,
        height: 24,
        cursor: "pointer",
      }}
    >
      {color === newTag.color && (
        <CheckCircleIcon style={{ width: 20, height: 20, color: "white" }} />
      )}
    </ColorSwatch>
  ));

  return (
    <Modal
      title={t("newTagModal.title")}
      opened={isOpenNewTagModal}
      onClose={() => closeNewTagModal()}
      centered
      transition={isOpenNewTagModal ? "slide-down" : "slide-up"}
      styles={{
        inner: {
          overflow: "hidden",
        },
      }}
      size={510}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 2}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md
        })}
      />
      <Stack>
        <Input
          placeholder={t("newTagModal.label")}
          disabled={isLoading}
          value={newTag.label}
          onChange={(event: any) => setNewTag({ label: event.target.value })}
        />
        <div>
          <Text color="dimmed" size="sm">
            {t("newTagModal.color")}
          </Text>
          <Space h={8} />
          <Group position="center" spacing="xs">
            {swatches}
          </Group>
        </div>
        <Group position="right">
          <Button
            variant="light"
            color="gray"
            onClick={() => closeNewTagModal()}
          >
            {t("cancel")}
          </Button>
          <Button onClick={() => createNewTag()}>{t("save")}</Button>
        </Group>
      </Stack>
    </Modal>
  );
};

export default NewTagModal;
