import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

import {
  Modal,
  Table,
  Button,
  Group,
  Avatar,
  Stack,
  Divider,
  Text,
  Select,
  Pagination,
  ScrollArea,
  Input,
  LoadingOverlay,
  Tooltip,
} from "@mantine/core";
import { useCallback, useEffect, useState } from "react";
import { UserType } from "~utils/types/User";
import { getHotkeyHandler, useElementSize } from "@mantine/hooks";
import { MagnifyingGlassIcon } from "@heroicons/react/24/outline";
import { useTranslation } from "next-i18next";
import ActiveAvatar from "~components/ActiveAvatar";
import { orderBy, uniqBy } from "lodash";

interface PropType {
  userList?: number[];
  onesList: number[];
  handleSelect: (id: number) => void;
  handleRemove: (id: number) => void;
}

export default function SelectUserModal(props: PropType) {
  const { userList, onesList, handleSelect, handleRemove } = props;

  const { users, filteredUsers, actionGetUsers } = useStore(
    "users",
    (state) => ({
      users: state.users!,
      filteredUsers: state.filteredUsers!,
      actionGetUsers: state.actionGetUsers!,
      // actionResetUsers: state.actionResetUsers!,
    }),
    shallow
  );
  const {
    isLoading,
    loadingLevel,
    isOpenSelectUserModal,
    setIsOpenSelectUserModal,
    setIsLoading,
    setLoadingLevel,
  } = useStore(
    "temp",
    (state) => ({
      isLoading: state.isLoading!,
      loadingLevel: state.loadingLevel!,
      setIsLoading: state.setIsLoading!,
      isOpenSelectUserModal: state.isOpenSelectUserModal!,
      setIsOpenSelectUserModal: state.setIsOpenSelectUserModal!,
      setLoadingLevel: state.setLoadingLevel!,
    }),
    shallow
  );

  const { metrics } = useStore(
    "analytics",
    (state) => ({
      metrics: state.metrics!,
    }),
    shallow
  );

  const { t } = useTranslation();
  const { width, ref } = useElementSize();
  const [rowsPerPage, setRowsPerPage] = useState("10");
  const [activePage, setActivePage] = useState(1);
  const [search, setSearch] = useState("");

  const updateUsers = useCallback(
    async () => {
      setIsLoading(true);
      setLoadingLevel(2);

      try {
        await actionGetUsers(+rowsPerPage, +activePage - 1, search);
      } finally {
        setIsLoading(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rowsPerPage, activePage, search]
  );

  useEffect(() => {
    if (isOpenSelectUserModal) {
      updateUsers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpenSelectUserModal, rowsPerPage, activePage]);

  const rows = orderBy(
    !!userList
      ? filteredUsers.filter((e) => userList.includes(e.id))
      : filteredUsers,
    "id"
  ).map((user: UserType, i: number) => (
    <tr key={`user-${i}`}>
      <td>
        <Group spacing={12}>
          <ActiveAvatar userId={user.id} size={32} radius="xl" />
          <Tooltip label={`${user.name} ${user.surname}`}>
            <Text
              sx={{
                maxWidth: 150,
                whiteSpace: "nowrap",
                textOverflow: "ellipsis",
                overflow: "hidden",
              }}
            >
              {user.name} {user.surname}
            </Text>
          </Tooltip>
        </Group>
      </td>
      <td>{user.email}</td>
      <td>{user.phone}</td>
      <td align="right" style={{ width: "50px !important" }}>
        {onesList.findIndex((e: any) => e === user.id) > -1 ? (
          <Button size="xs" color="red" onClick={() => handleRemove(user.id)}>
            {t("remove")}
          </Button>
        ) : (
          <Button size="xs" onClick={() => handleSelect(user.id)}>
            {t("add")}
          </Button>
        )}
      </td>
    </tr>
  ));

  return (
    <Modal
      opened={isOpenSelectUserModal}
      onClose={() => setIsOpenSelectUserModal(false)}
      centered
      title={t("selectUser.title")}
      transition={isOpenSelectUserModal ? "slide-down" : "slide-up"}
      size={700}
      zIndex={2000}
    >
      <LoadingOverlay
        visible={isLoading && loadingLevel === 2}
        loaderProps={{ size: "lg", variant: "bars" }}
        sx={(theme) => ({
          borderRadius: theme.radius.md,
        })}
      />
      <Stack ref={ref}>
        <Group
          position={width < 520 && width !== 0 ? "center" : "apart"}
          align="center"
        >
          <Group>
            <Text size="xs" weight={600} color="dimmed">
              {t("rowPerPage")}
            </Text>
            <Select
              size="xs"
              value={rowsPerPage}
              onChange={(value: string) => setRowsPerPage(value)}
              data={[
                { value: "2", label: "2" },
                { value: "10", label: "10" },
                { value: "25", label: "25" },
                { value: "50", label: "50" },
              ]}
              sx={{
                width: 100,
              }}
              transition="fade"
              transitionDuration={200}
            />
          </Group>
          <Group position="center">
            <Input
              size="xs"
              icon={<MagnifyingGlassIcon style={{ width: 16, height: 16 }} />}
              placeholder={t("search")}
              styles={{
                input: {
                  width: 140,
                },
              }}
              value={search}
              onChange={(e: any) => setSearch(e.target.value)}
              onKeyDown={getHotkeyHandler([["Enter", updateUsers]])}
            />
            <Pagination
              page={activePage}
              onChange={setActivePage}
              total={Math.ceil(metrics.users / +rowsPerPage) || 1}
              size="sm"
            />
          </Group>
        </Group>

        <ScrollArea
          sx={{
            maxWidth: width >= 660 ? "auto" : "calc(100vw - 72px)",
          }}
          offsetScrollbars
          styles={{
            viewport: {
              padding: 0,
            },
          }}
        >
          <Table sx={{ minWidth: 640 }}>
            <thead>
              <tr>
                <th>{t("selectUser.name")}</th>
                <th>{t("selectUser.email")}</th>
                <th>{t("selectUser.phone")}</th>
                <th></th>
              </tr>
            </thead>
            <tbody>{rows}</tbody>
          </Table>
        </ScrollArea>

        <Divider orientation="horizontal" />
        <Group position="right">
          <Group>
            <Button
              variant="light"
              color="gray"
              onClick={() => setIsOpenSelectUserModal(false)}
            >
              {t("close")}
            </Button>
          </Group>
        </Group>
      </Stack>
    </Modal>
  );
}
