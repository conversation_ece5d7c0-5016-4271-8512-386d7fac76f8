import React, { useEffect } from "react";
import ReactHowler from "react-howler";
import shallow from "zustand/shallow";
import { useStore } from "~utils/store";

const NotificationSound = () => {
  const { notification, setNotification } = useStore(
    "temp",
    (state) => ({
      notification: state.notification!,
      setNotification: state.setNotification!,
    }),
    shallow
  );

  return (
    <ReactHowler
      src="/notification_sound.mp3"
      onEnd={() =>
        setNotification({
          play: false,
        })
      }
      playing={notification.play}
    />
  );
};

export default NotificationSound;
