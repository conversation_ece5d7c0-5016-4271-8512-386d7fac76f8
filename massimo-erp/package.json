{"name": "massimo-erp", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/preset-react": "^7.18.6", "@emotion/react": "^11.10.5", "@emotion/server": "^11.10.0", "@fullcalendar/common": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/list": "^5.11.3", "@fullcalendar/react": "^5.11.2", "@fullcalendar/timegrid": "^5.11.3", "@heroicons/react": "^2.0.11", "@mantine/carousel": "^5.9.2", "@mantine/core": "^5.9.2", "@mantine/dates": "^5.9.2", "@mantine/dropzone": "^5.9.2", "@mantine/form": "^5.9.2", "@mantine/hooks": "^5.9.2", "@mantine/modals": "^5.9.2", "@mantine/next": "^5.5.0", "@mantine/notifications": "^5.9.2", "@mantine/nprogress": "^5.9.2", "@mantine/prism": "^5.9.2", "@mantine/rte": "^5.5.0", "@mantine/spotlight": "^5.9.2", "@mantine/tiptap": "^5.9.2", "@tabler/icons": "^1.115.0", "@tiptap/extension-highlight": "^2.0.0-beta.202", "@tiptap/extension-link": "^2.0.0-beta.205", "@tiptap/extension-subscript": "^2.0.0-beta.202", "@tiptap/extension-superscript": "^2.0.0-beta.202", "@tiptap/extension-text-align": "^2.0.0-beta.202", "@tiptap/extension-underline": "^2.0.0-beta.202", "@tiptap/react": "^2.0.0-beta.205", "@tiptap/starter-kit": "^2.0.0-beta.205", "@types/lodash": "^4.14.186", "@types/react-howler": "^5.2.0", "@types/react-input-mask": "^3.0.1", "add": "^2.0.6", "apexcharts": "^3.36.0", "axios": "^1.1.3", "cookies-next": "^2.1.1", "dayjs": "^1.11.7", "deepmerge": "^4.2.2", "embla-carousel-react": "^7.0.5", "eventemitter3": "^4.0.7", "immer": "^9.0.15", "lodash": "^4.17.21", "next": "^13.0.0", "next-i18next": "^12.1.0", "next-transpile-modules": "^9.0.0", "prosemirror-commands": "^1.5.0", "prosemirror-dropcursor": "^1.6.1", "prosemirror-gapcursor": "^1.3.1", "prosemirror-history": "^1.3.0", "prosemirror-keymap": "^1.2.0", "prosemirror-schema-list": "^1.2.2", "react": "^18.2.0", "react-apexcharts": "^1.4.0", "react-awesome-query-builder": "^5.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.2.0", "react-howler": "^5.2.0", "react-infinite-scroll-component": "^6.1.0", "react-input-mask": "^2.0.4", "react-masonry-css": "^1.0.16", "socket.io-client": "^4.5.3", "yarn": "^1.22.19", "zustand": "^4.1.1"}, "devDependencies": {"@types/node": "18.7.23", "@types/react": "18.0.21", "@types/react-beautiful-dnd": "^13.1.2", "@types/react-dom": "18.0.6", "eslint": "8.24.0", "eslint-config-next": "^13.0.0", "typescript": "4.8.4"}}