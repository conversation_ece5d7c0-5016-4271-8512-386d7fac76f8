html,
body {
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Oxygen,
    Ubuntu, Cantarell, Fira Sans, Droid Sans, Helvetica Neue, sans-serif;
  overflow: hidden;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }
  body {
    color: white;
    background: black;
  }
}

.my-masonry-grid {
  display: -webkit-box; /* Not needed if autoprefixing */
  display: -ms-flexbox; /* Not needed if autoprefixing */
  display: flex;
  margin-left: -30px; /* gutter size offset */
  width: auto;
}
.my-masonry-grid_column {
  padding-left: 30px; /* gutter size */
  background-clip: padding-box;
}

/* Style your items */
.my-masonry-grid_column > div {
  margin-bottom: 30px;
}

div.preview > h1,
div.preview > h2,
div.preview > h3,
div.preview > h4,
div.preview > h5,
div.preview > h6 {
  margin-top: 16px;
}

.mantine-SegmentedControl-control {
  border: none !important;
}

@media screen and (max-width: 992px) {
  .my-masonry-grid {
    width: calc(100vw - 2px);
  }

  .my-masonry-grid[class~="access"] {
    width: calc(100vw - 36px);
  }
}

.mantine-Modal-root {
  z-index: 510;
}
