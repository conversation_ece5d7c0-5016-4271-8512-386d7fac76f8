{"search": "Search", "explore": "Explore", "rowPerPage": "Per page", "noData": "No Data", "noOne": "No One", "on": "On", "off": "Off", "message": {"fail": {"title": "<PERSON><PERSON>", "message": "Message cannot be sent"}}, "task": {"project": {"fail": {"title": "Please select project", "message": "Please select any project to list tasks"}}}, "login": {"title": "<PERSON><PERSON>", "emailInput": {"label": "E-Mail", "placeholder": "<EMAIL>"}, "passwordInput": {"label": "Password", "placeholder": "Your password"}, "forgotPasswordText": "Forgot Password ?", "loginButton": "Sign in", "registerLinkText": "Do not have an account yet ?", "registerLink": "Create account", "fail": {"title": "Login Failed", "message": "Email or password may be incorrect"}, "success": {"title": "Login Successfully", "message": "Signed with your account"}}, "register": {"title": "Register", "nameInput": {"label": "Name", "placeholder": "Your name"}, "surnameInput": {"label": "Surname", "placeholder": "Your surname"}, "emailInput": {"label": "E-Mail", "placeholder": "<EMAIL>"}, "passwordInput": {"label": "Password", "placeholder": "Your password"}, "registerButton": "Sign up", "loginLinkText": "Do have an account ?", "loginLink": "Continue with your account", "fail": {"title": "Register Failed", "message": "An error occurred while creating your account"}, "success": {"title": "Register Successfully", "message": "Your account has been successfully created"}}, "navbar": {"Dashboard": "Dashboard", "Profile": "Profile", "CRM": "CRM", "Analytics": "Analytics", "Apps": "Apps", "Chat": "Cha<PERSON>", "Calendar": "Calendar", "Kanban": "Ka<PERSON><PERSON>", "Invoices": "Invoices", "Support": "Support", "FAQ": "FAQ", "Privacy": "Privacy", "Contact Us": "Contact", "Admin": "Admin", "Requests": "Requests", "Projects": "Projects", "Customers": "Customers", "Departments": "Departments", "Users": "Users", "Permissions": "Permissions", "Explore": "Explore", "Settings": "Settings"}, "breadcrumbs": {"dashboard": "Dashboard", "profile": "Profile", "crm": "CRM", "analytics": "Analytics", "apps": "Apps", "chat": "Cha<PERSON>", "calendar": "Calendar", "kanban": "Ka<PERSON><PERSON>", "invoices": "Invoices", "support": "Support", "faq": "FAQ", "privacy": "Privacy", "contactUs": "Contact", "admin": "Admin", "requests": "Requests", "projects": "Projects", "customers": "Customers", "departments": "Departments", "users": "Users", "permissions": "Permissions", "user": "User", "explore": "Explore", "settings": "Settings", "organization": "Organization", "notifications": "Notifications"}, "languageSelector": {"title": "Language"}, "notifications": {"title": "Notifications", "markAllAsRead": "Read All", "markOnlyTabs": "Read Here", "center": "Notification Center", "task": "From Task", "project": "From Project"}, "profileMenu": {"profile": "Profile", "chat": "Cha<PERSON>", "settings": "Settings", "logout": "Logout"}, "crm": {"overdue": "Overdue", "today": "Today", "nextWeek": "This Week", "nextMonth": "This Month", "project": "Project", "endDate": "End Date", "assignees": "Assignees", "tasksTable": {"status/title": "Status/Title", "tags": "Tags", "deadline": "Deadline", "noTask": "No Task Left"}, "meetingSchedule": "Meeting Schedule", "projectTimeline": {"title": "Project Timeline", "total": "Total", "taskCompleted": "Task Completed"}, "paymentHistory": {"title": "Payment History", "card": "Card", "date": "Date", "spendings": "Spendings"}}, "analytics": {"last": "Last", "month": "Month", "year": "Year", "visits": "Visits", "totalOrders": "Total Orders", "totalSales": "Total Sales", "totalImpressions": "Total Completed Tasks", "totalProfit": "Total Profit", "totalGrowth": "Total Growth", "totalRevenue": "Total Revenue", "socialNetwork": "Social Network Visits", "externalLinks": "External Links", "weekslyOverview": {"title": "Weeksly Overview", "message1": "Your sales performance is", "message2": "😎 better compared to last month", "details": "Details"}, "visitsByDay": {"title": "Visits By Day", "total": "Total", "mostVisitedDay": "Most Visited Day", "message": "Visits on Thursday"}, "OrganicSessions": "Metrics"}, "profile": {"Shared": "Shared", "ToDo": "To Do", "Completed": "Completed", "Gallery": "Gallery", "Timeline": "Timeline", "CompletedProjects": "Completed Projects", "Bills": "Bills", "balance": "Balance", "debtStatus": "Debt Status", "sendRequest": "Send Request", "empty": "Empty", "newPost": "New Post", "connections": "Connections", "tasks": {"Overdue": "Overdue", "Todo": "To Do", "Completed": "Completed", "project": "Project", "endDate": "End Date"}, "posts": "Posts"}, "post": {"attachment": "Attachment", "send": "Send", "comments": "Comments", "edit": "Edit", "delete": "Delete", "employeeList": "Employee List"}, "chat": {"messages": "Messages", "edit": "Edit", "reply": "Reply", "delete": "Delete", "scrollBottom": "<PERSON><PERSON> To <PERSON>", "image": "Image", "file": "File", "task": "Task", "notifications": "Notifications", "pinned": "Pinned", "members": "Members", "addMember": "Add Member", "attachments": "Attachments", "kick": "Kick", "you": "You", "search": "Search", "inputPlaceholder": "Your message"}, "calendar": {"viewAll": "View All", "createTask": "Create Task", "status": "Status"}, "faq": {"title": "Frequently Asked Questions"}, "contact": {"contactInformation": "Contact information", "Email": "E-Mail", "Phone": "Phone", "Address": "Address", "getInTouch": "Get in touch", "yourName": "Your Name", "yourEmail": "Your email", "subject": "Subject", "yourMessage": "Your message", "textareaPlaceholder": "Please include all relevant information", "sendMessage": "Send message"}, "404": {"title": "Nothing to see here", "message": "Page you are trying to open does not exist. You may have mistyped the address, or the page has been moved to another URL. If you think this is an error contact support.", "button": "Take me back to home page"}, "503": {"title": "All of our servers are busy", "message": "We cannot handle your request right now, please wait for a couple of minutes and refresh the page. Our team is already working on this issue.", "button": "Refresh the page"}, "addDepartment": "Add Department", "addUser": "Add User", "userTable": {"User": "User", "Phone": "Phone", "EMail": "E-Mail", "Departments": "Departments", "RestrictedRoles": "Restricted Roles", "DefaultRole": "Default Role", "Plan": "Plan", "Status": "Status", "edit": "Edit", "informations": "Informations", "delete": "Delete"}, "addCustomer": "Add Customer", "customerInvites": "Unverified Users", "customerTable": {"Company": "Company", "Phone": "Phone", "EMail": "E-Mail", "Authorized": "Authorized"}, "Offers": "Offers", "Tasks": "Tasks", "RecentActivities": "Recent Activities", "noActivityYet": "No Activity Yet", "addProject": "Add Project", "save": "Save", "back": "Back", "next": "Next", "cancel": "Cancel", "discard": "Discard", "delete": "Delete", "ok": "Ok", "customerModal": {"title": {"edit": "Edit Customer", "new": "New Customer"}, "locationInput": {"label": "Company Location", "placeholder": "Location"}, "companyInformations": "Company Informations", "shortName": {"label": "Short Name (e.g. ABC)", "placeholder": "(e.g. ABC)"}, "fullName": {"label": "Full Name (e.g. ABC Bilişim Ltd. Şti.)", "placeholder": "(e.g. ABC Bilişim Ltd. Şti.)"}, "phone": {"label": "Phone", "placeholder": "Company phone number"}, "email": {"label": "E-Mail", "placeholder": "Company E-Mail"}, "billingInformations": "Billing Informations", "taxDepartment": {"label": "Tax Department", "placeholder": "Tax Department"}, "taxNumber": {"label": "Tax Number", "placeholder": "Tax Number"}, "address": {"label": "Address", "placeholder": "Address"}, "customerpersons": "Customer Persons", "pleaseSelectLocation": "Please select location"}, "Users": "Users", "departmentModal": {"title": {"new": "New Department", "edit": "Edit Department"}, "color": "Department Color", "nameInput": {"label": "Name", "placeholder": "Department Name"}, "descriptionInput": {"label": "Description", "placeholder": "Department Description"}, "rolesPermissions": "Roles & Permissions", "newRole": {"label": "New Role Name", "placeholder": "Role Name"}, "weight": "Weight", "noRole": "No Roles Yet", "isVisible": "Is Visible"}, "roleAccesses": "Role Accesses", "role": "Role", "userModal": {"title": {"new": "New User", "edit": "Edit User"}, "avatarInput": {"label": "Avatar", "placeholder": "User Avatar"}, "contactInformations": "Contact informations", "nameInput": {"label": "Name", "placeholder": "User Name"}, "surnameInput": {"label": "Surname", "placeholder": "User surname"}, "phoneInput": {"label": "Phone", "placeholder": "User Phone"}, "emailInput": {"label": "E-Mail", "placeholder": "<EMAIL>"}, "passwordInput": {"label": "Password", "placeholder": "User password"}, "siteOptions": "Site Options", "defaultRole": {"label": "Default Role", "placeholder": "Default Role"}, "plan": {"label": "Label", "placeholder": "User Plan"}, "userInformations": "User Informations", "isBanned": "Is Banned", "isCustomer": "Is Customer", "isVerified": "Is Verified"}, "Department": "Department", "customerInvitesModal": {"title": "Unverified Users", "user": "User", "email": "E-Mail", "phone": "Phone", "actions": "Actions"}, "newTagModal": {"title": "New Tag", "label": "Tag Label", "color": "Tag Color"}, "newTask": {"errors": {"title": "Title must not be empty", "projectId": "Project must be selected", "priority": "Priority must be selected"}, "title": "New Task", "tabs": {"Details": "Details", "Description": "Description", "Files": "Files", "Steps": "Steps", "Recurrence": "Recurrence", "Other": "Other"}, "newSubtasks": "New Subtasks", "subtasks": "Subtasks", "project": {"label": "Project", "placeholder": "Select Project"}, "taskTitle": {"label": "Title", "placeholder": "Task Title"}, "tags": {"label": "Tags", "placeholder": "Select Tag"}, "priority": {"label": "Priority", "placeholder": "Priority"}, "dateRange": {"label": "Date Range", "placeholder": "Date Range"}, "timeRange": {"label": "Time Range"}, "filesTab": {"title": "Drag images here or click to select files", "text": "Attach as many files as you like, each file should not exceed 5mb"}, "onlyAssignees": "Only the assignees can see the task.", "onlyAssigneesMessage": "When you select this option, the task becomes private. Only assignees can see private tasks.", "customerSee": "The customer does not see", "customerSeeMessage": "When you select this option, this task cannot be seen by the customer.", "noSubtask": "No Subtasks Yet", "recurrenceTab": {"switchLabel": "Recurrence", "Every": "Every", "in": "in", "on": "on", "and": "and", "hours": "hours", "or": "OR", "every": "every", "Year": "Year", "Month": "Month", "Week": "Week", "Day": "Day", "Hour": "Hour", "Minute": "Minute", "January": "January", "February": "February", "March": "March", "April": "April", "May": "May", "June": "June", "July": "July", "August": "August", "September": "September", "October": "October", "November": "November", "December": "December", "Sunday": "Sunday", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday"}}, "offersModal": {"tasks": "Tasks", "approver": "Approver", "customer": "Customer", "status": "Status", "activePrice": "Active Prive", "description": "Description", "invoices": "Invoices", "titleLabel": "Project Title", "customerLabel": "Customer", "descriptionLabel": "Description", "dateRange": "Date Range", "timeRange": "Time Range", "departments": "Departments", "priority": "Priority", "allowedUsers": "Allowed Users", "restrictedUsers": "Restricted Users", "yourMessage": "Your message", "Attachment": "Attachment", "Revise": "Revise", "cancelOffer": "<PERSON><PERSON> Offer", "giveAQuote": "Give a Quote", "revised": "Revised", "revisedMessage": "This Offer is Revised", "complete": "Complete", "statuses": {"Request": "Request", "Offer": "Offer", "Completed": "Completed", "Canceled": "Canceled", "Revised": "Revised"}, "tasksTable": {"status": "Status", "title": "Title", "date": "Date"}}, "send": "Send", "giveAQuote": "Give a Quote", "price": "Price", "cancelOffer": "<PERSON><PERSON> Offer", "cancelReason": "Reason for cancellation", "cancelMessage": "This Offer <PERSON>ed", "reject": "Reject", "approve": "Approve", "budget": "Budget", "projectSelector": "Project Selector", "close": "Close", "sendRequestModal": {"title": "Send Request", "titleInput": {"label": "Title", "placeholder": "Request Title"}, "customer": "Customer", "departments": {"label": "Departments", "placeholder": "Assign to department"}, "price": "Price", "description": {"label": "Description", "placeholder": "Request Description"}, "allowedUsers": "Allowed Users", "restrictedUsers": "Restricted Users"}, "sharePostModal": {"postTitle": "Share Post", "storyTitle": "Share Story", "addTask": "Add Task", "description": "Description", "image": "Image"}, "share": "Share", "add": "Add", "remove": "Remove", "selectUser": {"title": "Select User", "name": "Name", "email": "E-Mail", "phone": "Phone"}, "taskViewModal": {"title": "Update Task", "yourComment": "Your Comment", "attachment": "Attachment", "image": "Image", "project": "Project", "tags": "Tags", "newTag": "Create Tag", "status": "Status", "priority": "Priority", "assignees": "Assignees", "contributors": "Contributors", "startDate": "Start Date", "endDate": "End Date", "timeRange": "Zaman Aralığı", "onlyAssignees": "Only those assignees", "hideFromCustomer": "Hide from customer", "description": "Description", "preview": "Preview"}, "Update": "Update", "footer": {"faq": "FAQ", "privacy": "Privacy", "contact": "Contact Us"}, "status": {"To Do": "To Do", "Active": "Active", "Completed": "Completed", "Problems": "Problems", "Archived": "Archived", "Empty": "Empty"}, "priority": {"Very Low": "Very Low", "Low": "Low", "Medium": "Medium", "High": "High", "Very High": "Very High"}, "registerCompleteText": "You have completed the registration process, we will inform you as soon as we validate you account. Thanks for your patience.", "userModalEmptyDepartmentText": "User does not exist in any department", "successful": {"title": "Successful", "userCreate": "User successfully created", "userUpdate": "User successfully updated", "usersUpdate": "User table successfully updated", "taskCreate": "Task successfully created", "taskUpdate": "Task successfully updated", "postCreate": "Post successfully created", "postUpdate": "Post successfully updated", "customerCreate": "Customer successfully created", "customerUpdate": "Customer successfully updated", "departmentCreate": "Department successfully created", "departmentUpdate": "Department successfully updated", "comingSoon": {"title": "Coming soon...", "message": "This feature is not defined"}}, "yes": "Yes", "no": "No", "confirmModal": {"title": "Please confirm your action", "message": "Are you sure you want to exit without saving?"}, "fullscreenModal": {"title": "Please enter full screen", "message": "Please click below to enter the full screen mode. This app doesn't work well without full screen on mobile"}, "userInformationsModal": {"title": "User Informations"}, "userStatus": {"online": "Online", "offline": "Offline", "busy": "Busy"}, "accessContent": {"defaultRoles": "Default Roles", "roleName": "Role Name"}, "nothingFound": "Nothing found", "spotlight": {"groups": {"dashboard": "Dashboard", "apps": "Apps", "support": "Support", "admin": "Admin"}, "items": [{"title": "CRM", "description": "Get to Dashboard CRM"}, {"title": "Analytics", "description": "Get to Dashboard Analytics"}, {"title": "Profile", "description": "Get to Profile"}, {"title": "Cha<PERSON>", "description": "Get to Chat App"}, {"title": "Calendar", "description": "Get to Calendar App"}, {"title": "Ka<PERSON><PERSON>", "description": "Get to Kanban App"}, {"title": "CRM Invoices", "description": "Get to CRM Invoices"}, {"title": "FAQ", "description": "Get to FAQ"}, {"title": "Privacy", "description": "Get to Privacy"}, {"title": "Contact", "description": "Get to Contact"}, {"title": "Requests", "description": "Get to Requests"}, {"title": "Departments", "description": "Get to Departments"}, {"title": "Users", "description": "Get to Users"}, {"title": "Customers", "description": "Get to Customers"}, {"title": "Projects", "description": "Get to Projects"}, {"title": "Roles & Permissions", "description": "Get to Roles & Permissions"}]}, "createInvoice": "Create Invoice", "convertInvoice": "Convert Invoice", "contactWithCustomer": "Contact With Customer", "noOneSelect": "No One Selected", "createProject": "Create Project", "userSettings": "User Settings", "taskAssignees": "Assignees", "organizations": "Organizations", "users": "Users", "connectionsList": "Connections", "organizationsList": "Organizations", "files": "Files", "images": "Images", "dataNotFound": "Data Not Found", "activeProjects": "Active Projects", "completedProjects": "Completed Projects", "requestInfo": "This action will automaticly create related project", "defaultRoles": {"admin": "Admin", "developer": "Developer", "staff": "Staff"}, "departmentList": "Departments", "deselect": "Deselect", "verifiedUsers": "Verified Users", "highPriorityTasks": "High Priority Tasks", "error": {"title": "Oops, there is an error!", "message": "If it doesn't get better after refreshing, let the developers know.", "try": "Try again"}, "notificationTitles": {"connections": "Connection request", "messages": "You have a new message", "tasks": "You have been assigned to a new task"}, "notificationTypes": {"connections": "Connections", "tasks": "Tasks", "chats": "Messages"}, "connectionRequests": "Connection Requests", "formErrors": {"name": "Invalid name", "surname": "Invalid surname", "email": "Invalid email", "password": {"empty": "Password cannot be empty", "short": "Password must be longer than 8 characters"}, "color": "Color cannot be empty", "label": "Label cannot be empty"}, "showMore": "Show more", "hide": "<PERSON>de", "Connected": "Connected", "Connect": "Connect", "RequestSent": "Request Sent", "updated": {"tasks": "Task updated.", "projects": "Project updated"}, "noNotification": "No Notification", "infiniteScroll": {"loader": "Loading", "pullDownRefresh": "Pull down to refresh", "releaseToRefresh": "Release to refresh"}, "likes": "<PERSON>s", "viewers": "Viewers", "profileSearch": "Profile Search", "none": "None", "noLikes": "No Likes", "noViews": "No Views", "largeFile": {"title": "The File Is Large", "message": "The file cannot be uploaded because it is larger than 10 MB."}, "stories": "Stories", "noPost": "No Post"}